package cn.mingyang.cloud.usercenter.dao.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 优惠卷信息
 */
@Data
public class OrgCoupon {

    /**
     * 主键，自增
     */
    private Integer id;

    /**
     * 优惠价格
     */
    private Float price;

    /**
     * 过期时间，默认为当前时间
     */
    private LocalDateTime endtime;

    /**
     * 描述信息
     */
    private String desc;

    /**
     * 是否生效，0-未生效，1-生效，默认为0
     */
    private Integer valid;

    /**
     * 应用ID，默认为'default'
     */
    private String appid;

    /**
     * 渠道，默认为'default'
     */
    private String df;

    /**
     * 优惠倒计时(分钟)，默认为120分钟
     */
    private Integer countdown;

    /**
     * 是否循环发放，0-不循环，1-循环，默认为1
     */
    private Integer isCycle;

    /**
     * 适用商品ID列表
     */
    private String applyProductids;

    /**
     * 商品标识
     */
    private String productName;
}
