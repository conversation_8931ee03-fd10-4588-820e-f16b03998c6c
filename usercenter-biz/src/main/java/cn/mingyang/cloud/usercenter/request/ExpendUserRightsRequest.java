package cn.mingyang.cloud.usercenter.request;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 权益扣除请求
 */
@Data
public class ExpendUserRightsRequest {

    /**
     * 功能id
     */
    @NotNull(message = "功能id：functionid不能为空")
    private Integer functionid;

    /**
     * 消耗数量
     */
    @NotNull(message = "消耗数量：quantity不能为空，校验则传0")
    private Integer quantity;

    /**
     * 老权益扣减数量
     */
    private Integer oldQuantity;

    /**
     * 校验会员等级，默认校验权益； 1： 校验会员等级
     */
    private Integer checkvip;
}
