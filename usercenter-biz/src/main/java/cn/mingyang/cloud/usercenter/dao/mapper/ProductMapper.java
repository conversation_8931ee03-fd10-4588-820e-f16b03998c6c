package cn.mingyang.cloud.usercenter.dao.mapper;


import cn.mingyang.cloud.usercenter.dao.domain.ProductDoMain;
import cn.mingyang.cloud.usercenter.dao.entity.ProductInfo;
import cn.mingyang.cloud.usercenter.dao.entity.SellPointInfo;
import cn.mingyang.cloud.usercenter.request.GetProductInfoRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权益查询Mapper
 */
@Mapper
public interface ProductMapper {


    /**
     * 根据应用ID查询产品信息
     *
     * @param appid 产品名称
     * @return 订单列表
     */
    List<ProductInfo> getProductInfoByAppid(@Param("appid") String appid,
                                            @Param("osid") String osid,
                                            @Param("product") GetProductInfoRequest productRequest);

    /**
     * 根据产品ID查询卖点信息
     *
     * @param productId 产品ID
     * @return 卖点信息
     */
    List<SellPointInfo> getSellPointByProductId(@Param("productid") int productId);

    /**
     * 根据产品ID查询VIP信息
     *
     * @param productId 产品ID
     * @return VIP信息
     */
    ProductDoMain getProductType(@Param("productid") int productId);

    /**
     * 根据应用ID查询卖点信息
     *
     * @param appid 应用ID
     * @param osid  系统ID
     * @return 卖点信息
     */
    List<SellPointInfo> getSellPointInfoByAppid(@Param("appid") String appid, @Param("osid") String osid);

    /**
     * 获取免费权益
     *
     * @param appIds 应用ID列表
     * @return 卖点信息
     */
    List<SellPointInfo> getGiftSellPoint(@Param("appIds") List<String> appIds);


    /**
     * 查询新的产品信息
     *
     * @param product 产品信息
     * @return 产品列表
     */
    List<ProductDoMain> queryNewProduct(@Param("product") ProductDoMain product);


    /**
     * 根据查询老商品表
     *
     * @param productName 产品名称
     * @return 老商品信息
     */
    ProductDoMain queryOldProductByName(@Param("productName") String productName);


    /**
     * 根据别名查询到对应的商品
     *
     * @param alias 产品别名
     * @return 商品信息
     */
    List<ProductDoMain> getProductByAlias(@Param("alias") String alias);

}
