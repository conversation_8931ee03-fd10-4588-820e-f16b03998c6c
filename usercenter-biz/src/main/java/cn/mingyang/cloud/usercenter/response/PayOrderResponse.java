package cn.mingyang.cloud.usercenter.response;

import lombok.Data;

import java.util.Map;

/**
 * 支付下单返回值
 */
@Data
public class PayOrderResponse {

    /**
     * 订单编号
     */
    private String outtradeno;

    /**
     * 支付AppID(废弃)
     */
    private String payAppId;

    /**
     * 支付平台应用ID
     */
    private String platformappid;

    /**
     * 商户号(废弃)
     */
    private String mchId;

    /**
     * 商户号
     */
    private String platformmchid;

    /**
     * 随机字符串(废弃)
     */
    private String nonceStr;

    /**
     * 随机字符串
     */
    private String randomstr;

    /**
     * 签名
     */
    private String sign;

    /**
     * 时间戳
     */
    private String timestamp;


    /**
     * 支付差异化返回值
     */
    private Map<String, String> paydata;
}
