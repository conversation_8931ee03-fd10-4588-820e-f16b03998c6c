package cn.mingyang.cloud.usercenter.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.center.common.util.AesUtils;
import cn.mingyang.cloud.center.common.util.IpUtils;
import cn.mingyang.cloud.center.common.util.MapUtils;
import cn.mingyang.cloud.center.common.util.XmlUtils;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.center.common.comEnum.BaseErrorCodeEnum;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import cn.mingyang.cloud.usercenter.comEnum.PayType;
import cn.mingyang.cloud.usercenter.comEnum.PayTypeDetail;
import cn.mingyang.cloud.usercenter.dao.domain.PayAmount;
import cn.mingyang.cloud.usercenter.dao.entity.OrgUserOrder;
import cn.mingyang.cloud.usercenter.dao.entity.PayConfig;
import cn.mingyang.cloud.usercenter.dao.mapper.PayMapper;
import cn.mingyang.cloud.usercenter.request.PayOrderRequest;
import cn.mingyang.cloud.usercenter.response.PayOrderResponse;
import com.aliyun.openservices.shade.org.apache.commons.lang3.RandomStringUtils;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.app.AppService;
import com.wechat.pay.java.service.payments.app.model.PrepayResponse;
import com.wechat.pay.java.service.payments.h5.H5Service;
import com.wechat.pay.java.service.payments.h5.model.H5Info;
import com.wechat.pay.java.service.payments.h5.model.SceneInfo;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.jsapi.model.Payer;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.Amount;
import com.wechat.pay.java.service.payments.nativepay.model.PrepayRequest;
import com.wechat.pay.java.service.refund.RefundService;
import com.wechat.pay.java.service.refund.model.AmountReq;
import com.wechat.pay.java.service.refund.model.CreateRequest;
import com.wechat.pay.java.service.refund.model.Refund;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付Service
 */
@Slf4j
@Service
public class WxPayService {

    private final PayMapper payMapper;
    private final CloseableHttpClient httpClient;
    private final OrderService orderService;

    public WxPayService(PayMapper payMapper, CloseableHttpClient httpClient, OrderService orderService) {
        this.payMapper = payMapper;
        this.httpClient = httpClient;
        this.orderService = orderService;
    }

    /**
     * 微信统一下单
     */
    @DS("#cmd.ds")
    @Transactional
    public CommonResult<?> wxUnifiedOrder(CmdRequest cmd, ComRequest comRequest, PayOrderRequest payRequest) {
        try {
            PayTypeDetail payTypeDetail = PayTypeDetail.fromCode(payRequest.getPaycode());
            if (null == payTypeDetail) {
                return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "没有查到对应的支付方式");
            }
            //应用ID
            String appId = cmd.getAppId();
            PayConfig payConfig;
            //查询支付配置，初始化Config
            payConfig = payMapper.getPayChannelConfig(appId, PayType.WX.getCode());
            if (null == payConfig) {
                payConfig = payMapper.getPayChannelConfig(Constant.DEFAULT, PayType.WX.getCode());
                if (null == payConfig) {
                    return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "没有找到当前应用: " + appId + " 的微信支付渠道配置");
                }
            }
            // 创建支付订单

            OrgUserOrder orgUserOrder = orderService.createPayOrder(payRequest, cmd, comRequest, PayType.WX.getIndex(), payConfig.getMchid());
            log.info("微信统一下单, 订单信息： {}", JSONUtil.toJsonStr(orgUserOrder));

            // 支付金额
            PayAmount amount = new PayAmount();
            amount.setTotalFen(BigDecimal.valueOf(orgUserOrder.getFee() * 100).intValue());
            amount.setCurrency("CNY");

            //跳转支付
            switch (payTypeDetail) {
                case WX_BAR:
                    return this.WxBarPay(payConfig, amount, orgUserOrder, payRequest);
                case WX_JSAPI:
                case WX_LITE:
                    return "v2".equals(payConfig.getVersion()) ?
                            this.WxJsapiV2(payConfig, amount, orgUserOrder, payRequest) :
                            this.WxJsapi(payConfig, amount, orgUserOrder, payRequest);
                case WX_APP:
                    return "v2".equals(payConfig.getVersion()) ?
                            this.WxAppV2(payConfig, amount, orgUserOrder) :
                            this.WxApp(amount, payConfig, orgUserOrder);
                case WX_H5:
                    return "v2".equals(payConfig.getVersion()) ?
                            this.WxH5V2(payConfig, amount, orgUserOrder) :
                            this.WxH5(payConfig, amount, orgUserOrder);
                case WX_NATIVE:
                    return "v2".equals(payConfig.getVersion()) ?
                            this.WxNativeV2(payConfig, amount, orgUserOrder) :
                            this.WxNative(amount, payConfig, orgUserOrder);
                case WX_REFUNDS:
                    return this.WxRefunds(amount, payConfig, orgUserOrder);
                default:
                    return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "不支持的支付方式");
            }
        } catch (Exception e) {
            log.error("微信下单报错， Exception： ", e);
            throw new RuntimeException(e);
        }

    }

    /**
     * 获取微信支付配置
     */
    private Config getWxConfig(PayConfig payConfig) {
        JSONObject wxConfig = JSONUtil.parseObj(payConfig.getConfigvalues());
        return new RSAAutoCertificateConfig.Builder()
                .merchantId(payConfig.getMchid())
                .privateKeyFromPath(wxConfig.getStr("privateKeyPath"))
                .merchantSerialNumber(wxConfig.getStr("merchantSerialNumber"))
                .apiV3Key(payConfig.getApikey())
                .build();
    }

    /**
     * 处理微信支付V2版本响应
     */
    private CommonResult<?> handleWxPayV2Response(String message, String outTradeNo, Map<String, String> responseMap) {
        if (responseMap == null || responseMap.isEmpty()) {
            return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "微信支付响应为空");
        }

        // 检查通信标识
        if (!"SUCCESS".equals(responseMap.get("return_code"))) {
            String errorMsg = responseMap.get("return_msg");
            log.error("[{}] 微信支付通信失败, outTradeNo: {}, errorMsg: {}", message, outTradeNo, errorMsg);
            return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "微信支付通信失败：" + errorMsg);
        }

        // 检查业务结果
        if (!"SUCCESS".equals(responseMap.get("result_code"))) {
            String errorCode = responseMap.get("err_code");
            String errorMsg = responseMap.get("err_code_des");
            log.error("[{}] 微信支付业务失败, outTradeNo: {}, errorCode: {}, errorMsg: {}",
                    message, outTradeNo, errorCode, errorMsg);
            return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(),
                    String.format("微信支付业务失败[%s]：%s", errorCode, errorMsg));
        }

        log.info("[{}] 微信支付成功, outTradeNo: {}", message, outTradeNo);
        return null; // 返回null表示成功，需要调用方处理成功响应
    }

    /**
     * 微信条码支付（V2版本）
     */
    public CommonResult<?> WxBarPay(PayConfig channelConfig, PayAmount amount, OrgUserOrder orgUserOrder, PayOrderRequest payOrderRequest) {
        try {
            if (StringUtils.isEmpty(payOrderRequest.getAuthcode())) {
                return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "微信条码支付，用户条码不能为空");
            }

            log.info("[WxBarPay] 开始微信条码支付, 请求参数: {}", JSONUtil.toJsonStr(payOrderRequest));
            // 1. 构造请求参数
            Map<String, String> params = new HashMap<>();
            params.put("appid", channelConfig.getPlatformappid());
            params.put("mch_id", channelConfig.getMchid());
            params.put("nonce_str", RandomStringUtils.randomAlphanumeric(32));
            params.put("body", "微信条码支付");
            params.put("out_trade_no", orgUserOrder.getOutTradeNo());
            params.put("total_fee", String.valueOf(amount.getTotalFen()));
            params.put("auth_code", payOrderRequest.getAuthcode());
            params.put("sign_type", "MD5");

            // 2. 生成签名
            String sign = AesUtils.generateSign(params, channelConfig.getApikey());
            params.put("sign", sign);

            // 3. 转换为XML
            String xmlRequest = XmlUtils.mapToXml(params);

            // 4. 发送HTTP请求
            HttpPost httpPost = new HttpPost("https://api.mch.weixin.qq.com/pay/micropay");
            httpPost.setEntity(new StringEntity(xmlRequest, "UTF-8"));
            httpPost.setHeader("Content-Type", "application/xml");
            String xmlResponse = httpClient.execute(httpPost, response -> EntityUtils.toString(response.getEntity(), "UTF-8"));

            // 5. 解析XML响应
            Map<String, String> responseMap = XmlUtils.parseXmlResponse(xmlResponse);
            CommonResult<?> errorResult = handleWxPayV2Response("WxBarPay", orgUserOrder.getOutTradeNo(), responseMap);
            if (errorResult != null) {
                return errorResult;
            }

            PayOrderResponse payOrderResponse = new PayOrderResponse();
            payOrderResponse.setOuttradeno(orgUserOrder.getOutTradeNo());
            payOrderResponse.setPaydata(responseMap);
            return CommonResult.success(payOrderResponse);
        } catch (Exception e) {
            log.error("[WxBarPay] 微信条码支付异常", e);
            return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "微信条码支付失败：" + e.getMessage());
        }
    }

    /**
     * 微信公众号、小程序支付（V2版本）
     */
    public CommonResult<?> WxJsapiV2(PayConfig payConfig, PayAmount amount, OrgUserOrder orgUserOrder, PayOrderRequest payOrderRequest) {
        if (StringUtils.isEmpty(payOrderRequest.getOpenid())) {
            return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "微信公众号、小程序支付支付失败，缺少参数OpenId");
        }
        Map<String, String> extraParams = new HashMap<>();
        extraParams.put("openid", payOrderRequest.getOpenid());
        return handleWxPayV2(payConfig, amount, orgUserOrder, "JSAPI",
                "微信公众号、小程序支付：" + orgUserOrder.getTitle(), extraParams);
    }

    /**
     * 微信APP支付（V2版本）
     */
    public CommonResult<?> WxAppV2(PayConfig payConfig, PayAmount amount, OrgUserOrder orgUserOrder) {
        return handleWxPayV2(payConfig, amount, orgUserOrder, "APP",
                "微信APP支付：" + orgUserOrder.getTitle(), null);
    }

    /**
     * 微信H5支付（V2版本）
     */
    public CommonResult<?> WxH5V2(PayConfig payConfig, PayAmount amount, OrgUserOrder orgUserOrder) {
        //获取H5配置信息
        JSONObject wxConfig = JSONUtil.parseObj(payConfig.getConfigvalues());
        Map<String, String> extraParams = new HashMap<>();
        extraParams.put("scene_info", wxConfig.getStr("scene_info"));
        return handleWxPayV2(payConfig, amount, orgUserOrder, "MWEB",
                "H5商品支付：" + orgUserOrder.getTitle(), extraParams);
    }

    /**
     * 微信扫码支付（V2版本）
     */
    public CommonResult<?> WxNativeV2(PayConfig payConfig, PayAmount amount, OrgUserOrder orgUserOrder) {
        return handleWxPayV2(payConfig, amount, orgUserOrder, "NATIVE",
                "微信扫码支付：" + orgUserOrder.getTitle(), null);
    }

    /**
     * 处理微信支付V2版本统一下单
     */
    private CommonResult<?> handleWxPayV2(PayConfig payConfig, PayAmount amount, OrgUserOrder orgUserOrder,
                                          String tradeType, String productName, Map<String, String> extraParams) {
        try {
            log.info("[WxPayV2] 开始微信{}支付, outTradeNo: {}, productName: {}", tradeType, orgUserOrder.getOutTradeNo(), productName);

            // 1. 构造基础请求参数
            Map<String, String> params = new HashMap<>();
            params.put("appid", payConfig.getPlatformappid());
            params.put("mch_id", payConfig.getMchid());
            params.put("nonce_str", RandomStringUtils.randomAlphanumeric(32));
            params.put("body", productName);
            params.put("out_trade_no", orgUserOrder.getOutTradeNo());
            params.put("total_fee", amount.getTotalFen().toString());
            params.put("fee_type", amount.getCurrency());
            params.put("spbill_create_ip", IpUtils.getLocalIpAddress());
            params.put("notify_url", payConfig.getNotifyurl());
            params.put("trade_type", tradeType);
            params.put("sign_type", "MD5");

            // 2. 添加额外参数
            if (extraParams != null) {
                params.putAll(extraParams);
            }

            // 3. 生成签名
            String sign = AesUtils.generateSign(params, payConfig.getApikey());
            params.put("sign", sign);

            // 4. 转换为XML
            String xmlRequest = XmlUtils.mapToXml(params);
            // 5. 发送HTTP请求
            HttpPost httpPost = new HttpPost("https://api.mch.weixin.qq.com/pay/unifiedorder");
            httpPost.setEntity(new StringEntity(xmlRequest, "UTF-8"));
            httpPost.setHeader("Content-Type", "application/xml");
            String xmlResponse = httpClient.execute(httpPost, response -> EntityUtils.toString(response.getEntity(), "UTF-8"));

            // 6. 解析XML响应
            Map<String, String> responseMap = XmlUtils.parseXmlResponse(xmlResponse);
            CommonResult<?> errorResult = handleWxPayV2Response("WxPayV2", orgUserOrder.getOutTradeNo(), responseMap);
            if (errorResult != null) {
                return errorResult;
            }
            // 返回信息
            PayOrderResponse payOrderResponse = new PayOrderResponse();
            payOrderResponse.setOuttradeno(orgUserOrder.getOutTradeNo());
            payOrderResponse.setPayAppId(payConfig.getPlatformappid());
            payOrderResponse.setPlatformappid(payConfig.getPlatformappid());
            payOrderResponse.setMchId(payConfig.getMchid());
            payOrderResponse.setPlatformmchid(payConfig.getMchid());
            payOrderResponse.setTimestamp(orgUserOrder.getTime().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond() + "");
            Map<String, String> payData = new HashMap<>();
            // 根据不同的交易类型返回不同的支付数据
            switch (tradeType) {
                case "JSAPI":
                    payData.put("prepayId", "prepay_id=" + responseMap.get("prepay_id"));
                    payData.put("signType", params.get("sign_type"));
                    this.getPaySingOfJspi(payOrderResponse, payConfig, "prepay_id=" + responseMap.get("prepay_id"));
                    break;
                case "APP":
                    payData.put("prepayId", responseMap.get("prepay_id"));
                    this.getPaySing(payOrderResponse, payConfig, responseMap.get("prepay_id"));
                    break;
                case "MWEB":
                    payData.put("h5Url", responseMap.get("mweb_url"));
                    break;
                case "NATIVE":
                    payData.put("codeUrl", responseMap.get("code_url"));
                    break;
            }
            payOrderResponse.setPaydata(payData);
            return CommonResult.success(payOrderResponse);
        } catch (Exception e) {
            log.error("[WxPayV2] 微信{}支付异常, outTradeNo: {}", tradeType, orgUserOrder.getOutTradeNo(), e);
            return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), tradeType + "支付失败：" + e.getMessage());
        }
    }

    /**
     * 返回给客户端
     *
     * @param payOrderResponse 返回信息
     * @param payConfig        支付配置
     * @param prepayid         预支付ID
     */
    private void getPaySing(PayOrderResponse payOrderResponse, PayConfig payConfig, String prepayid) {
        Map<String, String> params = new HashMap<>();
        params.put("appid", payConfig.getPlatformappid());
        params.put("partnerid", payConfig.getMchid());
        params.put("prepayid", prepayid);
        String noncestr = RandomStringUtils.randomAlphanumeric(32);
        params.put("noncestr", noncestr);
        payOrderResponse.setNonceStr(noncestr);
        payOrderResponse.setRandomstr(noncestr);
        params.put("package", "Sign=WXPay");
        params.put("timestamp", payOrderResponse.getTimestamp());
        //算签
        String sign = AesUtils.generateSign(params, payConfig.getApikey());
        payOrderResponse.setSign(sign);
    }

    /**
     * 微信小程序算签
     *
     * @param payOrderResponse 返回信息
     * @param payConfig        支付配置
     * @param prepayid         预支付ID
     */
    private void getPaySingOfJspi(PayOrderResponse payOrderResponse, PayConfig payConfig, String prepayid) {
        Map<String, String> params = new HashMap<>();
        params.put("appId", payConfig.getPlatformappid());
        String noncestr = RandomStringUtils.randomAlphanumeric(32);
        params.put("nonceStr", noncestr);
        payOrderResponse.setNonceStr(noncestr);
        payOrderResponse.setRandomstr(noncestr);
        params.put("package", prepayid);
        params.put("signType", "MD5");
        params.put("timeStamp", payOrderResponse.getTimestamp());
        //算签
        String sign = AesUtils.generateSign(params, payConfig.getApikey());
        payOrderResponse.setSign(sign);
    }


    /**
     * 微信公众号、小程序支付（V3版本）
     */
    public CommonResult<?> WxJsapi(PayConfig payConfig, PayAmount amount, OrgUserOrder orgUserOrder, PayOrderRequest payOrderRequest) {
        try {
            if (StringUtils.isEmpty(payOrderRequest.getOpenid())) {
                return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "微信公众号、小程序支付支付失败，缺少参数OpenId");
            }

            // V3版本支付配置
            Config config = this.getWxConfig(payConfig);

            log.info("[WxJsapi] 开始微信公众号、小程序支付, outTradeNo: {}, productName: {}", orgUserOrder.getOutTradeNo(), orgUserOrder.getProductName());
            JsapiService service = new JsapiService.Builder().config(config).build();
            com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest request = new com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest();
            request.setAppid(payConfig.getPlatformappid());
            request.setMchid(payConfig.getMchid());
            request.setDescription("微信公共号、小程序支付 : " + orgUserOrder.getTitle());
            request.setNotifyUrl(payConfig.getNotifyurl());

            //支付金额
            com.wechat.pay.java.service.payments.jsapi.model.Amount payAmount = new com.wechat.pay.java.service.payments.jsapi.model.Amount();
            payAmount.setTotal(amount.getTotalFen());
            payAmount.setCurrency(amount.getCurrency());
            request.setAmount(payAmount);

            //支付者信息
            com.wechat.pay.java.service.payments.jsapi.model.Payer payer = new Payer();
            payer.setOpenid(payOrderRequest.getOpenid());
            request.setPayer(payer);
            request.setOutTradeNo(orgUserOrder.getOutTradeNo());

            com.wechat.pay.java.service.payments.jsapi.model.PrepayResponse response = service.prepay(request);
            log.info("[WxJsapi] 微信公众号、小程序支付下单成功, prepayId: {}", response.getPrepayId());
            PayOrderResponse payOrderResponse = new PayOrderResponse();
            payOrderResponse.setOuttradeno(request.getOutTradeNo());
            Map<String, String> payData = new HashMap<>();
            payData.put("prepayId", response.getPrepayId());
            payOrderResponse.setPaydata(payData);
            return CommonResult.success(payOrderResponse);
        } catch (Exception e) {
            log.error("[WxJsapi] 微信公众号、小程序支付异常, outTradeNo: {}", orgUserOrder.getOutTradeNo(), e);
            return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "公众号、小程序支付失败：" + e.getMessage());
        }
    }

    /**
     * 微信APP支付（V3版本）
     */
    public CommonResult<?> WxApp(PayAmount payAmount, PayConfig payConfig, OrgUserOrder orgUserOrder) {
        try {
            // V3版本支付配置
            Config config = this.getWxConfig(payConfig);

            log.info("[WxApp] 开始微信APP支付, outTradeNo: {}, productName: {}", orgUserOrder.getOutTradeNo(), orgUserOrder.getProductName());
            AppService service = new AppService.Builder().config(config).build();
            com.wechat.pay.java.service.payments.app.model.PrepayRequest request = new com.wechat.pay.java.service.payments.app.model.PrepayRequest();

            //支付金额
            com.wechat.pay.java.service.payments.app.model.Amount amount = new com.wechat.pay.java.service.payments.app.model.Amount();
            amount.setTotal(payAmount.getTotalFen());
            amount.setCurrency(payAmount.getCurrency());
            request.setAmount(amount);

            request.setAppid(payConfig.getPlatformappid());
            request.setMchid(payConfig.getMchid());
            request.setDescription("微信APP支付： " + orgUserOrder.getTitle());
            request.setNotifyUrl(payConfig.getNotifyurl());
            request.setOutTradeNo(orgUserOrder.getOutTradeNo());

            PrepayResponse response = service.prepay(request);
            log.info("[WxApp] 微信APP支付下单成功, prepayId: {}", response.getPrepayId());
            PayOrderResponse payOrderResponse = new PayOrderResponse();
            payOrderResponse.setOuttradeno(orgUserOrder.getOutTradeNo());
            payOrderResponse.setPayAppId(payConfig.getPlatformappid());
            payOrderResponse.setPlatformappid(payConfig.getPlatformappid());
            payOrderResponse.setMchId(payConfig.getMchid());
            payOrderResponse.setPlatformmchid(payConfig.getMchid());
            payOrderResponse.setTimestamp(orgUserOrder.getTime().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond() + "");
            payOrderResponse.setOuttradeno(request.getOutTradeNo());
            Map<String, String> payData = new HashMap<>();
            payData.put("prepayId", response.getPrepayId());
            payOrderResponse.setPaydata(payData);
            return CommonResult.success(payOrderResponse);
        } catch (Exception e) {
            log.error("[WxApp] 微信APP支付异常, outTradeNo: {}", orgUserOrder.getOutTradeNo(), e);
            return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "APP支付失败：" + e.getMessage());
        }
    }

    /**
     * 微信H5支付（V3版本）
     */
    public CommonResult<?> WxH5(PayConfig payConfig, PayAmount amount, OrgUserOrder orgUserOrder) {
        try {
            // V3版本支付配置
            Config config = this.getWxConfig(payConfig);


            log.info("[WxH5] 开始微信H5支付, outTradeNo: {}, productName: {}", orgUserOrder.getOutTradeNo(), orgUserOrder.getProductName());
            H5Service service = new H5Service.Builder().config(config).build();
            com.wechat.pay.java.service.payments.h5.model.PrepayRequest request = new com.wechat.pay.java.service.payments.h5.model.PrepayRequest();
            request.setAppid(payConfig.getPlatformappid());
            request.setMchid(payConfig.getMchid());
            request.setDescription("H5商品支付：" + orgUserOrder.getTitle());
            //支付金额
            com.wechat.pay.java.service.payments.h5.model.Amount payAmount = new com.wechat.pay.java.service.payments.h5.model.Amount();
            payAmount.setTotal(amount.getTotalFen());
            payAmount.setCurrency(amount.getCurrency());
            request.setAmount(payAmount);

            //使用场景
            SceneInfo sceneInfo = new SceneInfo();
            sceneInfo.setPayerClientIp(IpUtils.getLocalIpAddress());
            H5Info h5Info = new H5Info();
            h5Info.setType("Wap");
            sceneInfo.setH5Info(h5Info);
            request.setSceneInfo(sceneInfo);

            request.setNotifyUrl(payConfig.getNotifyurl());
            request.setOutTradeNo(orgUserOrder.getOutTradeNo());

            com.wechat.pay.java.service.payments.h5.model.PrepayResponse response = service.prepay(request);
            log.info("[WxH5] 微信H5支付下单成功, h5Url: {}", response.getH5Url());
            PayOrderResponse payOrderResponse = new PayOrderResponse();
            payOrderResponse.setOuttradeno(request.getOutTradeNo());
            Map<String, String> payData = new HashMap<>();
            payData.put("h5Url", response.getH5Url());
            payOrderResponse.setPaydata(payData);
            return CommonResult.success(payOrderResponse);
        } catch (Exception e) {
            log.error("[WxH5] 微信H5支付异常, outTradeNo: {}", orgUserOrder.getOutTradeNo(), e);
            return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "H5支付失败：" + e.getMessage());
        }
    }

    /**
     * 微信扫码支付（V3版本）
     */
    public CommonResult<?> WxNative(PayAmount payAmount, PayConfig payConfig, OrgUserOrder orgUserOrder) {
        try {
            // V3版本支付配置
            Config config = this.getWxConfig(payConfig);


            log.info("[WxNative] 开始微信扫码支付, outTradeNo: {}, productName: {}", orgUserOrder.getOutTradeNo(), orgUserOrder.getProductName());
            NativePayService service = new NativePayService.Builder().config(config).build();
            PrepayRequest request = new PrepayRequest();

            //支付金额
            Amount amount = new Amount();
            amount.setTotal(payAmount.getTotalFen());
            amount.setCurrency(payAmount.getCurrency());
            request.setAmount(amount);

            request.setAppid(payConfig.getPlatformappid());
            request.setMchid(payConfig.getMchid());
            request.setDescription("微信扫码支付： " + orgUserOrder.getTitle());
            request.setNotifyUrl(payConfig.getNotifyurl());
            request.setOutTradeNo(orgUserOrder.getOutTradeNo());

            com.wechat.pay.java.service.payments.nativepay.model.PrepayResponse response = service.prepay(request);
            log.info("[WxNative] 微信扫码支付下单成功, codeUrl: {}", response.getCodeUrl());
            PayOrderResponse payOrderResponse = new PayOrderResponse();
            payOrderResponse.setOuttradeno(request.getOutTradeNo());
            Map<String, String> payData = new HashMap<>();
            payData.put("codeUrl", response.getCodeUrl());
            payOrderResponse.setPaydata(payData);
            return CommonResult.success(payOrderResponse);
        } catch (Exception e) {
            log.error("[WxNative] 微信扫码支付异常, outTradeNo: {}", orgUserOrder.getOutTradeNo(), e);
            return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "扫码支付失败：" + e.getMessage());
        }
    }

    /**
     * 微信订单退款（V3版本）
     */
    public CommonResult<?> WxRefunds(PayAmount payAmount, PayConfig payConfig, OrgUserOrder orgUserOrder) {
        try {
            if (null == payAmount.getRefundFen()) {
                return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "微信订单退款失败, 退款金额不能为空");
            }


            // V3版本支付配置
            Config config = this.getWxConfig(payConfig);


            log.info("[WxRefunds] 微信订单退款, outTradeNo: {}, productName: {}", orgUserOrder.getOutTradeNo(), orgUserOrder.getProductName());
            RefundService service = new RefundService.Builder().config(config).build();
            CreateRequest request = new CreateRequest();

            //退款金额
            AmountReq amount = new AmountReq();
            amount.setTotal((long) payAmount.getTotalFen());
            amount.setRefund((long) payAmount.getRefundFen());
            amount.setCurrency(payAmount.getCurrency());
            request.setAmount(amount);
            request.setNotifyUrl(payConfig.getNotifyurl());
            request.setOutTradeNo(orgUserOrder.getOutTradeNo());
            request.setOutRefundNo(orgUserOrder.getOutTradeNo());

            //调用退款
            Refund refund = service.create(request);
            log.info("[WxRefunds] 微信订单退款下单成功, refund: {}", JSONUtil.toJsonStr(refund));
            PayOrderResponse payOrderResponse = new PayOrderResponse();
            payOrderResponse.setOuttradeno(request.getOutTradeNo());
            payOrderResponse.setPaydata(MapUtils.convertToMap(refund));
            return CommonResult.success(payOrderResponse);
        } catch (Exception e) {
            log.error("[WxRefunds] 微信订单退款异常, outTradeNo: {}", orgUserOrder.getOutTradeNo(), e);
            return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "微信订单退款失败：" + e.getMessage());
        }
    }


    /**
     * 通过商家AppID获取支付配置
     */
    public PayConfig getPayChannelConfig(String appId, String payChannel) {
        return payMapper.getPayChannelConfigByPayChannel(appId, payChannel);
    }

    /**
     * 微信支付V2回调验签
     */
    @DS("#pid")
    public boolean verifyWxPayNotifyV2(String pid, Map<String, String> params) {
        String appid = params.get("appid");
        String mchId = params.get("mch_id");
        PayConfig payConfig = getPayChannelConfig(appid, PayType.WX.getCode());
        if (payConfig == null) {
            log.error("[微信支付回调V2] 未找到支付配置 pid={}, appid={}, mch_id={}", pid, appid, mchId);
            return false;
        }
        String apiKey = payConfig.getApikey();
        String localSign = AesUtils.generateSign(params, apiKey);
        String wxSign = params.get("sign");
        log.info("[微信支付回调V2] 本地签名: {}, 微信签名: {}", localSign, wxSign);
        return localSign.equalsIgnoreCase(wxSign);
    }
}
