package cn.mingyang.cloud.usercenter.dao.entity;


import lombok.Data;

/**
 * 功能卖点计价单元表
 */
@Data
public class SellPriceItem {

    /**
     * ID
     */
    private int id;

    /**
     * 功能id
     */
    private Integer functionid;

    /**
     * 资源编码
     */
    private Integer sellgroup;

    /**
     * 消费单价
     */
    private Float expend;

    /**
     * 消费单元 1：次数 2：金币 3：数量
     */
    private Integer expendtype;

    /**
     * 客户端显示计价（5秒/金币）
     */
    private String pricehint;

    /**
     * appid
     */
    private String appid;

    /**
     * 功能初始ID
     */
    private String startuserid;

    /**
     * 功能终止ID
     */
    private String enduserid;

    /**
     * vip类型，0 非会员，1 vip， 2 svip
     */
    private Integer viptype;
}
