package cn.mingyang.cloud.usercenter.service;


import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.center.common.comEnum.BaseErrorCodeEnum;
import cn.mingyang.cloud.usercenter.config.TencentConfig;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.vod.v20180717.VodClient;
import com.tencentcloudapi.vod.v20180717.models.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 腾讯云Service
 */
@Slf4j
@Service
public class TencentService {

    private final TencentConfig tencentConfig;

    public TencentService(TencentConfig tencentConfig) {
        this.tencentConfig = tencentConfig;
    }


    /**
     * 查询腾讯云媒体
     */
    public CommonResult<?> getTencentMedia(SearchMediaRequest searchMediaRequest) {
        try {
            Credential credential = new Credential(tencentConfig.getSecretId(), tencentConfig.getSecretKey());
            VodClient vodClient = new VodClient(credential, tencentConfig.getRegion());
            return CommonResult.success(vodClient.SearchMedia(searchMediaRequest));
        } catch (Exception exception) {
            log.error("查询腾讯云媒体失败， 异常信息： ", exception);
            return CommonResult.error(BaseErrorCodeEnum.QUERY_MEDIA_ERROR.getCode(), "查询腾讯云媒体异常");
        }
    }


    /**
     * 删除腾讯云媒体
     */
    public CommonResult<?> delTencentMedia(SearchMediaRequest searchMediaRequest) {
        try {
            Credential credential = new Credential(tencentConfig.getSecretId(), tencentConfig.getSecretKey());
            VodClient vodClient = new VodClient(credential, tencentConfig.getRegion());
            //根据条件查询到媒体
            SearchMediaResponse mediaResponse = vodClient.SearchMedia(searchMediaRequest);
            //删除云媒体文件
            if (null != mediaResponse && null != mediaResponse.getMediaInfoSet()) {
                for (MediaInfo mediaInfo : mediaResponse.getMediaInfoSet()) {
                    DeleteMediaRequest req = new DeleteMediaRequest();
                    req.setFileId(mediaInfo.getFileId());
                    req.setSubAppId(searchMediaRequest.getSubAppId());
                    DeleteMediaResponse deleteMediaResponse = vodClient.DeleteMedia(req);
                    log.info("删除腾讯云媒体, 请求信息： {}，返回信息: {}", JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(deleteMediaResponse));
                }
            }
            return CommonResult.success(null);
        } catch (Exception exception) {
            log.error("删除腾讯云媒体失败， 异常信息： ", exception);
            return CommonResult.error(BaseErrorCodeEnum.DEL_MEDIA_ERROR.getCode(), "删除腾讯云媒体异常");
        }
    }
}
