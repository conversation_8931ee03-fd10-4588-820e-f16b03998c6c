package cn.mingyang.cloud.usercenter.dao.mapper;


import cn.mingyang.cloud.center.common.dao.entity.FunctionForward;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface FunctionForwardMapper {

    /**
     * 新增功能转发配置
     *
     * @param functionForward 功能转发对象
     * @return 影响行数
     */
    int insert(FunctionForward functionForward);

    /**
     * 根据ID删除功能转发配置
     *
     * @param id 配置ID
     * @return 影响行数
     */
    int deleteById(Integer id);

    /**
     * 批量删除功能转发配置
     *
     * @param ids 配置ID列表
     * @return 影响行数
     */
    int deleteBatchIds(@Param("ids") List<Integer> ids);

    /**
     * 更新功能转发配置
     *
     * @param functionForward 功能转发对象
     * @return 影响行数
     */
    int update(FunctionForward functionForward);

    /**
     * 根据ID查询功能转发配置
     *
     * @param id 配置ID
     * @return 功能转发对象
     */
    FunctionForward selectById(Integer id);

    /**
     * 查询所有功能转发配置
     *
     * @return 功能转发列表
     */
    List<FunctionForward> selectAll();

    /**
     * 根据条件分页查询功能转发配置
     *
     * @param mainFunction 主功能类型
     * @param newCNo       新版功能编号
     * @return 功能转发列表
     */
    List<FunctionForward> selectByCondition(
            @Param("mainFunction") String mainFunction,
            @Param("newCNo") Integer newCNo
    );
}
