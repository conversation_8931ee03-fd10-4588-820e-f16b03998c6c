package cn.mingyang.cloud.usercenter.dao.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 原始单据表
 */
@Data
public class OrgBills {

    /**
     * 交易记录ID，主键，自增
     */
    private Integer id;

    /**
     * 用户ID，关联用户表
     */
    private Integer userid;

    /**
     * 交易时间，自动更新
     */
    private LocalDateTime time;

    /**
     * 交易金额
     */
    private Float fee;

    /**
     * 交易单号
     */
    private String tradeno;

    /**
     * 交易描述
     */
    private String description;

    /**
     * 交易类型：
     * 0：消耗金币
     * 1：兑换金币
     * 2：Paypal支付
     * 3：支付宝支付
     * 4：微信支付
     * 5：谷歌支付
     * 6：看视频赚金币
     * 7：苹果支付
     * 8：配音模板购买
     * 9：IOS成品录音
     * 10：android成品录音
     * 11：大转盘
     * 12：支付宝包月
     * 13：微信包月
     */
    private Integer type;

    /**
     * 主播名称（如果适用）
     */
    private String anchorname;

    /**
     * 交易前金币数量
     */
    private Integer beforecoins;

    /**
     * 交易后金币数量
     */
    private Integer aftercoins;

    /**
     * 渠道
     */
    private String df;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 商户号
     */
    private String mchId;
}
