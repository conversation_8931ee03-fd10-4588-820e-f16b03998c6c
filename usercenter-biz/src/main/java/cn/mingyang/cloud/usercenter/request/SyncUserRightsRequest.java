package cn.mingyang.cloud.usercenter.request;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 用户权益同步和转移
 */
@Data
public class SyncUserRightsRequest {

    /**
     * 同步标识
     */
    @NotBlank(message = "同步标识：syncflag 不能为空")
    private String syncflag;


    /**
     * 源用户ID
     */
    @NotNull(message = "源用户ID：orguserid 不能为空")
    private Integer orguserid;


    /**
     * 目标用户ID
     */
    @NotNull(message = "目标用户ID：targetuserid 不能为空")
    private Integer targetuserid;

}
