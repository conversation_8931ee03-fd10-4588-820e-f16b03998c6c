package cn.mingyang.cloud.usercenter.comEnum;

import lombok.Getter;

/**
 * 用户类型
 */
@Getter
public enum UserType {
    COMM("0", "普通用户"),
    VIP("101", "VIP"),
    SVIP("102", "SVIP");

    private final String code;

    private final String desc;

    UserType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static UserType fromCode(String code) {
        for (UserType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据产品类型判断会员类型
     *
     * @param productType 产品类型
     * @return 类型编号
     */
    public static String getUserTypeByProductType(String productType) {
        if (productType.equals(UserType.VIP.getCode()) || productType.equals(UserType.SVIP.getCode())) {
            return productType;
        }
        return UserType.COMM.code;
    }

}
