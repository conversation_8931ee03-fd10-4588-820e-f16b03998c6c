package cn.mingyang.cloud.usercenter.controller.feign;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.comEnum.CommandType;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.center.common.util.MyUtils;
import cn.mingyang.cloud.center.common.util.ValidateUtils;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.api.request.FeignRequest;
import cn.mingyang.cloud.usercenter.api.response.ProductInfoResponse;
import cn.mingyang.cloud.usercenter.api.response.UserRightResponse;
import cn.mingyang.cloud.usercenter.request.BuyProductRequest;
import cn.mingyang.cloud.usercenter.request.GetProductInfoRequest;
import cn.mingyang.cloud.usercenter.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * Feign产品服务
 */
@RestController
@RequestMapping("/inner/product")
@Slf4j
public class FeignProductController {

    private final ProductService productService;

    private final ValidateUtils validateUtils;


    public FeignProductController(ProductService productService, ValidateUtils validateUtils) {
        this.productService = productService;
        this.validateUtils = validateUtils;
    }

    /**
     * 获取商品列表
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10201&*")
    public CommonResult<List<ProductInfoResponse>> getProductInfo(@RequestBody @Valid FeignRequest feignRequest) {
        CmdRequest cmdRequest = BeanUtil.toBean(feignRequest.getCmd(), CmdRequest.class);
        cmdRequest.setCno(CommandType.GetProductInfo.getCode());
        ComRequest comRequest = BeanUtil.toBean(feignRequest.getCom(), ComRequest.class);
        // 校验CMD
        MyUtils.checkCommand(cmdRequest, comRequest);
        //参数校验
        GetProductInfoRequest productRequest = validateUtils.validateParam(comRequest.getParam(), GetProductInfoRequest.class);
        log.info("Feign 获取商品列表, 请求信息： request: {} ", JSONUtil.toJsonStr(feignRequest));
        return productService.getProductInfo(cmdRequest, productRequest, comRequest);
    }

    /**
     * 购买商品
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10203&*")
    public CommonResult<UserRightResponse> buyProduct(@RequestBody @Valid FeignRequest feignRequest) {
        CmdRequest cmdRequest = BeanUtil.toBean(feignRequest.getCmd(), CmdRequest.class);
        cmdRequest.setCno(CommandType.BuyProduct.getCode());
        ComRequest comRequest = BeanUtil.toBean(feignRequest.getCom(), ComRequest.class);
        // 校验CMD
        MyUtils.checkCommand(cmdRequest, comRequest);
        //参数校验
        BuyProductRequest buyProductRequest = validateUtils.validateParam(comRequest.getParam(), BuyProductRequest.class);
        log.info("Feign 购买商品, 请求信息： request: {} ", JSONUtil.toJsonStr(feignRequest));
        return productService.buyProduct(buyProductRequest, cmdRequest, comRequest);
    }

    /**
     * 新注册用户赠送权限
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10204&*")
    public CommonResult<UserRightResponse> newUserGift(@RequestBody @Valid FeignRequest feignRequest) {
        CmdRequest cmdRequest = BeanUtil.toBean(feignRequest.getCmd(), CmdRequest.class);
        cmdRequest.setCno(CommandType.NewUserGift.getCode());
        ComRequest comRequest = BeanUtil.toBean(feignRequest.getCom(), ComRequest.class);
        // 校验CMD
        MyUtils.checkCommand(cmdRequest, comRequest);
        log.info("Feign 新注册用户赠送权限, 请求信息： request: {} ", JSONUtil.toJsonStr(feignRequest));
        return productService.newUserGift(cmdRequest, comRequest);
    }

}
