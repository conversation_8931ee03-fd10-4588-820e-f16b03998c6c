package cn.mingyang.cloud.usercenter.config;


import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 腾讯云配置
 */
@Getter
@Setter
@Configuration
@Slf4j
@ConfigurationProperties(prefix = "tencent")
public class TencentConfig {

    /**
     * secretId
     */
    private String secretId;

    /**
     * secretKey
     */
    private String secretKey;

    /**
     * 地域
     */
    private String region;
}
