package cn.mingyang.cloud.usercenter.dao.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 原始订阅表
 */
@Data
public class OrgSubscription {

    /**
     * 订阅记录ID，主键，自增
     */
    private Integer id;

    /**
     * 用户ID，关联用户表
     */
    private Integer userid;

    /**
     * 外部交易号
     */
    private String outTradeNo;

    /**
     * 订阅状态：1为自动续费，0非自动续费
     */
    private Integer subscribeStatus;

    /**
     * 自动续费金额（单位：元）
     */
    private Float fee;

    /**
     * 自动续费粒度（单位：月）
     */
    private Integer subscribeCycle;

    /**
     * 服务开始时间
     */

    private LocalDateTime createtime;

    /**
     * 服务到期时间
     */
    private LocalDateTime endtime;

    /**
     * 用户状态：1付费用户，0试用用户，-1退款用户
     */
    private String status;

    /**
     * 支付方式
     */
    private String payment;

    /**
     * 有效性：1有效，2无效
     */
    private String valid;

    /**
     * 服务有效期结束时间
     */
    private LocalDateTime svipendtime;

    /**
     * 限制类型，默认值为'0'
     */
    private String limittype;

    /**
     * AI限制，默认值为'100'
     */
    private String ailimit;

    /**
     * 原始限制，默认值为'100'
     */
    private String originallimit;

    /**
     * 高级限制，默认值为'40'
     */
    private String seniorlimit;

    /**
     * app独有的次数限制
     */
    private String appselflimit;

    /**
     * 视频时长，默认值为0
     */
    private Integer videoDuration;

    /**
     * 会员AI绘画次数，默认值为0
     */
    private Integer aiimgNum;
}
