package cn.mingyang.cloud.usercenter.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.comEnum.BaseErrorCodeEnum;
import cn.mingyang.cloud.center.common.exception.MyBaseException;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import cn.mingyang.cloud.usercenter.comEnum.PayType;
import cn.mingyang.cloud.usercenter.comEnum.PayTypeDetail;
import cn.mingyang.cloud.usercenter.dao.domain.AliPayBizContent;
import cn.mingyang.cloud.usercenter.dao.domain.AliPayConfig;
import cn.mingyang.cloud.usercenter.dao.domain.PayAmount;
import cn.mingyang.cloud.usercenter.dao.domain.ProductDoMain;
import cn.mingyang.cloud.usercenter.dao.entity.OrgUserOrder;
import cn.mingyang.cloud.usercenter.dao.entity.PayConfig;
import cn.mingyang.cloud.usercenter.dao.entity.PaySubscribe;
import cn.mingyang.cloud.usercenter.dao.mapper.PayMapper;
import cn.mingyang.cloud.usercenter.request.PayOrderRequest;
import cn.mingyang.cloud.usercenter.request.PaySubscribeUnSignRequest;
import cn.mingyang.cloud.usercenter.response.PayOrderResponse;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayResponse;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.*;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.internal.util.StringUtils;
import com.alipay.api.request.*;
import com.alipay.api.response.*;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 支付宝支付Service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AliPayService {

    // ==================== 常量定义 ====================

    /**
     * 支付宝网关地址 - 正式环境
     */
    private static final String ALIPAY_GATEWAY_PROD = "https://openapi.alipay.com/gateway.do";

    /**
     * 支付宝网关地址 - 沙箱环境
     */
    private static final String ALIPAY_GATEWAY_SANDBOX = "https://openapi-sandbox.dl.alipaydev.com/gateway.do";

    /**
     * 默认字符集
     */
    private static final String DEFAULT_CHARSET = "UTF-8";

    /**
     * 默认数据格式
     */
    private static final String DEFAULT_FORMAT = "json";

    /**
     * 默认签名类型
     */
    private static final String DEFAULT_SIGN_TYPE = "RSA2";

    /**
     * 条码支付场景
     */
    private static final String SCENE_BAR_CODE = "bar_code";

    private final PayMapper payMapper;
    private final OrderService orderService;

    public AliPayService(PayMapper payMapper, OrderService orderService) {
        this.payMapper = payMapper;
        this.orderService = orderService;
    }


    /**
     * 支付宝统一下单
     */
    @DS("#cmd.ds")
    @Transactional
    public CommonResult<?> aliUnifiedOrder(CmdRequest cmd, ComRequest com, PayOrderRequest payRequest) {
        // 参数验证
        CommonResult<?> validateResult = validateUnifiedOrderParams(cmd, com, payRequest);
        if (validateResult != null) {
            return validateResult;
        }

        try {
            PayTypeDetail payTypeDetail = PayTypeDetail.fromCode(payRequest.getPaycode());
            if (null == payTypeDetail) {
                return handleError("没有查到对应的支付方式: " + payRequest.getPaycode(), null, null);
            }

            String appId = cmd.getAppId();

            // 获取支付配置
            PayConfig payConfig = getPayChannelConfig(appId);
            if (payConfig == null) {
                return handleError("没有找到当前应用: " + appId + " 的支付宝支付渠道配置", null, null);
            }

            // 查询商品信息
            ProductDoMain queryProduct = new ProductDoMain();
            queryProduct.setProductid(payRequest.getProductid());
            ProductDoMain dbProduct = orderService.getProductInfo(cmd.getDs(),
                    queryProduct,
                    cmd.getAppId(),
                    com.getBase().getOsid(),
                    com.getBase().getLg(),
                    com.getBase().getDf());
            if (dbProduct == null) {
                return handleError("没有找到当前商品信息: " + payRequest.getProductid(), null, null);
            }

            // 创建支付订单
            OrgUserOrder orgUserOrder = null;
            // 如果是订阅支付，则查询是否有待支付订阅订单
            if (payTypeDetail == PayTypeDetail.ALI_SUBSCRIBE) {
                List<OrgUserOrder> userOrders = payMapper.selectNoPaySubscribeOrder(payConfig.getMchid(),
                        Integer.valueOf(com.getBase().getUserid()), DateUtil.today(), dbProduct.getProductname());
                if (CollectionUtil.isNotEmpty(userOrders)) {
                    orgUserOrder = userOrders.get(0);
                    orgUserOrder.setTitle(dbProduct.getTitle());
                }
            } else {
                // 创建支付订单
                orgUserOrder = orderService.createPayOrder(
                        payRequest, cmd, com, PayType.ALI.getIndex(), payConfig.getMchid());
            }
            if (orgUserOrder == null) {
                return handleError("创建支付订单失败", null, null);
            }
            log.info("支付宝统一下单, 订单信息： {}", JSONUtil.toJsonStr(orgUserOrder));

            // 判断是否是订阅订单
            PaySubscribe paySubscribe = null;
            if (Objects.equals(orgUserOrder.getIsSubscribe(), Constant.IsSubscribe.YES)
                    && payTypeDetail != PayTypeDetail.ALI_SUBSCRIBE) {
                paySubscribe = orderService.initSubscribeOrder(dbProduct, orgUserOrder, payConfig);
            }

            // 构建支付金额
            PayAmount amount = buildPayAmount(orgUserOrder);

            // 根据支付方式执行相应的支付逻辑
            return executePaymentByType(payTypeDetail, payConfig, amount, orgUserOrder, payRequest, paySubscribe);

        } catch (Exception e) {
            log.error("支付宝统一下单异常", e);
            return handleError("支付宝支付下单失败: " + e.getMessage(), null, e);
        }
    }

    /**
     * 验证统一下单参数
     */
    private CommonResult<?> validateUnifiedOrderParams(CmdRequest cmd, ComRequest com, PayOrderRequest payRequest) {
        if (cmd == null) {
            return handleError("命令参数不能为空", null, null);
        }
        if (com == null) {
            return handleError("通用参数不能为空", null, null);
        }
        if (payRequest == null) {
            return handleError("支付请求参数不能为空", null, null);
        }
        if (StrUtil.isBlank(cmd.getAppId())) {
            return handleError("应用ID不能为空", null, null);
        }
        if (StrUtil.isBlank(payRequest.getPaycode())) {
            return handleError("支付方式不能为空", null, null);
        }
        if (payRequest.getProductid() == null || payRequest.getProductid() <= 0) {
            return handleError("商品ID不能为空", null, null);
        }
        return null; // 验证通过
    }

    /**
     * 获取支付渠道配置
     */
    private PayConfig getPayChannelConfig(String appId) {
        PayConfig payConfig = payMapper.getPayChannelConfig(appId, PayType.ALI.getCode());
        if (payConfig == null) {
            payConfig = payMapper.getPayChannelConfig(Constant.DEFAULT, PayType.ALI.getCode());
        }
        return payConfig;
    }


    /**
     * 构建支付金额对象
     */
    private PayAmount buildPayAmount(OrgUserOrder orgUserOrder) {
        PayAmount amount = new PayAmount();
        amount.setCurrency("CNY");
        amount.setTotalYuan(orgUserOrder.getFee());
        return amount;
    }

    /**
     * 根据支付方式执行相应的支付逻辑
     */
    private CommonResult<?> executePaymentByType(PayTypeDetail payTypeDetail, PayConfig payConfig,
                                                 PayAmount amount, OrgUserOrder orgUserOrder,
                                                 PayOrderRequest payRequest, PaySubscribe paySubscribe) {
        return switch (payTypeDetail) {
            case ALI_BAR -> this.aliBarPay(payConfig, amount, orgUserOrder, payRequest);
            case ALI_JSAPI -> this.aliJsapiPay(payConfig, amount, orgUserOrder);
            case ALI_LITE -> this.aliLitePay(payConfig, amount, orgUserOrder);
            case ALI_APP -> this.aliAppPay(payConfig, amount, orgUserOrder, paySubscribe);
            case ALI_WAP -> this.aliWapPay(payConfig, amount, orgUserOrder);
            case ALI_PC -> this.aliPcPay(payConfig, amount, orgUserOrder);
            case ALI_QR -> this.aliQrPay(payConfig, amount, orgUserOrder);
            case ALI_SUBSCRIBE -> this.aliSubscribePay(payConfig, amount, orgUserOrder, payRequest);
            default -> handleError("不支持的支付方式: " + payTypeDetail.getDesc(), null, null);
        };
    }

    /**
     * 支付宝条码支付
     */
    private CommonResult<?> aliBarPay(PayConfig config, PayAmount amount, OrgUserOrder order, PayOrderRequest req) {
        String outTradeNo = order != null ? order.getOutTradeNo() : null;

        // 参数验证
        CommonResult<?> validateResult = validateBarPayParams(config, amount, order, req);
        if (validateResult != null) {
            return validateResult;
        }

        try {
            log.info("[AliBarPay] 开始支付宝条码支付, outTradeNo: {}, productName: {}",
                    outTradeNo, order.getProductName());

            AlipayClient alipayClient = buildAlipayClient(config);
            AlipayTradePayRequest request = new AlipayTradePayRequest();

            AliPayBizContent bizContent = buildBaseBizContent(order, amount, "支付宝条码支付： " + order.getTitle());
            bizContent.setScene(SCENE_BAR_CODE);
            bizContent.setAuth_code(req.getAuthcode());
            request.setBizContent(JSONUtil.toJsonStr(bizContent));

            AlipayTradePayResponse response = alipayClient.execute(request);
            CommonResult<?> errorResult = handleAlipayResponse("AliBarPay", outTradeNo, response);
            if (errorResult != null) {
                return errorResult;
            }

            Map<String, String> payData = new HashMap<>();
            if (StrUtil.isNotBlank(response.getTradeNo())) {
                payData.put("tradeNo", response.getTradeNo());
            }
            return buildPayResponse(outTradeNo, config, payData);

        } catch (Exception e) {
            log.error("[AliBarPay] 条码支付异常, outTradeNo: {}", outTradeNo, e);
            return handleError("支付宝条码支付失败: " + e.getMessage(), outTradeNo, e);
        }
    }

    /**
     * 验证条码支付参数
     */
    private CommonResult<?> validateBarPayParams(PayConfig config, PayAmount amount, OrgUserOrder order, PayOrderRequest req) {
        if (config == null) {
            return handleError("支付配置不能为空", null, null);
        }
        if (amount == null) {
            return handleError("支付金额不能为空", null, null);
        }
        if (order == null) {
            return handleError("订单信息不能为空", null, null);
        }
        if (req == null) {
            return handleError("支付请求不能为空", null, null);
        }
        if (StrUtil.isBlank(req.getAuthcode())) {
            return handleError("支付宝条码支付，用户条码不能为空", order.getOutTradeNo(), null);
        }
        if (StrUtil.isBlank(order.getOutTradeNo())) {
            return handleError("订单号不能为空", null, null);
        }
        return null; // 验证通过
    }

    /**
     * 支付宝JSAPI支付（生活号/小程序）
     */
    private CommonResult<?> aliJsapiPay(PayConfig config, PayAmount amount, OrgUserOrder order) {
        String outTradeNo = order != null ? order.getOutTradeNo() : null;

        // 参数验证
        CommonResult<?> validateResult = validateCommonPayParams(config, amount, order, "JSAPI支付");
        if (validateResult != null) {
            return validateResult;
        }

        try {
            log.info("[AliJsapiPay] 开始支付宝JSAPI支付, outTradeNo: {}, productName: {}",
                    outTradeNo, order.getProductName());

            // 获取并验证支付配置
            AliPayConfig payConfig = parseAndValidatePayConfig(config, outTradeNo);
            if (payConfig == null) {
                return handleError("支付配置解析失败", outTradeNo, null);
            }

            AlipayClient alipayClient = buildAlipayClient(config);
            AlipayTradeWapPayRequest request = new AlipayTradeWapPayRequest();

            AliPayBizContent bizContent = buildBaseBizContent(order, amount, "支付宝JSAPI支付： " + order.getTitle());
            bizContent.setProduct_code(payConfig.getProductCode());
            request.setBizContent(JSONUtil.toJsonStr(bizContent));
            request.setNotifyUrl(config.getNotifyurl());

            // 设置返回地址
            if (StrUtil.isNotBlank(payConfig.getReturnUrl())) {
                request.setReturnUrl(payConfig.getReturnUrl());
            }

            AlipayTradeWapPayResponse response = alipayClient.pageExecute(request);
            CommonResult<?> errorResult = handleAlipayResponse("AliJsapiPay", outTradeNo, response);
            if (errorResult != null) {
                return errorResult;
            }

            Map<String, String> payData = new HashMap<>();
            if (StrUtil.isNotBlank(response.getBody())) {
                payData.put("body", response.getBody());
            }
            return buildPayResponse(outTradeNo, config, payData);

        } catch (Exception e) {
            log.error("[AliJsapiPay] JSAPI支付异常, outTradeNo: {}", outTradeNo, e);
            return handleError("支付宝JSAPI支付失败: " + e.getMessage(), outTradeNo, e);
        }
    }

    /**
     * 验证通用支付参数
     */
    private CommonResult<?> validateCommonPayParams(PayConfig config, PayAmount amount, OrgUserOrder order, String payMethod) {
        if (config == null) {
            return handleError("支付配置不能为空", null, null);
        }
        if (amount == null) {
            return handleError("支付金额不能为空", null, null);
        }
        if (order == null) {
            return handleError("订单信息不能为空", null, null);
        }
        if (StrUtil.isBlank(order.getOutTradeNo())) {
            return handleError("订单号不能为空", null, null);
        }
        if (amount.getTotalYuan() == null || amount.getTotalYuan() <= 0) {
            return handleError("支付金额必须大于0", order.getOutTradeNo(), null);
        }
        return null; // 验证通过
    }

    /**
     * 解析并验证支付配置
     */
    private AliPayConfig parseAndValidatePayConfig(PayConfig config, String outTradeNo) {
        try {
            if (StrUtil.isBlank(config.getConfigvalues())) {
                log.error("支付配置值为空, outTradeNo: {}", outTradeNo);
                return null;
            }

            AliPayConfig payConfig = JSONUtil.toBean(config.getConfigvalues(), AliPayConfig.class);
            if (payConfig == null) {
                log.error("支付配置解析失败, outTradeNo: {}, configValues: {}", outTradeNo, config.getConfigvalues());
                return null;
            }

            if (StrUtil.isBlank(payConfig.getProductCode())) {
                log.error("缺少必要配置参数：product_code, outTradeNo: {}", outTradeNo);
                return null;
            }

            return payConfig;
        } catch (Exception e) {
            log.error("解析支付配置异常, outTradeNo: {}", outTradeNo, e);
            return null;
        }
    }

    /**
     * 支付宝小程序支付（同JSAPI）
     */
    private CommonResult<?> aliLitePay(PayConfig config, PayAmount amount, OrgUserOrder order) {
        return aliJsapiPay(config, amount, order);
    }

    /**
     * 支付宝APP支付
     */
    private CommonResult<?> aliAppPay(PayConfig config, PayAmount amount, OrgUserOrder order, PaySubscribe paySubscribe) {
        String outTradeNo = order != null ? order.getOutTradeNo() : null;

        // 参数验证
        CommonResult<?> validateResult = validateCommonPayParams(config, amount, order, "APP支付");
        if (validateResult != null) {
            return validateResult;
        }

        try {
            log.info("[AliAppPay] 开始支付宝APP支付, outTradeNo: {}, productName: {}",
                    outTradeNo, order.getProductName());

            // 初始化SDK
            AlipayClient alipayClient = buildAlipayClient(config);
            // 获取并验证支付配置
            AliPayConfig payConfig = parseAndValidatePayConfig(config, outTradeNo);
            if (payConfig == null) {
                return handleError("支付配置解析失败", outTradeNo, null);
            }
            AlipayTradeAppPayRequest request = new AlipayTradeAppPayRequest();
            AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();
            // 设置商户订单号
            model.setOutTradeNo(order.getOutTradeNo());
            // 设置订单总金额
            model.setTotalAmount(amount.getTotalYuan() + "");
            // 设置订单标题
            model.setSubject(order.getTitle());
            // 设置产品码
            model.setProductCode(payConfig.getProductCode());
            // 如果有订阅商品，设置签约参数
            if (paySubscribe != null) {
                if (null == payConfig.getSubscribe()) {
                    return handleError("签约参数不能为空", config.getPlatformappid(), null);
                }
                model.setAgreementSignParams(buildAgreementSignParams(config, payConfig, paySubscribe, order));
            }
            request.setBizModel(model);
            // 设置回调
            request.setNotifyUrl(config.getNotifyurl());
            log.info("[AliAppPay] APP支付, 支付参数: {}", JSONUtil.toJsonStr(request));
            AlipayTradeAppPayResponse response = alipayClient.sdkExecute(request);
            CommonResult<?> errorResult = handleAlipayResponse("AliAppPay", outTradeNo, response);
            if (errorResult != null) {
                return errorResult;
            }
            Map<String, String> payData = new HashMap<>();
            if (StrUtil.isNotBlank(response.getBody())) {
                payData.put("body", response.getBody());
            }
            return buildPayResponse(outTradeNo, config, payData);

        } catch (Exception e) {
            log.error("[AliAppPay] APP支付异常, outTradeNo: {}", outTradeNo, e);
            return handleError("支付宝APP支付失败: " + e.getMessage(), outTradeNo, e);
        }
    }


    /**
     * 封装订阅支付参数
     *
     * @param config       支付配置
     * @param paySubscribe 订阅商品信息
     * @return 订阅Model
     */
    private SignParams buildAgreementSignParams(PayConfig config, AliPayConfig payConfig, PaySubscribe paySubscribe, OrgUserOrder order) {
        // 设置签约参数
        SignParams agreementSignParams = new SignParams();
        AccessParams accessParams = new AccessParams();
        // 渠道
        accessParams.setChannel(payConfig.getSubscribe().getAccessParams().getChannel());
        agreementSignParams.setAccessParams(accessParams);
        PeriodRuleParams periodRuleParams = new PeriodRuleParams();
        // 间隔
        periodRuleParams.setPeriod(paySubscribe.getDuration().longValue());
        // 首次执行时间
        periodRuleParams.setExecuteTime(LocalDateTime.now()
                .plusDays(paySubscribe.getDuration()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        periodRuleParams.setSingleAmount(paySubscribe.getFee() + "");
        periodRuleParams.setPeriodType("DAY");
        agreementSignParams.setPeriodRuleParams(periodRuleParams);
        agreementSignParams.setSignNotifyUrl(payConfig.getSubscribe().getSignNotifyUrl());
        agreementSignParams.setPersonalProductCode(payConfig.getSubscribe().getPersonalProductCode());
        agreementSignParams.setProductCode(payConfig.getSubscribe().getProductCode());
        agreementSignParams.setSignScene(payConfig.getSubscribe().getSignScene());
        agreementSignParams.setExternalAgreementNo(order.getOutTradeNo());
        return agreementSignParams;
    }


    /**
     * 支付宝H5支付（WAP）
     */
    private CommonResult<?> aliWapPay(PayConfig config, PayAmount amount, OrgUserOrder order) {
        return aliJsapiPay(config, amount, order);
    }

    /**
     * 支付宝PC网站支付
     */
    private CommonResult<?> aliPcPay(PayConfig config, PayAmount amount, OrgUserOrder order) {
        String outTradeNo = order != null ? order.getOutTradeNo() : null;

        // 参数验证
        CommonResult<?> validateResult = validateCommonPayParams(config, amount, order, "PC支付");
        if (validateResult != null) {
            return validateResult;
        }

        try {
            log.info("[AliPcPay] 开始支付宝PC网站支付, outTradeNo: {}, productName: {}",
                    outTradeNo, order.getProductName());

            // 获取并验证支付配置
            AliPayConfig payConfig = parseAndValidatePayConfig(config, outTradeNo);
            if (payConfig == null) {
                return handleError("支付配置解析失败", outTradeNo, null);
            }

            AlipayClient alipayClient = buildAlipayClient(config);
            AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();

            AliPayBizContent bizContent = buildBaseBizContent(order, amount, "支付宝PC网站支付： " + order.getTitle());
            bizContent.setProduct_code(payConfig.getProductCode());
            request.setBizContent(JSONUtil.toJsonStr(bizContent));
            request.setNotifyUrl(config.getNotifyurl());

            // 设置返回地址
            if (StrUtil.isNotBlank(payConfig.getReturnUrl())) {
                request.setReturnUrl(payConfig.getReturnUrl());
            }

            AlipayTradePagePayResponse response = alipayClient.pageExecute(request);
            CommonResult<?> errorResult = handleAlipayResponse("AliPcPay", outTradeNo, response);
            if (errorResult != null) {
                return errorResult;
            }

            Map<String, String> payData = new HashMap<>();
            if (StrUtil.isNotBlank(response.getBody())) {
                payData.put("body", response.getBody());
            }
            return buildPayResponse(outTradeNo, config, payData);

        } catch (Exception e) {
            log.error("[AliPcPay] PC支付异常, outTradeNo: {}", outTradeNo, e);
            return handleError("支付宝PC支付失败: " + e.getMessage(), outTradeNo, e);
        }
    }

    /**
     * 支付宝二维码支付
     */
    private CommonResult<?> aliQrPay(PayConfig config, PayAmount amount, OrgUserOrder order) {
        String outTradeNo = order != null ? order.getOutTradeNo() : null;

        // 参数验证
        CommonResult<?> validateResult = validateCommonPayParams(config, amount, order, "二维码支付");
        if (validateResult != null) {
            return validateResult;
        }

        try {
            log.info("[AliQrPay] 开始支付宝二维码支付, outTradeNo: {}, productName: {}",
                    outTradeNo, order.getProductName());

            AlipayClient alipayClient = buildAlipayClient(config);
            AlipayTradePrecreateRequest request = new AlipayTradePrecreateRequest();

            AliPayBizContent bizContent = buildBaseBizContent(order, amount, "支付宝二维码支付： " + order.getTitle());
            request.setBizContent(JSONUtil.toJsonStr(bizContent));
            request.setNotifyUrl(config.getNotifyurl());

            AlipayTradePrecreateResponse response = alipayClient.execute(request);
            CommonResult<?> errorResult = handleAlipayResponse("AliQrPay", outTradeNo, response);
            if (errorResult != null) {
                return errorResult;
            }

            Map<String, String> payData = new HashMap<>();
            if (StrUtil.isNotBlank(response.getQrCode())) {
                payData.put("qrCode", response.getQrCode());
            }
            return buildPayResponse(outTradeNo, config, payData);

        } catch (Exception e) {
            log.error("[AliQrPay] 二维码支付异常, outTradeNo: {}", outTradeNo, e);
            return handleError("支付宝二维码支付失败: " + e.getMessage(), outTradeNo, e);
        }
    }

    /**
     * 支付宝订阅支付
     */
    private CommonResult<?> aliSubscribePay(PayConfig config, PayAmount amount, OrgUserOrder order, PayOrderRequest payRequest) {
        String outTradeNo = order != null ? order.getOutTradeNo() : null;
        // 参数验证
        CommonResult<?> validateResult = validateCommonPayParams(config, amount, order, "支付宝代扣支付");
        if (validateResult != null) {
            return validateResult;
        }
        try {
            log.info("[AliSubscribe] 开始支付宝代扣支付支付, outTradeNo: {}, productName: {}",
                    outTradeNo, order.getProductName());
            AlipayClient alipayClient = buildAlipayClient(config);
            AliPayBizContent bizContent = buildBaseBizContent(order, amount, "支付宝代扣支付： " + order.getTitle());
            AlipayTradePayRequest request = new AlipayTradePayRequest();
            AlipayTradePayModel model = new AlipayTradePayModel();
            // 设置商户订单号
            model.setOutTradeNo(bizContent.getOut_trade_no());
            // 设置订单总金额
            model.setTotalAmount(bizContent.getTotal_amount() + "");
            // 设置订单标题
            model.setSubject(bizContent.getSubject());
            // 设置产品码
            model.setProductCode("GENERAL_WITHHOLDING");
            // 设置代扣信息
            AgreementParams agreementParams = new AgreementParams();
            agreementParams.setAgreementNo(payRequest.getAgreementno());
            model.setAgreementParams(agreementParams);
            request.setBizModel(model);
            request.setNotifyUrl(config.getNotifyurl());
            AlipayTradePayResponse response = alipayClient.execute(request);
            log.info("[AliSubscribe] 支付宝代扣支, response: {}", JSONUtil.toJsonStr(response));
            CommonResult<?> result = handleAlipayResponse("AliSubscribe", outTradeNo, response);
            if (null == result) {
                return CommonResult.success(null);
            }
            return result;
        } catch (Exception e) {
            log.error("[AliSubscribe] 支付宝代扣支付异常, outTradeNo: {}", outTradeNo, e);
            return handleError("支付宝代扣支付失败: " + e.getMessage(), outTradeNo, e);
        }
    }

    /**
     * 处理错误响应
     */
    private CommonResult<?> handleError(String message, String outTradeNo, Exception e) {
        String errorMsg = StrUtil.isBlank(message) ? "支付处理失败" : message;

        if (e != null) {
            log.error("[AliPay] {}, outTradeNo: {}", errorMsg, outTradeNo, e);
            throw new RuntimeException(BaseErrorCodeEnum.PAY_ERROR.getCode(), e);
        } else {
            log.error("[AliPay] {}, outTradeNo: {}", errorMsg, outTradeNo);
        }

        return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), errorMsg);
    }

    /**
     * 处理支付宝响应
     */
    private CommonResult<?> handleAlipayResponse(String payMethod, String outTradeNo, Object response) {
        if (response == null) {
            log.error("[{}] 支付宝响应为空, outTradeNo: {}", payMethod, outTradeNo);
            return handleError("支付宝响应为空", outTradeNo, null);
        }

        if (response instanceof AlipayResponse alipayResponse) {
            if (alipayResponse.isSuccess()) {
                log.info("[{}] 下单成功, outTradeNo: {}", payMethod, outTradeNo);
                return null; // 返回null表示成功，需要调用方处理成功响应
            } else {
                String errorMsg = buildAlipayErrorMessage(alipayResponse);
                log.error("[{}] 下单失败, outTradeNo: {}, errorMsg: {}", payMethod, outTradeNo, errorMsg);
                return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), errorMsg);
            }
        }

        log.error("[{}] 不支持的响应类型: {}, outTradeNo: {}", payMethod, response.getClass().getSimpleName(), outTradeNo);
        return handleError("不支持的响应类型", outTradeNo, null);
    }

    /**
     * 构建支付宝错误信息
     */
    private String buildAlipayErrorMessage(AlipayResponse response) {
        StringBuilder errorMsg = new StringBuilder();

        if (StrUtil.isNotBlank(response.getMsg())) {
            errorMsg.append(response.getMsg());
        }

        if (StrUtil.isNotBlank(response.getSubMsg())) {
            if (!errorMsg.isEmpty()) {
                errorMsg.append(": ");
            }
            errorMsg.append(response.getSubMsg());
        }

        if (errorMsg.isEmpty()) {
            errorMsg.append("支付宝接口调用失败");
        }

        return errorMsg.toString();
    }

    /**
     * 构建基础业务参数
     */
    private AliPayBizContent buildBaseBizContent(OrgUserOrder order, PayAmount amount, String subject) {
        if (order == null) {
            throw new IllegalArgumentException("订单信息不能为空");
        }
        if (amount == null) {
            throw new IllegalArgumentException("支付金额不能为空");
        }
        if (StrUtil.isBlank(subject)) {
            subject = "商品支付";
        }

        AliPayBizContent bizContent = new AliPayBizContent();
        bizContent.setOut_trade_no(order.getOutTradeNo());
        bizContent.setTotal_amount(amount.getTotalYuan());
        bizContent.setSubject(subject);
        return bizContent;
    }


    /**
     * 构建完整的支付响应
     */
    private CommonResult<?> buildPayResponse(String outTradeNo, PayConfig config, Map<String, String> payData) {
        PayOrderResponse payOrderResponse = new PayOrderResponse();
        // 设置基础信息
        payOrderResponse.setOuttradeno(outTradeNo);
        payOrderResponse.setPayAppId(config.getPlatformappid());
        payOrderResponse.setPlatformappid(config.getPlatformappid());
        payOrderResponse.setMchId(config.getMchid());
        payOrderResponse.setPlatformmchid(config.getMchid());
        // 设置支付数据
        payOrderResponse.setPaydata(payData != null ? payData : new HashMap<>());
        log.info("[AliPay] 构建完整支付响应成功, outTradeNo: {}, payAppId: {}, mchId: {}",
                outTradeNo, config.getPlatformappid(), config.getMchid());
        return CommonResult.success(payOrderResponse);
    }


    /**
     * 构建支付宝客户端
     */
    private AlipayClient buildAlipayClient(PayConfig config) {
        if (config == null) {
            throw new IllegalArgumentException("支付配置不能为空");
        }
        if (StrUtil.isBlank(config.getPlatformappid())) {
            throw new IllegalArgumentException("支付宝AppId不能为空");
        }
        if (StrUtil.isBlank(config.getPrivatekey())) {
            throw new IllegalArgumentException("商户私钥不能为空");
        }
        if (StrUtil.isBlank(config.getPublickey())) {
            throw new IllegalArgumentException("支付宝公钥不能为空");
        }

        // 根据配置决定使用正式环境还是沙箱环境
        String gatewayUrl = determineGatewayUrl(config);

        log.debug("[AliPay] 构建支付宝客户端, appId: {}, gateway: {}", config.getPlatformappid(), gatewayUrl);

        return new DefaultAlipayClient(
                gatewayUrl,
                config.getPlatformappid(),
                config.getPrivatekey(),
                DEFAULT_FORMAT,
                DEFAULT_CHARSET,
                config.getPublickey(),
                DEFAULT_SIGN_TYPE
        );
    }

    /**
     * 确定网关地址
     */
    private String determineGatewayUrl(PayConfig config) {
        try {
            if (StrUtil.isNotBlank(config.getConfigvalues())) {
                AliPayConfig payConfig = JSONUtil.toBean(config.getConfigvalues(), AliPayConfig.class);
                if (payConfig != null && payConfig.getSandbox()) {
                    log.info("[AliPay] 使用沙箱环境, appId: {}", config.getPlatformappid());
                    return ALIPAY_GATEWAY_SANDBOX;
                }
            }
        } catch (Exception e) {
            log.warn("[AliPay] 解析环境配置失败，使用生产环境, appId: {}", config.getPlatformappid(), e);
        }

        log.info("[AliPay] 使用生产环境, appId: {}", config.getPlatformappid());
        return ALIPAY_GATEWAY_PROD;
    }

    /**
     * 支付宝回调验签（自动查找公钥）
     */
    @DS("#pid")
    public boolean verifyAliPayNotify(String pid, Map<String, String> params) {
        // 参数验证
        if (StrUtil.isBlank(pid)) {
            log.error("[支付宝支付回调] pid不能为空");
            return false;
        }
        if (params == null || params.isEmpty()) {
            log.error("[支付宝支付回调] 回调参数不能为空, pid={}", pid);
            return false;
        }

        String appId = params.get("app_id");
        if (StrUtil.isBlank(appId)) {
            log.error("[支付宝支付回调] app_id不能为空, pid={}", pid);
            return false;
        }

        // 获取支付配置
        PayConfig payConfig = orderService.getPayChannelConfig(pid, appId, PayType.ALI.getCode());
        if (payConfig == null) {
            log.error("[支付宝支付回调] 未找到支付配置 pid={}, app_id={}", pid, appId);
            return false;
        }

        String alipayPublicKey = payConfig.getPublickey();
        if (StrUtil.isBlank(alipayPublicKey)) {
            log.error("[支付宝支付回调] 支付宝公钥为空, pid={}, app_id={}", pid, appId);
            return false;
        }

        return verifyAliPayNotifyInternal(params, alipayPublicKey, pid, appId);
    }

    /**
     * 支付宝回调验签（指定公钥，供内部调用）
     */
    private boolean verifyAliPayNotifyInternal(Map<String, String> params, String alipayPublicKey, String pid, String appId) {
        try {
            // charset和sign_type通常在回调参数中有，若无可默认UTF-8和RSA2
            String charset = params.getOrDefault("charset", DEFAULT_CHARSET);
            String signType = params.getOrDefault("sign_type", DEFAULT_SIGN_TYPE);

            log.debug("[支付宝支付回调] 开始验签, pid={}, app_id={}, charset={}, signType={}",
                    pid, appId, charset, signType);

            boolean verifyResult = AlipaySignature.rsaCheckV1(params, alipayPublicKey, charset, signType);

            if (verifyResult) {
                log.info("[支付宝支付回调] 验签成功, pid={}, app_id={}", pid, appId);
            } else {
                log.error("[支付宝支付回调] 验签失败, pid={}, app_id={}", pid, appId);
            }

            return verifyResult;
        } catch (Exception e) {
            log.error("[支付宝支付回调] 验签异常, pid={}, app_id={}", pid, appId, e);
            return false;
        }
    }


    /**
     * 处理支付宝订阅
     *
     * @param alipayUserId        支付宝唯一用户号
     * @param payConfig           支付配置
     * @param agreementNo         订阅协议号
     * @param status              订阅状态
     * @param externalAgreementNo 首次订阅单号
     * @return 结果
     */
    @DS("#pid")
    public String doSubscribe(String alipayUserId, String pid, PayConfig payConfig, String agreementNo, String status, String externalAgreementNo) {
        // 根据首次单号查询订阅数据
        int signStatus = Constant.SignStatus.SIGNING;
        if (status.equals(Constant.AliSignStatus.SIGNED)) {
            signStatus = Constant.SignStatus.SIGNED;
        }
        if (status.equals(Constant.AliSignStatus.CANCEL)) {
            signStatus = Constant.SignStatus.CANCEL;
        }
        PaySubscribe dbPaySubscribe = payMapper.selectPaySubscribeByFirstOutTradeNo(externalAgreementNo);
        if (null == dbPaySubscribe) {
            // 历史订单，则跳过
            if (externalAgreementNo.startsWith("20")) {
                return "success";
            }
            //查询订单
            OrgUserOrder userOrder = payMapper.getUserOrderByOutTradeNo(externalAgreementNo);
            if (userOrder == null) {
                log.warn("[支付宝订阅回调] 未找到订单 outTradeNo={}", externalAgreementNo);
                throw new MyBaseException(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "未找到订单: " + externalAgreementNo);
            }
            //查询商品
            ProductDoMain queryProduct = new ProductDoMain();
            queryProduct.setProductname(userOrder.getProductName());
            ProductDoMain product = orderService.getProductInfo(
                    pid, queryProduct, userOrder.getAppid(), userOrder.getOsid(), Constant.LanguageType.ZH_HANS, userOrder.getDf());
            if (product == null) {
                log.warn("[支付宝订阅回调] 未找到商品 product={}", userOrder.getProductName());
                throw new MyBaseException(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "未找到商品: " + userOrder.getProductName());
            }
            PaySubscribe subscribe = orderService.initSubscribeOrder(product, userOrder, payConfig);
            subscribe.setStatus(signStatus);
            subscribe.setAgreementno(agreementNo);
            Map<String, String> params = new HashMap<>();
            params.put("alipay_user_id", alipayUserId);
            subscribe.setConfigvalues(JSONUtil.toJsonStr(params));
            orderService.createSubscribeOrder(pid, subscribe);
        } else {
            payMapper.updatePaySubscribeStatus(dbPaySubscribe.getId(), signStatus, agreementNo);
        }
        return "success";
    }

    /**
     * 支付宝订阅取消
     */
    @DS("#cmd.ds")
    @Transactional
    public CommonResult<?> aliSubscribeUnSign(CmdRequest cmd, ComRequest com, PaySubscribeUnSignRequest unSignRequest) {
        String appId = cmd.getAppId();
        // 获取支付配置
        PayConfig payConfig = getPayChannelConfig(appId);
        if (payConfig == null) {
            return handleError("没有找到当前应用: " + appId + " 的支付宝支付渠道配置", null, null);
        }
        try {
            // 查询订阅记录
            List<PaySubscribe> subscribes = payMapper.selectPaySubscribeByIds(unSignRequest.getIds());
            if (CollectionUtil.isEmpty(subscribes)) {
                return handleError("未查询到订阅记录", null, null);
            }
            // 逐条解约
            AlipayClient alipayClient = buildAlipayClient(payConfig);
            for (PaySubscribe subscribe : subscribes) {
                if (StrUtil.isBlank(subscribe.getAgreementno())) {
                    log.warn("[AliUnSign] 订阅记录缺少协议号，跳过，id={}", subscribe.getId());
                    continue;
                }
                // 调用支付宝解约接口
                AlipayUserAgreementUnsignRequest request = new AlipayUserAgreementUnsignRequest();
                AlipayUserAgreementUnsignModel model = new AlipayUserAgreementUnsignModel();
                model.setAgreementNo(subscribe.getAgreementno());
                model.setPersonalProductCode("CYCLE_PAY_AUTH_P"); // 常用代扣产品码
                if (!StringUtils.isEmpty(subscribe.getConfigvalues())) {
                    JSONObject jb = JSONUtil.parseObj(subscribe.getConfigvalues());
                    model.setAlipayUserId(jb.getStr("alipay_user_id"));
                }
                request.setBizModel(model);
                log.info("[AliUnSign] 取消订阅成功，request={}", JSONUtil.toJsonStr(request));
                AlipayUserAgreementUnsignResponse response = alipayClient.execute(request);
                CommonResult<?> errorResult = handleAlipayResponse("AliUnSign", subscribe.getFirstouttradeno(), response);
                if (errorResult != null) {
                    return errorResult;
                }
                // 更新本地订阅状态为取消
                payMapper.updatePaySubscribeStatus(subscribe.getId(), Constant.SignStatus.CANCEL, subscribe.getAgreementno());
                log.info("[AliUnSign] 取消订阅成功，id={} agreementNo={}", subscribe.getId(), subscribe.getAgreementno());
            }
            return CommonResult.success(null);
        } catch (Exception e) {
            log.error("[AliUnSign] 取消订阅异常", e);
            return handleError("支付宝取消订阅失败: " + e.getMessage(), null, e);
        }
    }
}
