package cn.mingyang.cloud.usercenter.config;

import cn.mingyang.cloud.usercenter.listener.RightsCacheChangeListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;

/**
 * Redis配置类
 */
@Configuration
public class RedisConfig {

    @Autowired
    private RightsCacheChangeListener rightsCacheChangeListener;


    /**
     * 监听Redis操作
     *
     * @param connectionFactory Redis链接工厂
     * @return 监听器
     */
    @Bean
    public RedisMessageListenerContainer redisMessageListenerContainer(
            RedisConnectionFactory connectionFactory) {

        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);

        // 监听所有键的 set/del/expired 事件（通过通配符）
        container.addMessageListener(rightsCacheChangeListener, rightsCacheChangeListener.getTopic());
        return container;
    }


}
