package cn.mingyang.cloud.usercenter.dao.domain;


import lombok.Data;

import java.math.BigDecimal;


/**
 * 支付金额
 */
@Data
public class PayAmount {

    /**
     * 支付总金额（分）
     */
    private Integer totalFen;

    /**
     * 退款金额（分）
     */
    private Integer refundFen;

    /**
     * 支付总金额（元）
     */
    private Float totalYuan;

    /**
     * 币种
     */
    private String currency;

    /**
     * 获取总金额（分）
     * 如果totalFen为空，则根据totalYuan计算
     */
    public Integer getTotalFen() {
        if (totalFen != null) {
            return totalFen;
        }
        if (totalYuan != null) {
            return BigDecimal.valueOf(totalYuan * 100).intValue();
        }
        return 0;
    }

    /**
     * 设置总金额（元），同时更新分的值
     */
    public void setTotalYuan(Float totalYuan) {
        this.totalYuan = totalYuan;
        if (totalYuan != null) {
            this.totalFen = BigDecimal.valueOf(totalYuan * 100).intValue();
        }
    }
}
