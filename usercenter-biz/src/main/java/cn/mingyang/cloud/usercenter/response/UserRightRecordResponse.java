package cn.mingyang.cloud.usercenter.response;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户权益记录返回信息
 */
@Data
public class UserRightRecordResponse {

    /**
     * 总数
     */
    private Integer total;

    /**
     * 当前页码，默认1
     */
    private Integer pageNum;

    /**
     * 记录
     */
    private List<RightRecordDetail> records;


    @Data
    public static class RightRecordDetail {
        /**
         * ID
         */
        private Integer id;

        /**
         * 用户ID
         */
        private Integer userid;

        /**
         * 数量
         */
        private Float quantity;

        /**
         * (待删除)
         * 会员权益流水标识
         * 101: 注册会员赠送;
         * 102: 购买VIP赠送;
         * 103: 购买SVIP赠送;
         * 104: 客服调整;
         * 105: 购买加油包;
         * 201: 消耗;
         */
        private Integer consumeType;

        /**
         * 会员权益流水标识
         * 101: 注册会员赠送;
         * 102: 购买VIP赠送;
         * 103: 购买SVIP赠送;
         * 104: 客服调整;
         * 105: 购买加油包;
         * 201: 消耗;
         */
        private Integer recordtype;

        /**
         * 描述
         */
        private String desc;

        /**
         * 来源描述（待删除）
         */
        private String fromDesc;

        /**
         * 卖点组编号，默认0
         */
        private Integer sellgroup;

        /**
         * 消耗时间
         */
        private LocalDateTime time;

        /**
         * 消耗时间，字符串(待删除)
         */
        private String timeStr;

        /**
         * 消耗时间，字符串
         */
        private String timeformat;
    }
}
