package cn.mingyang.cloud.usercenter.dao.entity;


import lombok.Data;

import java.time.LocalDateTime;

/**
 * 支付订阅
 */
@Data
public class PaySubscribe {

    /**
     * 键，自增
     */
    private Integer id;

    /**
     * 首次订单号
     */
    private String firstouttradeno;

    /**
     * 支付平台编号
     */
    private String platformcode;

    /**
     * 商户号
     */
    private String mchid;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 渠道
     */
    private String df;

    /**
     * 用户ID，关联用户表
     */
    private Integer userid;

    /**
     * 系统id
     */
    private String osid;

    /**
     * 商品id
     */
    private Integer productid;

    /**
     * 下一次扣款时间
     */
    private LocalDateTime nextdeducttime;

    /**
     * 状态 0签约中 1签约 2取消 3其他
     */
    private Integer status;

    /**
     * 单次扣款金额
     */
    private Float fee;

    /**
     * 签约周期 (天)
     */
    private Integer duration;

    /**
     * （失败的）是否可再次重试 0 不可，1可以
     */
    private Integer retry;

    /**
     * 创建时间
     */
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    private LocalDateTime updatetime;

    /**
     * 签约协议号
     */
    private String agreementno;

    /**
     * 币种
     */
    private String currency;

    /**
     * 扩展配置
     */
    private String configvalues;

}
