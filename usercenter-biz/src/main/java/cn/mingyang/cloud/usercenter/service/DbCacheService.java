package cn.mingyang.cloud.usercenter.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.service.RedisService;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import cn.mingyang.cloud.usercenter.config.CacheConfig;
import cn.mingyang.cloud.usercenter.dao.domain.ProductDoMain;
import cn.mingyang.cloud.usercenter.dao.domain.UserRightsCache;
import cn.mingyang.cloud.usercenter.dao.entity.ProductInfo;
import cn.mingyang.cloud.usercenter.dao.entity.SellPointInfo;
import cn.mingyang.cloud.usercenter.dao.entity.UserRightsRecord;
import cn.mingyang.cloud.usercenter.dao.mapper.ProductMapper;
import cn.mingyang.cloud.usercenter.dao.mapper.RightsMapper;
import cn.mingyang.cloud.usercenter.request.GetProductInfoRequest;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 缓存服务类
 */
@Slf4j
@Service
public class DbCacheService {

    /**
     * 获取人民币汇率地址
     */
    @Value("${fx.cny.url:https://v6.exchangerate-api.com/v6/************************/latest/CNY}")
    private String fxCnyUrl;

    private static final String FX_RATES_KEY = "fx:cny:rates";
    private static final long FX_CACHE_TTL_DAYS = 1L;

    private final ProductMapper productMapper;
    private final RightsMapper rightsMapper;
    private final RedisService redisService;
    private final CloseableHttpClient httpClient;

    public DbCacheService(ProductMapper productMapper, RightsMapper rightsMapper, RedisService redisService, CloseableHttpClient httpClient) {
        this.productMapper = productMapper;
        this.rightsMapper = rightsMapper;
        this.redisService = redisService;
        this.httpClient = httpClient;
    }

    /**
     * 根据应用ID查询产品信息（带缓存）
     *
     * @param appid          应用ID
     * @param osid           操作系统ID
     * @param productRequest 产品查询请求
     * @param pid            数据源ID
     * @return 产品信息列表
     */
    @Cacheable(value = CacheConfig.PRODUCT_INFO_CACHE,
            key = "#pid + ':' + #appid + ':' + #osid + ':' + #productRequest.getCacheKey()",
            unless = "#result == null or #result.isEmpty()")
    @DS("#pid")
    public List<ProductInfo> getProductInfoByAppid(String pid, String appid, String osid, GetProductInfoRequest productRequest) {
        log.debug("Querying product info from database - appid: {}, osid: {}, pid: {}", appid, osid, pid);
        List<ProductInfo> result = productMapper.getProductInfoByAppid(appid, osid, productRequest);
        log.debug("Retrieved {} products from database", result != null ? result.size() : 0);
        return result;
    }

    /**
     * 根据产品ID查询卖点信息（带缓存）
     *
     * @param productId 产品ID
     * @param pid       数据源ID
     * @return 卖点信息列表
     */
    @Cacheable(value = CacheConfig.PRODUCT_SELL_POINT_CACHE,
            key = "#pid + ':' + #productId",
            unless = "#result == null or #result.isEmpty()")
    @DS("#pid")
    public List<SellPointInfo> getSellPointByProductId(String pid, int productId) {
        log.debug("Querying sell points from database - productId: {}, pid: {}", productId, pid);
        List<SellPointInfo> result = productMapper.getSellPointByProductId(productId);
        log.debug("Retrieved {} sell points from database", result != null ? result.size() : 0);
        return result;
    }

    /**
     * 根据产品ID查询VIP信息（带缓存）
     *
     * @param productId 产品ID
     * @param pid       数据源ID
     * @return VIP信息
     */
    @Cacheable(value = CacheConfig.PRODUCT_INFO_CACHE,
            key = "#pid + ':' + #productId",
            unless = "#result == null")
    @DS("#pid")
    public ProductDoMain getProductType(String pid, int productId) {
        log.debug("Querying product type from database - productId: {}, pid: {}", productId, pid);
        ProductDoMain result = productMapper.getProductType(productId);
        log.debug("Retrieved product type from database: {}", result != null ? "found" : "not found");
        return result;
    }

    /**
     * 获取免费权益（带缓存）
     *
     * @param appid 应用ID
     * @param pid   数据源ID
     * @return 卖点信息列表
     */
    @Cacheable(value = CacheConfig.PRODUCT_SELL_POINT_CACHE,
            key = "#pid + ':' + #appid",
            unless = "#result == null or #result.isEmpty()")
    @DS("#pid")
    public List<SellPointInfo> getGiftSellPoint(String pid, String appid) {
        log.debug("Querying gift sell points from database - appid: {}, pid: {}", appid, pid);
        List<SellPointInfo> result = productMapper.getGiftSellPoint(List.of(appid, Constant.DEFAULT));
        log.debug("Retrieved {} gift sell points from database", result != null ? result.size() : 0);
        return result;
    }


    /**
     * 获取所有卖点信息（带缓存）
     *
     * @param pid 数据源ID
     * @return 卖点信息列表
     */
    @DS("#pid")
    public List<SellPointInfo> getAllSellPoint(String pid, List<Integer> sellGroups) {
        log.debug("Get all sell points from database - pid: {}, sellGroups: {}", pid, JSONUtil.toJsonStr(sellGroups));
        //先查询缓存
        String cacheKey = CacheConfig.PRODUCT_SELL_POINT_CACHE + ":" + pid + ":ALL";
        String cacheValue = redisService.getString(cacheKey);
        List<SellPointInfo> result;
        if (StringUtils.isEmpty(cacheValue)) {
            result = rightsMapper.getSellPointBySellGroup(sellGroups);
            if (CollectionUtil.isNotEmpty(result)) {
                redisService.set(cacheKey, JSONUtil.toJsonStr(result), 5 * 60, TimeUnit.SECONDS);
            }
        } else {
            result = JSONUtil.toList(cacheValue, SellPointInfo.class);
        }
        if (CollectionUtil.isEmpty(sellGroups)) {
            return result;
        }
        return result.stream().filter(sp -> sellGroups.contains(sp.getSellgroup())).collect(Collectors.toList());
    }

    /**
     * 查询新的产品信息
     *
     * @param product 产品信息
     * @return 产品列表
     */
    @DS("#pid")
    public List<ProductDoMain> queryNewProduct(String pid, ProductDoMain product) {
        log.debug("Querying new products from database - productId: {}, productName: {}",
                product.getProductid(), product.getProductname());
        List<ProductDoMain> result = productMapper.queryNewProduct(product);
        log.debug("Retrieved {} new products from database", result != null ? result.size() : 0);
        return result;
    }

    /**
     * 权益重置锁
     *
     * @return 结果
     */
    public boolean checkRested(String userFlag) {
        //尝试获取重置锁
        String redisKey = UserRightsCache.userRightCacheReset + ":" + userFlag;
        return redisService.tryLock(redisKey, userFlag, 60 * 10);
    }

    /**
     * 释放重置锁
     */
    public boolean unlockRested(String userFlag) {
        //尝试获取重置锁
        String redisKey = UserRightsCache.userRightCacheReset + ":" + userFlag;
        return redisService.unlock(redisKey, userFlag);
    }

    /**
     * 将今日需要同步的用户写入到Redis列表
     *
     * @return 结果
     */
    public void userRightsCacheSync(String userFlag) {
        //今日需要同步到数据库的用户
        String redisKey = UserRightsCache.userRightCacheSync + "_" + DateUtil.today();
        boolean isExits = redisService.hasKey(redisKey);
        if (!isExits) {
            redisService.sAddWithExpire(redisKey, 48, TimeUnit.HOURS, userFlag);
        } else {
            //判断是否已有
            boolean isReset = redisService.sIsMember(redisKey, userFlag);
            if (!isReset) {
                redisService.sAdd(redisKey, userFlag);
            }
        }
    }


    /**
     * 查询记录
     *
     * @param pid           业务ID
     * @param recordRequest 查询参数
     * @return 记录列表
     */
    @DS("#pid")
    public List<UserRightsRecord> getUserRightsRecords(String pid, UserRightsRecord recordRequest) {
        return rightsMapper.selectRightRecord(recordRequest);
    }

    /**
     * 获取现有缓存
     */
    public UserRightsCache getExistingCache(String cacheKey) {
        String cacheValue = null;
        try {
            cacheValue = redisService.getString(cacheKey);
            if (cacheValue == null || com.alibaba.druid.util.StringUtils.isEmpty(cacheValue)) {
                log.debug("No existing cache found for key: {}", cacheKey);
                return null;
            }
            return JSONUtil.toBean(cacheValue, UserRightsCache.class);
        } catch (Exception e) {
            log.error("Get rights cache to json is error, cacheValue: {} ", cacheValue, e);
            return null;
        }
    }

    /**
     * 保存缓存
     */
    public void saveCache(String cacheKey, UserRightsCache cache) {
        redisService.delete(cacheKey);
        redisService.set(cacheKey, JSONUtil.toJsonStr(cache), 30, TimeUnit.DAYS);
        log.debug("Saved cache for key: {} cache: {}", cacheKey, JSONUtil.toJsonStr(cache));
    }

    /**
     * 根据别名查询到对应的商品
     *
     * @param pid   业务ID
     * @param alias 商品别名
     * @return 记录列表
     */
    @DS("#pid")
    public List<ProductDoMain> getProductByAlias(String pid, String alias) {
        return productMapper.getProductByAlias(alias);
    }


    /**
     * 获取 CNY -> * 汇率（带 Redis 缓存，缓存 1 天）
     * 优化版本：更好的错误处理、缓存策略和性能
     */
    public BigDecimal getCnyRate(String forwardCurrency) {

        // CNY -> CNY 直接返回 1
        if (Constant.CurrencyType.CNY.equalsIgnoreCase(forwardCurrency)) {
            return BigDecimal.ONE;
        }

        try {
            // 1. 优先从缓存获取汇率数据
            String cacheRates = redisService.getString(FX_RATES_KEY);
            JSONObject rates;

            if (StrUtil.isNotBlank(cacheRates)) {
                rates = JSONUtil.parseObj(cacheRates);
                log.debug("从缓存获取汇率数据成功，货币: {}", forwardCurrency);
            } else {
                // 2. 缓存未命中，从API获取并缓存
                rates = fetchAndCacheExchangeRates();
                if (rates == null) {
                    return getDefaultRate(forwardCurrency);
                }
            }

            // 3. 计算汇率：forwardCurrency -> CNY = 1 / (CNY -> forwardCurrency)
            BigDecimal cnyToTarget = rates.getBigDecimal(forwardCurrency.toUpperCase());
            if (isValidRate(cnyToTarget)) {
                BigDecimal result = BigDecimal.ONE.divide(cnyToTarget, 6, RoundingMode.HALF_UP);
                log.debug("汇率计算成功: {} -> CNY = {}", forwardCurrency, result);
                return result;
            } else {
                log.warn("汇率数据无效，货币: {}, 值: {}, 使用默认汇率", forwardCurrency, cnyToTarget);
                return getDefaultRate(forwardCurrency);
            }

        } catch (Exception e) {
            log.error("获取 {} -> CNY 汇率异常，使用默认汇率", forwardCurrency, e);
            return getDefaultRate(forwardCurrency);
        }
    }

    /**
     * 从API获取汇率数据并缓存
     */
    private JSONObject fetchAndCacheExchangeRates() {
        try {
            log.info("开始从API获取汇率数据");

            HttpGet get = new HttpGet(fxCnyUrl);
            String responseBody = httpClient.execute(get, response -> {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode != 200) {
                    throw new RuntimeException("汇率API响应异常，状态码: " + statusCode);
                }
                return EntityUtils.toString(response.getEntity(), "UTF-8");
            });

            if (StrUtil.isBlank(responseBody)) {
                log.warn("汇率API响应为空");
                return null;
            }

            JSONObject json = JSONUtil.parseObj(responseBody);
            JSONObject rates = json.getJSONObject("conversion_rates");

            if (rates == null || rates.isEmpty()) {
                log.warn("汇率API响应中缺少conversion_rates字段: {}", responseBody);
                return null;
            }

            // 缓存汇率数据
            redisService.set(FX_RATES_KEY, rates.toString(), FX_CACHE_TTL_DAYS, TimeUnit.DAYS);
            log.info("汇率数据获取并缓存成功，货币数量: {}", rates.size());

            return rates;

        } catch (Exception e) {
            log.error("从API获取汇率数据失败", e);
            return null;
        }
    }

    /**
     * 验证汇率是否有效
     */
    private boolean isValidRate(BigDecimal rate) {
        return rate != null && rate.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 获取默认汇率
     */
    private BigDecimal getDefaultRate(String forwardCurrency) {
        if (Constant.CurrencyType.USD.equalsIgnoreCase(forwardCurrency)) {
            return BigDecimal.valueOf(7.0); // USD -> CNY 默认汇率
        } else {
            return BigDecimal.ONE; // 其他货币默认返回1.0
        }
    }

}