package cn.mingyang.cloud.usercenter.dao.domain;


import lombok.Data;


/**
 * 鸿蒙支付订单信息
 */
@Data
public class HarmonyPayInfo {

    // -------------------------- 基础必选参数 --------------------------
    /**
     * 环境类型（必选）
     * 取值：NORMAL（生产环境）、SANDBOX（沙盒环境）
     */
    private String environment;

    /**
     * 购买订单号ID（必选）
     * 具体一笔订单对应的唯一订单标识，最大长度256字符
     */
    private String purchaseOrderId;

    /**
     * 购买token（必选）
     * 说明：1. 消耗型/非消耗型/非续期订阅商品场景：与具体购买订单一一对应；
     * 2. 自动续期订阅商品场景：与订阅ID一一对应；
     * 3. 限制：一个购买令牌只能发货一次，需做发货次数限制，避免多次发货造成损失；
     * 4. 最大长度256字符
     */
    private String purchaseToken;

    /**
     * 应用ID（必选）
     * 获取方式参见华为开发者文档「应用信息」
     */
    private String applicationId;

    /**
     * 商品ID（必选）
     * 开发者在AppGallery Connect中配置的商品唯一标识
     */
    private String productId;

    /**
     * 商品类型（必选）
     * 取值：0（消耗型商品）、1（非消耗型商品）、2（自动续期订阅商品）、3（非续期订阅商品）
     */
    private String productType;

    /**
     * 购买时间（必选）
     * UTC时间戳，单位：毫秒；若未完成购买，则该字段无值（为null）
     */
    private Long purchaseTime;

    /**
     * 价格（必选）
     * 单位：分（如100表示1元）
     */
    private Long price;

    /**
     * 币种（必选）
     * 遵循ISO 4217标准，例如：CNY（人民币）、USD（美元）、MYR（马来西亚林吉特）
     */
    private String currency;

    /**
     * 国家/地区码（必选）
     * 遵循ISO 3166标准，用于区分用户所在国家/地区
     */
    private String countryCode;

    /**
     * 签名时间（必选）
     * UTC时间戳，单位：毫秒；用于校验订单数据的有效性
     */
    private Long signedTime;

    // -------------------------- 非必选参数 --------------------------
    /**
     * 购买商品数量（非必选）
     * 适用场景：仅支持消耗型商品和非续期订阅型商品的批量购买；
     * 注意：若使用该参数，需在发货时校验「下单数量」与「最终发货数量」一致性，避免漏发/多发；
     * 元服务支持：从版本5.0.3(15)开始，元服务API支持该参数
     */
    private Long quantity;

    /**
     * 发货状态（非必选）
     * 取值：1（已发货）、2（未发货）
     */
    private String finishStatus;

    /**
     * 是否需要确认发货（非必选）
     * 取值：true（必须确认发货才能完成购买）、false（可选确认发货完成购买）
     */
    private Boolean needFinish;

    /**
     * 商户侧保留信息（非必选）
     * 由应用调用支付接口时传入，用于商户自定义业务逻辑（如订单关联的业务参数）
     */
    private String developerPayload;

    /**
     * 购买订单撤销原因码（非必选）
     * 取值：0（其他）、1（用户遇到问题退款）；仅订单撤销时该字段有值
     */
    private String purchaseOrderRevocationReasonCode;

    /**
     * 购买订单撤销时间（非必选）
     * UTC时间戳，单位：毫秒；仅订单撤销时该字段有值
     */
    private Long revocationTime;

    /**
     * 优惠类型（非必选）
     * 取值：1（推介促销）、2（优惠促销）、4（挽留促销）
     */
    private String offerTypeCode;

    /**
     * 优惠ID（非必选）
     * 关联开发者配置的优惠策略，与 OfferInfo 中的 offerId 对应
     */
    private String offerId;

    // -------------------------- 自动续期订阅商品专属参数（仅该场景返回） --------------------------
    /**
     * 订阅组的代ID（必选，仅自动续期订阅商品场景返回）
     * 规则：1. 用户切换订阅商品时，该ID不变；
     * 2. 订阅失效且超出保留期后，用户重新购买时，该ID改变
     */
    private String subGroupGenerationId;

    /**
     * 商品的订阅ID（必选，仅自动续期订阅商品场景返回）
     * 规则：1. 用户切换订阅商品时，该ID改变；
     * 2. 订阅失效且超出保留期后，用户重新购买时，该ID改变
     */
    private String subscriptionId;

    /**
     * 订阅组ID（必选，仅自动续期订阅商品场景返回）
     * 开发者在AppGallery Connect中配置的订阅组唯一标识
     */
    private String subGroupId;

    /**
     * 购买有效周期（必选，仅自动续期订阅商品场景返回）
     * 遵循ISO 8601格式，例如：P1W（一周）、P1M（一个月）
     */
    private String duration;

    /**
     * 订阅周期段类型（必选，仅自动续期订阅商品场景返回）
     * 取值：0（正常周期段）、1（延期段）
     */
    private String durationTypeCode;

}
