package cn.mingyang.cloud.usercenter.dao.entity;


import lombok.Data;


/**
 * 产品价格表
 */
@Data
public class ProductPriceInfo {

    // 价格主键
    private Integer priceid;

    // 商品id
    private Integer productid;

    // 原始价格
    private Float originalPrice;

    // 实付价格
    private Float realPrice;

    // 首次优惠价格
    private Float firstPromotePrice;

    // 是否自动订阅：0=不是，1=是
    private Integer isSubscribe;

    // appid
    private String appid;

    // df（需确认业务含义，若无特殊说明保持字段名）
    private String df;

    // 币种
    private String currency;

    // osid
    private String osid;

    // 商品版本
    private Integer version;

    // 商品别名
    private String alias;
}
