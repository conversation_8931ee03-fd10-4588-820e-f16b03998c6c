package cn.mingyang.cloud.usercenter.comEnum;

import lombok.Getter;

import java.util.List;

/**
 * 通联支付类型明细枚举
 */
@Getter
public enum UnionPayEnum {
    W01("W01", "微信扫码支付"),
    W02("W02", "微信JS支付"),
    W03("W03", "微信APP支付"),
    W06("W06", "微信小程序支付"),
    W11("W11", "微信订单支付"),
    A01("A01", "支付宝扫码支付"),
    A02("A02", "支付宝JS支付"),
    A03("A03", "支付宝APP支付"),
    U01("U01", "银联扫码支付(CSB)"),
    U02("U02", "银联JS支付"),
    S01("S01", "数币扫码支付"),
    S03("S03", "数字货币H5/APP"),
    N03("N03", "网联支付");

    private final String code;


    private final String desc;


    UnionPayEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static UnionPayEnum fromCode(String code) {
        for (UnionPayEnum type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 将微信支付或者支付宝支付转换为通联支付方式
     *
     * @param code 支付编码
     * @return 结果
     */
    public static UnionPayEnum unionPayForwardWxOrAli(String code) {
        if (code.equals(PayTypeDetail.WX_BAR.getCode())) {
            return W01;
        }
        if (code.equals(PayTypeDetail.WX_JSAPI.getCode())) {
            return W02;
        }
        if (List.of(PayTypeDetail.WX_LITE.getCode(), PayTypeDetail.WX_H5.getCode()).contains(code)) {
            return W06;
        }
        if (code.equals(PayTypeDetail.WX_APP.getCode())) {
            return W03;
        }
        if (code.equals(PayTypeDetail.ALI_BAR.getCode())) {
            return A01;
        }
        if (List.of(PayTypeDetail.ALI_JSAPI.getCode(), PayTypeDetail.ALI_LITE.getCode()).contains(code)) {
            return A02;
        }
        if (code.equals(PayTypeDetail.ALI_APP.getCode())) {
            return A03;
        }
        return null;
    }

}
