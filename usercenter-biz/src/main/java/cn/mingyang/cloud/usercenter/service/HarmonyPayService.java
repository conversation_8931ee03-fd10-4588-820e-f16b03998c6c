package cn.mingyang.cloud.usercenter.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.comEnum.BaseErrorCodeEnum;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import cn.mingyang.cloud.usercenter.comEnum.PayType;
import cn.mingyang.cloud.usercenter.dao.domain.HarmonyNoticeParam;
import cn.mingyang.cloud.usercenter.dao.domain.HarmonyPayInfo;
import cn.mingyang.cloud.usercenter.dao.domain.UserDetail;
import cn.mingyang.cloud.usercenter.dao.entity.OrgUserOrder;
import cn.mingyang.cloud.usercenter.dao.entity.PayConfig;
import cn.mingyang.cloud.usercenter.dao.mapper.PayMapper;
import cn.mingyang.cloud.usercenter.request.PayOrderRequest;
import cn.mingyang.cloud.usercenter.response.PayOrderResponse;
import cn.mingyang.cloud.usercenter.uitls.HarmonyPayUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 鸿蒙支付服务
 */
@Slf4j
@Service
public class HarmonyPayService {

    /**
     * JWT根证书路径
     */
    @Value("${har.jwt.root.path:/home/<USER>/payment_certs/har/RootG2Ca.cer}")
    private String jwtRootPath;

    /**
     * 有效时间
     */
    @Value("${har.active.time.second:3600}")
    private long activeTimeSecond;

    private final PayMapper payMapper;
    private final OrderService orderService;
    private final CloseableHttpClient httpClient;

    public HarmonyPayService(PayMapper payMapper, OrderService orderService, CloseableHttpClient httpClient) {
        this.payMapper = payMapper;
        this.orderService = orderService;
        this.httpClient = httpClient;
    }

    /**
     * 鸿蒙内购统一下单
     */
    @DS("#cmd.ds")
    @Transactional
    public CommonResult<?> harmonyUnifiedOrder(CmdRequest cmd, ComRequest com, PayOrderRequest payRequest) {
        try {
            String appId = cmd.getAppId();
            log.info("鸿蒙内购统一下单开始, appId: {}, paycode: {}", appId, payRequest.getPaycode());

            // 获取支付配置
            PayConfig payConfig = payMapper.getPayChannelConfig(appId, PayType.HARMONY.getCode());
            if (payConfig == null) {
                payConfig = payMapper.getPayChannelConfig("DEFAULT", PayType.HARMONY.getCode());
                if (payConfig == null) {
                    return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(),
                            "没有找到当前应用: " + appId + " 的鸿蒙内购配置");
                }
            }

            // 创建支付订单
            OrgUserOrder orgUserOrder = orderService.createPayOrder(
                    payRequest, cmd, com, PayType.HARMONY.getIndex(), payConfig.getMchid());
            log.info("鸿蒙内购订单创建成功, outTradeNo: {}", orgUserOrder.getOutTradeNo());

            // 构建返回给客户端的数据
            PayOrderResponse payOrderResponse = new PayOrderResponse();
            payOrderResponse.setOuttradeno(orgUserOrder.getOutTradeNo());
            payOrderResponse.setPlatformappid(payConfig.getPlatformappid());
            payOrderResponse.setPlatformmchid(payConfig.getMchid());
            payOrderResponse.setTimestamp(String.valueOf(System.currentTimeMillis() / 1000));

            // 鸿蒙内购客户端需要的数据
            Map<String, String> payData = new HashMap<>();
            payData.put("productId", String.valueOf(payRequest.getProductid()));
            payData.put("outTradeNo", orgUserOrder.getOutTradeNo());
            payData.put("harmonyAppId", payConfig.getPlatformappid());
            payOrderResponse.setPaydata(payData);

            return CommonResult.success(payOrderResponse);
        } catch (Exception e) {
            log.error("鸿蒙内购统一下单异常", e);
            return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "鸿蒙内购下单失败：" + e.getMessage());
        }
    }

    /**
     * 新增购买
     *
     * @param pid         业务标识
     * @param payConfig   支付配置
     * @param noticeParam noticeParam 通知参数
     * @return 用户详情
     */
    @DS("#pid")
    public UserDetail createOrderAndAddRights(String pid, PayConfig payConfig, HarmonyNoticeParam noticeParam) {
        try {
            if (noticeParam == null || noticeParam.getNotificationMetaData() == null) {
                log.error("[鸿蒙内购] createOrderAndAddRights 参数不完整 pid={} noticeParam={}", pid, JSONUtil.toJsonStr(noticeParam));
                return null;
            }

            // 1. 查询华为订单详情，优先获取 developerPayload 中的我方 outTradeNo
            Map<String, Object> bodyMap = new HashMap<>();
            bodyMap.put("purchaseOrderId", noticeParam.getNotificationMetaData().getPurchaseOrderId());
            bodyMap.put("purchaseToken", noticeParam.getNotificationMetaData().getPurchaseToken());
            HarmonyPayInfo payInfo = callHarmonyService("query", payConfig, bodyMap);
            if (payInfo == null) {
                log.error("[鸿蒙内购] 查询订单详情失败 pid={} meta={}", pid, JSONUtil.toJsonStr(noticeParam.getNotificationMetaData()));
                return null;
            }

            String outTradeNo = null;
            String developerPayload = payInfo.getDeveloperPayload();
            if (StrUtil.isNotBlank(developerPayload)) {
                // 兼容 JSON/纯文本 两种形式
                if (JSONUtil.isTypeJSON(developerPayload)) {
                    JSONObject payloadObj = JSONUtil.parseObj(developerPayload);
                    String candidate = payloadObj.getStr("outTradeNo");
                    if (StrUtil.isNotBlank(candidate)) {
                        outTradeNo = candidate;
                    } else {
                        candidate = payloadObj.getStr("outtradeno");
                        if (StrUtil.isNotBlank(candidate)) {
                            outTradeNo = candidate;
                        }
                    }
                } else {
                    outTradeNo = developerPayload;
                }
            }


            // 3. 按我方订单号处理支付与权益
            UserDetail userDetail = orderService.handlePayNotice(pid, outTradeNo, PayType.HARMONY.getCode());
            if (userDetail != null) {
                userDetail.setPid(pid);
                userDetail.setOutTradeNo(outTradeNo);
            }

            // 4. 确认收货
            bodyMap.put("purchaseOrderId", payInfo.getPurchaseOrderId());
            bodyMap.put("purchaseToken", payInfo.getPurchaseToken());
            callHarmonyService("confirm", payConfig, bodyMap);
            return userDetail;
        } catch (Exception ex) {
            log.error("[鸿蒙内购] createOrderAndAddRights 处理异常 pid={} param={}", pid, JSONUtil.toJsonStr(noticeParam), ex);
            return null;
        }
    }


    /**
     * 调用鸿蒙支付平台
     *
     * @param sendType  请求类型
     * @param payConfig 支付参数
     * @param bodyMap   通知内容
     * @return 订单详情
     */
    private HarmonyPayInfo callHarmonyService(String sendType, PayConfig payConfig, Map<String, Object> bodyMap) {
        try {
            // 1. 初始化转发URL
            String url = "https://iap.cloud.huawei.com/order/harmony/v1/application/order/status/query";
            if ("confirm".equals(sendType)) {
                url = "https://iap.cloud.huawei.com/order/harmony/v1/application/purchase/shipped/confirm";
            }
            // 2. 发送HTTP请求
            String jwtBody = JSONUtil.toJsonStr(bodyMap);
            HttpPost httpPost = new HttpPost(url);
            httpPost.setEntity(new StringEntity(jwtBody, "UTF-8"));
            httpPost.setHeader("Content-Type", "application/json");
            JSONObject obj = JSONUtil.parseObj(payConfig.getConfigvalues());
            String kid = obj.getStr("kid");
            String iss = obj.getStr("iss");
            httpPost.setHeader("Authorization",
                    "Bearer " + HarmonyPayUtils.genJwtToken(kid, iss, payConfig.getPlatformappid(), payConfig.getPrivatekey(), this.activeTimeSecond, jwtBody));

            String response = httpClient.execute(httpPost, response1 -> EntityUtils.toString(response1.getEntity(), "UTF-8"));
            if (StrUtil.isBlank(response)) {
                log.error("[鸿蒙内购] 平台响应为空 url={} body={}", url, jwtBody);
                return null;
            }
            JSONObject respObj = JSONUtil.parseObj(response);
            // 兼容 data 根或直出
            JSONObject data = respObj.getObj("jwsPurchaseOrder") instanceof JSONObject ? respObj.getJSONObject("jwsPurchaseOrder") : respObj;
            HarmonyPayInfo payInfo = JSONUtil.toBean(data, HarmonyPayInfo.class);
            log.info("[鸿蒙内购] 平台响应 sendType={} jwsPurchaseOrder={}", sendType, data);
            return payInfo;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 订阅状态发生改变
     *
     * @param noticeParam 参数
     */
    public void didChangeRenewalStatus(HarmonyNoticeParam noticeParam) {
        try {
            if (noticeParam == null) {
                log.error("[鸿蒙内购] didChangeRenewalStatus 参数为空");
                return;
            }
            log.info("[鸿蒙内购] didChangeRenewalStatus: {}", JSONUtil.toJsonStr(noticeParam));
            // 可在此更新订阅续订状态（是否自动续订等），需结合现有订阅表结构落库
        } catch (Exception ex) {
            log.error("[鸿蒙内购] didChangeRenewalStatus 处理异常", ex);
        }
    }

    /**
     * 订单退款/撤销订阅
     *
     * @param noticeParam 参数
     */
    public void revoke(HarmonyNoticeParam noticeParam) {
        try {
            log.info("[鸿蒙内购] revoke 收到撤销/退款,  param={}", JSONUtil.toJsonStr(noticeParam));
            // 这里可以实现：
            // 1) 撤销用户对应权益
            // 2) 标记订单为退款/撤销
            // 如需调用内部订单退款流程，请在 OrderService 中增加相应方法
        } catch (Exception ex) {
            log.error("[鸿蒙内购] revoke 处理异常", ex);
        }
    }

    /**
     * 订阅过期时间调整
     *
     * @param noticeParam 参数
     */
    public void renewalTimeModified(HarmonyNoticeParam noticeParam) {
        try {
            if (noticeParam == null) {
                log.error("[鸿蒙内购] renewalTimeModified 参数为空");
                return;
            }
            log.info("[鸿蒙内购] renewalTimeModified: {}", JSONUtil.toJsonStr(noticeParam));
            // 如果 noticeParam 中含有新的续费时间字段，可在此更新订阅的 nextDeductTime
            // 需要结合现有订阅模型（PaySubscribe）以及 OrderService 的更新方法
        } catch (Exception ex) {
            log.error("[鸿蒙内购] renewalTimeModified 处理异常", ex);
        }
    }

    /**
     * 订阅已过期
     *
     * @param noticeParam 参数
     */
    public void expire(HarmonyNoticeParam noticeParam) {
        try {
            log.info("[鸿蒙内购] expire: {}", JSONUtil.toJsonStr(noticeParam));
            // 到期处理：可在此关闭用户订阅权益或标记过期状态
        } catch (Exception ex) {
            log.error("[鸿蒙内购] expire 处理异常", ex);
        }
    }


    /**
     * 处理鸿蒙内购支付相关事件
     */
    private String handleHarmonyPayment(String pid, String orderId, String eventType) {
        try {
            // 遍历业务ID,查询订单信息
            OrgUserOrder userOrder = orderService.getUserOrder(pid, orderId);
            if (null == userOrder) {
                for (String aPid : Constant.AllDs.ALL) {
                    userOrder = orderService.getUserOrder(aPid, orderId);
                    if (null != userOrder) {
                        pid = aPid;
                        log.info("[鸿蒙内购支付] 业务标识转化， 原pid：{} 转pid: {}", pid, aPid);
                        break;
                    }
                }
            }

            if (null == userOrder) {
                log.error("[鸿蒙内购支付] 订单不存在, orderId={}, eventType={}", orderId, eventType);
                return "fail";
            }

            // 处理支付通知
            UserDetail userDetail = orderService.handlePayNotice(pid, userOrder.getOutTradeNo(), PayType.HARMONY.getCode());
            if (null != userDetail) {
                //initUserRights(pid, userDetail);
                log.info("[鸿蒙内购支付] 支付处理成功, orderId={}, eventType={}, outTradeNo={}",
                        orderId, eventType, userOrder.getOutTradeNo());
            }

            return "success";
        } catch (Exception e) {
            log.error("[鸿蒙内购支付] 处理异常, orderId={}, eventType={}", orderId, eventType, e);
            return "fail";
        }
    }

    private String handlePaymentByAnyId(String orderId, String purchaseToken, String eventType) {
        String key = StrUtil.blankToDefault(orderId, purchaseToken);
        if (StrUtil.isBlank(key)) {
            log.error("[鸿蒙内购] 缺少订单标识, eventType={}", eventType);
            return "fail";
        }
        LocatedOrder located = locateOrderAcrossBusinesses(key);
        if (located == null || located.order == null) {
            log.error("[鸿蒙内购] 未找到订单, key={}, eventType={}", key, eventType);
            return "fail";
        }
        return handleHarmonyPayment(located.pid, located.order.getOutTradeNo(), eventType);
    }

    /**
     * 在所有业务数据源中按订单号定位订单
     */
    private LocatedOrder locateOrderAcrossBusinesses(String orderId) {
        try {
            // 优先尝试默认PID
            OrgUserOrder order = orderService.getUserOrder(Constant.DEFAULT, orderId);
            if (order != null) {
                return new LocatedOrder(Constant.DEFAULT, order);
            }
            for (String aPid : Constant.AllDs.ALL) {
                order = orderService.getUserOrder(aPid, orderId);
                if (order != null) {
                    return new LocatedOrder(aPid, order);
                }
            }
        } catch (Exception ex) {
            log.error("[鸿蒙内购] 跨业务定位订单异常, orderId={}", orderId, ex);
        }
        return null;
    }

    private static class LocatedOrder {
        final String pid;
        final OrgUserOrder order;

        LocatedOrder(String pid, OrgUserOrder order) {
            this.pid = pid;
            this.order = order;
        }
    }


    /**
     * 处理鸿蒙内购统一通知
     * 根据eventType字段区分不同类型的通知
     * 参考华为官方文档：
     * <a href="https://developer.huawei.com/consumer/cn/doc/harmonyos-references/iap-key-event-notifications">...</a>
     *
     * @param pid        业务标识
     * @param notifyData 通知数据
     * @return 处理结果
     */
    public JSONObject handleHarmonyPayNotify(String pid, String notifyData) {
        try {
            JSONObject notify = JSONUtil.parseObj(notifyData);
            log.info("[鸿蒙内购] 处理通知, pid: {}, 通知数据: {}", pid, notify);

            // 解析通知数据（按照华为官方文档的字段）
            String notificationId = notify.getStr("notificationId");
            String eventType = notify.getStr("eventType");
            String eventTime = notify.getStr("eventTime");
            String version = notify.getStr("version");

            // 获取业务相关字段
            String purchaseToken = notify.getStr("purchaseToken");
            String orderId = notify.getStr("orderId");
            String productId = notify.getStr("productId");
            String subscriptionId = notify.getStr("subscriptionId");

            // 验证必要字段
            if (StrUtil.isBlank(notificationId) || StrUtil.isBlank(eventType) || StrUtil.isBlank(eventTime)) {
                log.error("[鸿蒙内购] 通知缺少必要字段, notificationId: {}, eventType: {}, eventTime: {}",
                        notificationId, eventType, eventTime);
                return null;
            }

            // 根据事件类型获取相应的业务字段
            JSONObject result = new JSONObject();
            result.set("notificationId", notificationId);
            result.set("eventType", eventType);
            result.set("eventTime", eventTime);
            result.set("version", version);

            // 根据事件类型设置相应的业务字段
            switch (eventType) {
                case "INITIAL_BUY":
                case "RENEWAL":
                case "INTERACTIVE_RENEWAL":
                case "RESTORE":
                    // 购买相关事件
                    if (StrUtil.isNotBlank(purchaseToken)) {
                        result.set("purchaseToken", purchaseToken);
                    }
                    if (StrUtil.isNotBlank(productId)) {
                        result.set("productId", productId);
                    }
                    // 从purchaseToken中提取订单ID，或者使用其他字段
                    if (StrUtil.isNotBlank(purchaseToken)) {
                        result.set("orderId", purchaseToken); // 或者根据实际业务逻辑处理
                    }
                    break;

                case "CANCEL":
                case "BILLING_RETRY":
                case "PRICE_CHANGE":
                case "ACCOUNT_HOLD":
                case "GRACE_PERIOD":
                    // 订阅状态变更事件
                    if (StrUtil.isNotBlank(subscriptionId)) {
                        result.set("subscriptionId", subscriptionId);
                        result.set("orderId", subscriptionId); // 使用subscriptionId作为订单标识
                    }
                    break;

                case "REFUND":
                case "REFUND_DECLINED":
                    // 退款相关事件
                    if (StrUtil.isNotBlank(purchaseToken)) {
                        result.set("purchaseToken", purchaseToken);
                        result.set("orderId", purchaseToken);
                    }
                    break;

                case "CONSUMPTION_REQUEST":
                    // 消费请求事件
                    if (StrUtil.isNotBlank(purchaseToken)) {
                        result.set("purchaseToken", purchaseToken);
                        result.set("orderId", purchaseToken);
                    }
                    break;

                default:
                    log.warn("[鸿蒙内购] 未知事件类型: {}, notificationId: {}", eventType, notificationId);
                    // 尝试获取通用字段
                    if (StrUtil.isNotBlank(purchaseToken)) {
                        result.set("purchaseToken", purchaseToken);
                        result.set("orderId", purchaseToken);
                    }
                    if (StrUtil.isNotBlank(subscriptionId)) {
                        result.set("subscriptionId", subscriptionId);
                    }
                    break;
            }

            log.info("[鸿蒙内购] 解析后的通知数据: {}", result);
            return result;
        } catch (Exception e) {
            log.error("[鸿蒙内购] 处理通知异常", e);
            return null;
        }
    }


    /**
     * 根据事件类型判断是否需要处理支付逻辑
     *
     * @param eventType 事件类型
     * @return 是否需要处理支付
     */
    public boolean shouldProcessPayment(String eventType) {
        if (StrUtil.isBlank(eventType)) {
            return false;
        }

        // 需要处理支付的事件类型
        return Arrays.asList(
                "INITIAL_BUY",      // 首次购买
                "RENEWAL",          // 续费
                "INTERACTIVE_RENEWAL", // 交互式续费
                "RESTORE"           // 恢复购买
        ).contains(eventType);
    }

    /**
     * 根据事件类型判断是否需要处理订阅逻辑
     *
     * @param eventType 事件类型
     * @return 是否需要处理订阅
     */
    public boolean shouldProcessSubscription(String eventType) {
        if (StrUtil.isBlank(eventType)) {
            return false;
        }

        // 需要处理订阅的事件类型
        return Arrays.asList(
                "INITIAL_BUY",      // 首次购买
                "RENEWAL",          // 续费
                "INTERACTIVE_RENEWAL", // 交互式续费
                "CANCEL",           // 取消订阅
                "RESTORE"           // 恢复购买
        ).contains(eventType);
    }


    /**
     * 验证并解析华为IAP的JWS通知
     * 文档参考：
     * <a href="https://developer.huawei.com/consumer/cn/doc/harmonyos-references/iap-key-event-notifications">...</a>
     * <p>
     * 配置要求：在 PayConfig.configvalues 中配置字段 iapPublicKey（X509 PEM 公钥）
     *
     * @param jwsNotification 紧凑JWS串（header.payload.signature）
     * @return 验签后解析的载荷对象，失败返回null
     */
    public HarmonyNoticeParam verifyAndParseJwsNotification(String jwsNotification) {
        try {
            if (StrUtil.isBlank(jwsNotification) || !jwsNotification.contains(".")) {
                log.error("[鸿蒙内购] JWS格式不正确");
                return null;
            }
            String payloadJson = HarmonyPayUtils.checkAndDecodeJWS(this.jwtRootPath, jwsNotification);
            JSONObject payload = JSONUtil.parseObj(payloadJson);
            log.info("[鸿蒙内购] JWS验签通过, payload={}", payload);
            return JSONUtil.toBean(payload, HarmonyNoticeParam.class);
        } catch (Exception ex) {
            log.error("[鸿蒙内购] 验签解析异常", ex);
            return null;
        }
    }

    /**
     * 支付订阅取消
     */
    @DS("#cmd.ds")
    public CommonResult<?> harmonyTest(CmdRequest cmd, ComRequest com) {
        try {
            // 查询支付配置
            PayConfig payConfig = payMapper.getPayChannelConfigByPayChannel("5765880207855396839", PayType.HARMONY.getCode());


            // 1. 初始化转发URL
            String url = "https://iap.cloud.huawei.com/harmony/v1/application/notifications/test";

            // 2. 发送HTTP请求
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");
            JSONObject obj = JSONUtil.parseObj(payConfig.getConfigvalues());
            String kid = obj.getStr("kid");
            String iss = obj.getStr("iss");
            httpPost.setHeader("Authorization",
                    "Bearer " + HarmonyPayUtils.genJwtToken(kid, iss, payConfig.getPlatformappid(), payConfig.getPrivatekey(), this.activeTimeSecond, ""));

            String response = httpClient.execute(httpPost, response1 -> EntityUtils.toString(response1.getEntity(), "UTF-8"));
            if (StrUtil.isBlank(response)) {
                log.error("[鸿蒙内购] 平台响应为空 url={} body={}", url, "");
                return null;
            }
            JSONObject respObj = JSONUtil.parseObj(response);
            // 兼容 data 根或直出
            JSONObject data = respObj.getObj("jwsPurchaseOrder") instanceof JSONObject ? respObj.getJSONObject("jwsPurchaseOrder") : respObj;
            HarmonyPayInfo payInfo = JSONUtil.toBean(data, HarmonyPayInfo.class);
            log.info("[鸿蒙内购] 平台响应  jwsPurchaseOrder={}", data);
            return null;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
