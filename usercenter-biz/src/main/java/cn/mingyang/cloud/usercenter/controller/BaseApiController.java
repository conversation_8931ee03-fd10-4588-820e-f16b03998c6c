package cn.mingyang.cloud.usercenter.controller;

import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.center.common.util.ValidateUtils;
import cn.mingyang.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.request.ApplePayVerifyRequest;
import cn.mingyang.cloud.usercenter.request.PayOrderRequest;
import cn.mingyang.cloud.usercenter.request.PaySubscribeRequest;
import cn.mingyang.cloud.usercenter.request.PaySubscribeUnSignRequest;
import cn.mingyang.cloud.usercenter.service.*;
import com.alibaba.druid.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 基础接口API控制层
 */
@Slf4j
@RestController
@RequestMapping("/base")
public class BaseApiController {


    private final BaseService baseService;

    private final WxPayService wxPayService;

    private final AliPayService aliPayService;

    private final ValidateUtils validateUtils;

    private final UnifiedPayService unifiedPayService;

    private final UnionPayService unionPayService;

    private final HarmonyPayService harmonyPayService;


    public BaseApiController(BaseService baseService, WxPayService wxPayService, AliPayService aliPayService, ValidateUtils validateUtils, UnifiedPayService unifiedPayService, UnionPayService unionPayService, HarmonyPayService harmonyPayService) {
        this.baseService = baseService;
        this.wxPayService = wxPayService;
        this.aliPayService = aliPayService;
        this.validateUtils = validateUtils;
        this.unifiedPayService = unifiedPayService;
        this.unionPayService = unionPayService;
        this.harmonyPayService = harmonyPayService;
    }


    /**
     * 发送短信验证码
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10001&*")
    public CommonResult<?> SendAuthCode(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("发送短信验证码, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.SendAuthCode(cmdRequest, comRequest);
    }


    /**
     * 统一注册 & 登录
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10002&*")
    public CommonResult<?> ThirdPartyLogin(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("统一注册 & 登录, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.ThirdPartyLogin(cmdRequest, comRequest);
    }

    /**
     * 用户信息更新
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10003&*")
    public CommonResult<?> UserUpdate(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("用户信息更新, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.UserUpdate(cmdRequest, comRequest);
    }

    /**
     * 获取用户信息
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10004&*")
    public CommonResult<?> GetUserInfo(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("获取用户信息, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.GetUserInfo(cmdRequest, comRequest);
    }

    /**
     * 用户注销
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10005&*")
    public CommonResult<?> baseService(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("用户注销, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.UserUnregister(cmdRequest, comRequest);
    }

    /**
     * 用户权益信息
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10006&*")
    public CommonResult<?> GetUserRight(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("用户权益信息, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.GetUserRight(cmdRequest, comRequest);
    }

    /**
     * 手机号码一键登录
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10007&*")
    public CommonResult<?> AliGetPhoneNum(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("手机号码一键登录, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.AliGetPhoneNum(cmdRequest, comRequest);
    }


    /**
     * 获取产品信息
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10201&*")
    public CommonResult<?> getProductInfo(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("获取产品信息, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.getProductInfo(cmdRequest, comRequest);
    }

    /**
     * 获取优惠券信息
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10202&*")
    public CommonResult<?> getCouponInfo(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("获取优惠券信息, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.getCouponInfo(cmdRequest, comRequest);
    }

    /**
     * APP控制
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10301&*")
    public CommonResult<?> GrayControl(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("APP控制, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.GrayControl(cmdRequest, comRequest);
    }


    /**
     * 微信统一下单
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=10401&*")
    public CommonResult<?> wxUnifiedOrder(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("微信统一下单, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        //参数校验
        PayOrderRequest payOrderRequest = validateUtils.validateParam(comRequest.getParam(), PayOrderRequest.class);
        //判断是否支持通联支付
        boolean isSupportUnionPay = unionPayService.checkUnionPay(cmdRequest);
        if (isSupportUnionPay) {
            return unionPayService.unionUnifiedOrder(cmdRequest, comRequest, payOrderRequest);
        }
        return wxPayService.wxUnifiedOrder(cmdRequest, comRequest, payOrderRequest);
    }


    /**
     * 支付宝统一下单
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=10402&*")
    public CommonResult<?> aliUnifiedOrder(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("支付宝统一下单, 请求信息： cmd: {} com: {}",
                JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        //参数校验
        PayOrderRequest payOrderRequest = validateUtils.validateParam(comRequest.getParam(), PayOrderRequest.class);
        return aliPayService.aliUnifiedOrder(cmdRequest, comRequest, payOrderRequest);
    }

    /**
     * 通联支付统一下单
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=10405&*")
    public CommonResult<?> unionUnifiedOrder(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("通联支付统一下单, 请求信息： cmd: {} com: {}",
                JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        //参数校验
        PayOrderRequest payOrderRequest = validateUtils.validateParam(comRequest.getParam(), PayOrderRequest.class);
        return unionPayService.unionUnifiedOrder(cmdRequest, comRequest, payOrderRequest);
    }

    /**
     * 账单记录
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10403&*")
    public CommonResult<?> GetBills(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("账单记录, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.GetBills(cmdRequest, comRequest);
    }


    /**
     * 意见反馈
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10501&*")
    public CommonResult<?> SendAdvice(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("意见反馈, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.SendAdvice(cmdRequest, comRequest);
    }


    /**
     * 企微活码 (客服)
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10502&*")
    public CommonResult<?> createQyQrCode(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("企微活码 (客服), 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.createQyQrCode(cmdRequest, comRequest);
    }

    /**
     * 老权益-用户权益
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=10090&*")
    public CommonResult<?> userPolicyInfo(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("老权益-用户权益, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.userPolicyInfo(cmdRequest, comRequest);
    }

    /**
     * 老权益-权益消耗分页列表
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=10091&*")
    public CommonResult<?> userPolicyConsumeRecord(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("老权益-权益消耗分页列表, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.userPolicyConsumeRecord(cmdRequest, comRequest);
    }

    /**
     * 支付统一下单
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=10404&*")
    public CommonResult<?> unifiedOrder(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("支付统一下单, 请求信息： cmd: {} com: {}",
                JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        //参数校验
        PayOrderRequest payOrderRequest = validateUtils.validateParam(comRequest.getParam(), PayOrderRequest.class);
        if (StringUtils.isEmpty(payOrderRequest.getPayplatform())) {
            return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "使用统一下单接口时，支付平台必填");
        }
        return unifiedPayService.unifiedOrder(cmdRequest, comRequest, payOrderRequest);
    }

    /**
     * 游客登录
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10008&*")
    public CommonResult<?> visitorLogin(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("游客登录, 请求信息： cmd: {} com: {}",
                JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return baseService.visitorLogin(cmdRequest, comRequest);
    }


    /**
     * 苹果支付验签
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=10406&*")
    public CommonResult<?> applePayVerify(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("苹果支付验签, 请求信息： cmd: {} com: {}",
                JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        //参数校验
        ApplePayVerifyRequest payVerifyRequest = validateUtils.validateParam(comRequest.getParam(), ApplePayVerifyRequest.class);
        return unifiedPayService.applePayVerify(cmdRequest, comRequest, payVerifyRequest);
    }

    /**
     * 支付订阅列表
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=10407&*")
    public CommonResult<?> paymentSubscribeList(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("支付订阅列表, 请求信息： cmd: {} com: {}",
                JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        //参数校验
        PaySubscribeRequest subscribeRequest = validateUtils.validateParam(comRequest.getParam(), PaySubscribeRequest.class);
        return unifiedPayService.paymentSubscribeList(cmdRequest, comRequest, subscribeRequest);
    }


    /**
     * 支付订阅取消
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=10408&*")
    public CommonResult<?> paymentSubscribeUnsign(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("支付订阅取消, 请求信息： cmd: {} com: {}",
                JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        //参数校验
        PaySubscribeUnSignRequest unSignRequest = validateUtils.validateParam(comRequest.getParam(), PaySubscribeUnSignRequest.class);
        return unifiedPayService.paymentSubscribeUnsign(cmdRequest, comRequest, unSignRequest);
    }

    /**
     * 测试接口
     *
     * @return 返回信息
     */
    @PostMapping("/test/*")
    public CommonResult<?> harmonyTest(@RequestAttribute("cmdParam") CmdRequest cmdRequest,
                                       @RequestBody @Valid ComRequest comRequest) {
        return harmonyPayService.harmonyTest(cmdRequest, comRequest);
    }


}