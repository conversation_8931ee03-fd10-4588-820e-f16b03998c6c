package cn.mingyang.cloud.usercenter.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.mingyang.cloud.center.common.request.BaseRequest;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.center.common.service.RedisService;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.api.response.UserRightResponse;
import cn.mingyang.cloud.usercenter.request.BuyProductRequest;
import cn.mingyang.cloud.usercenter.service.ProductService;
import cn.mingyang.cloud.usercenter.service.RightsService;
import com.alibaba.druid.util.StringUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.stereotype.Component;

import java.util.LinkedHashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权限缓存操作监听器
 */
@Data
@Slf4j
@Component
public class RightsCacheChangeListener implements MessageListener {

    //权益订单缓存Key
    private final String rightsOrderCacheKey = "user:rights:order";

    //权益订单Redis锁
    private final String rightsOrderCacheSync = "user_rights_order_sync_lock";

    //监听主题
    private final PatternTopic topic = new PatternTopic("__keyspace@*__:" + this.rightsOrderCacheKey);

    private final RedisService redisService;

    private final ProductService productService;

    private final RightsService rightsService;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        // 解析事件信息
        String channel = new String(message.getChannel());
        String event = new String(message.getBody());
        log.debug("权益订单Redis操作， channel：{} event: {}", channel, event);
        // 根据事件类型处理业务逻辑
        switch (event) {
            case "sadd":
                handleKeySadd();
                break;
            case "srem":
                handleKeySrem();
                break;
            case "expired":
                handleKeyExpired();
                break;
            default:
                log.warn("Redis operate unknown event: {}", event);
        }
    }

    /**
     * 新增订单
     */
    private void handleKeySadd() {
        try {
            // 获取 Redis 集合中的值
            Set<Object> rightsOrder = redisService.sMembersByString(this.rightsOrderCacheKey);
            if (CollectionUtil.isNotEmpty(rightsOrder)) {
                //初始化后排序
                Set<String> orders = rightsOrder.stream()
                        .map(Object::toString)
                        .sorted()
                        .collect(Collectors.toCollection(LinkedHashSet::new));
                //遍历处理
                for (String productStr : orders) {
                    if (StringUtils.isEmpty(productStr)) {
                        log.warn("新增订单购买商品，参数无效： {}", productStr);
                        continue;
                    }
                    boolean isGetLock = false;
                    try {
                        //权益订单锁
                        if (!redisService.tryLock(this.rightsOrderCacheSync, productStr, 10)) {
                            continue;
                        } else {
                            isGetLock = true;
                            log.info("获取订单锁， 订单字符串： {}", productStr);
                        }
                        //数据格式 db_appid_osid_userId_productId
                        String[] rs = productStr.split("_");
                        if (rs.length != 5) {
                            log.error("Invalid product key format: {}", productStr);
                            continue;
                        }
                        String ds = rs[0];
                        String appId = rs[1];
                        String osId = rs[2];
                        String userId = rs[3];
                        int productId = Integer.parseInt(rs[4]);

                        BuyProductRequest buyRequest = new BuyProductRequest();
                        buyRequest.setProductid(productId);

                        CmdRequest cmd = new CmdRequest("", "", appId, "", ds);
                        ComRequest comRequest = new ComRequest();
                        BaseRequest baseRequest = new BaseRequest();
                        baseRequest.setUserid(userId);
                        baseRequest.setOsid(osId);
                        comRequest.setBase(baseRequest);

                        CommonResult<UserRightResponse> result;
                        // 如果商品ID是0，则调用新用户新增权益
                        if (productId == 0) {
                            result = productService.newUserGift(cmd, comRequest);
                        } else {
                            //调用购买产品新增权益
                            result = productService.buyProduct(buyRequest, cmd, comRequest);
                        }
                        if (result.isSuccess()) {
                            //删除key
                            redisService.sRemove(this.rightsOrderCacheKey, productStr);
                            log.info("Successfully processed product purchase for user: {}, product: {}", userId, productId);
                        } else {
                            log.warn("Failed to process product purchase for user: {}, product: {}, result: {}",
                                    userId, productId, result.getMsg());
                        }
                    } catch (Exception exception) {
                        log.error("新增订单购买商品异常, Key: {}, error: {}", productStr, exception.getMessage(), exception);
                    } finally {
                        // 获取到锁，则最终释放锁
                        if (isGetLock) {
                            // 确保释放锁
                            if (!redisService.unlock(this.rightsOrderCacheSync, productStr)) {
                                log.warn("锁释放失败（可能已超时自动释放），key: {} order: {}", this.rightsOrderCacheSync, productStr);
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("处理新增订单异常: {}", e.getMessage(), e);
        }
    }


    /**
     * 删除订单
     */
    private void handleKeySrem() {
        //log.info("权益订单缓存删除订单，不处理");
    }

    /**
     * 缓存过期
     */
    private void handleKeyExpired() {
        //log.error("权益订单缓存不应该过期， 缓存Key：{}", this.rightsOrderCacheKey);
    }
}
