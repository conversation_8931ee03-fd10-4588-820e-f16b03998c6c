package cn.mingyang.cloud.usercenter.dao.domain;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import lombok.Data;


/**
 * 苹果收据验证结果（涵盖苹果完整响应结构 + 常用便捷字段）
 */
@Data
public class AppleReceiptResult {

    // 通用状态
    private boolean success;           // 是否验证成功（status == 0 且关键字段存在）
    private Integer status;            // 苹果返回状态码
    private boolean sandbox;           // 实际验证环境（true: Sandbox, false: Production）
    private String environment;        // environment: "Sandbox"/"Production"

    // 完整结构（iOS7 及之后常见字段）
    private JSONObject receipt;                // receipt 对象
    private String latestReceipt;              // latest_receipt（base64）
    private JSONArray latestReceiptInfo;       // latest_receipt_info 数组
    private JSONArray pendingRenewalInfo;      // pending_renewal_info 数组（订阅）

    // 便捷字段（从 receipt/in_app 中提取的常用值）
    private String bundleId;                   // 应用包名
    private String applicationVersion;         // 应用版本
    private String receiptType;                // 收据类型

    private String transactionId;              // 最新一条交易ID
    private String productId;                  // 最新一条交易产品ID
    private String purchaseDate;               // 最新一条交易购买时间
    private String expiresDate;                // 最新一条交易过期时间（订阅）
    private String expiresDateMs;              // 最新一条交易过期时间戳（订阅）
    private String originalTransactionId;      // 原始订单号
    private boolean haveLifeOrder;             // 是否有终身订单
    private boolean subscribeChange;           // 订阅变更
    private JSONObject latestInApp;            // 最新一条交易对象
}


