package cn.mingyang.cloud.usercenter.dao.domain;

import lombok.Data;

/**
 * 阿里支付内容
 */
@Data
public class AliPayBizContent {

    /**
     * 商户订单
     */
    private String out_trade_no;

    /**
     * 支付场景
     * bar_code：当面付条码支付场景；
     * security_code：当面付刷脸支付场景；
     */
    private String scene;

    /**
     * 支付授权码
     */
    private String auth_code;

    /**
     * 订单标题
     */
    private String subject;

    /**
     * 订单总金额。
     * 单位为元，精确到小数点后两位，取值范围：[0.01,100000000]
     */
    private Float total_amount;

    /**
     * 销售产品码，与支付宝签约的产品码名称。注：目前电脑支付场景下仅支持FAST_INSTANT_TRADE_PAY
     */
    private String product_code;
}
