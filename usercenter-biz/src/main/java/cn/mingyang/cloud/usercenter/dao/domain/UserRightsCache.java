package cn.mingyang.cloud.usercenter.dao.domain;

import cn.mingyang.cloud.center.common.util.DateTimeUtils;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import com.alibaba.druid.util.StringUtils;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户权益缓存
 */
@Data
public class UserRightsCache {

    /**
     * 用户权益缓存Key
     */
    public final static String userRightsCacheKey = "user:rights:ds:userId";

    /**
     * 今日已重置队列
     */
    public final static String userRightCacheReset = "user:rights:reset";


    /**
     * 今日需要同步到数据库的用户权益
     */
    public final static String userRightCacheSync = "user:rights:sync";

    /**
     * 用户id
     */
    private int userid;

    /**
     * 金币
     */
    private String coins;

    /**
     * 数量
     */
    private String numbercharging;

    /**
     * 次数
     */
    private String timescharging;

    /**
     * vip到期时间
     */
    private String vipendtime;

    /**
     * svip到期时间
     */
    private String svipendtime;


    /**
     * 会员等级 1；普通会员 2： VIP 3: SVIP
     */
    private Integer vipType;

    /**
     * 获取会员等级
     *
     * @return
     */
    public Integer getVipType() {
        int vipType = Constant.VIPType.NOTVIP;
        if (!StringUtils.isEmpty(vipendtime)) {
            if (StringUtils.isEmpty(svipendtime)) {
                if (LocalDateTime.now().isBefore(DateTimeUtils.parseFromString(vipendtime))) {
                    vipType = Constant.VIPType.VIP;
                }
            } else {
                if (LocalDateTime.now().isBefore(DateTimeUtils.parseFromString(svipendtime))) {
                    vipType = Constant.VIPType.SVIP;
                } else {
                    if (LocalDateTime.now().isBefore(DateTimeUtils.parseFromString(vipendtime))) {
                        vipType = Constant.VIPType.VIP;
                    }
                }
            }
        } else {
            if (!StringUtils.isEmpty(svipendtime)) {
                if (LocalDateTime.now().isBefore(DateTimeUtils.parseFromString(svipendtime))) {
                    vipType = Constant.VIPType.SVIP;
                }
            }
        }
        return vipType;
    }

    /**
     * 构建缓存权益key
     *
     * @param ds     数据库
     * @param userId 用户ID
     * @return 缓存Key
     */
    public static String buildCacheKey(String ds, int userId) {
        return UserRightsCache.userRightsCacheKey
                .replace("ds", ds)
                .replace("userId", String.valueOf(userId));
    }
}
