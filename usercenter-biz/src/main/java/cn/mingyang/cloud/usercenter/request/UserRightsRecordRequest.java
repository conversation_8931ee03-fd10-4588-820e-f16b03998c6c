package cn.mingyang.cloud.usercenter.request;

import cn.mingyang.cloud.center.common.request.PageRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 查询用户权益记录请求
 */
@Getter
@Setter
public class UserRightsRecordRequest extends PageRequest {

    /**
     * 用户ID
     */
    private Integer userid;

    /**
     * 查询标识
     */
    private String flag;

    /**
     * 过滤显示消耗记录
     */
    private List<Integer> filterSellGroup;
}
