package cn.mingyang.cloud.usercenter.dao.domain;

import cn.mingyang.cloud.center.common.util.DateTimeUtils;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import cn.mingyang.cloud.usercenter.response.ExpendRightsResponse;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 权益项管理类
 * 用于管理用户的各类权益，包括：
 * 1. 权益项的添加和更新
 * 2. 权益的使用和扣除
 * 3. 权益的查询和统计
 * 4. 权益的重置管理
 */
@Slf4j
@Data
public class RightItems {

    /**
     * 权益项内部类
     * 用于存储单个权益项的详细信息
     */
    @Data
    public static class RightItem {
        private int i;      // 权益组ID (groupID)
        private String t;   // 下一次重置时间 (next reset time)
        private int r;      // 重置周期 (reset period)
        private float rq;   // 重置数量 (reset quantity)
        private float q;    // 周期重置权益 (period reset right)
        private float o;    // 加油包 (oil package)
        private float f;    // 免费权益 (free right)
        private int rt;     // 重置状态值 (reset type)
    }

    private List<RightItem> rightItems;

    /**
     * 构造函数
     *
     * @param jString JSON格式的权益项字符串，如果为空则创建空的权益项列表
     */
    public RightItems(String jString) {
        rightItems = new ArrayList<>();
        if (!StringUtils.isEmpty(jString)) {
            rightItems = JSON.parseArray(jString, RightItem.class);
        }
    }

    public RightItems() {
    }

    /**
     * 获取权益项的JSON字符串表示
     *
     * @return JSON格式的权益项字符串
     */
    public String getJString() {
        return JSON.toJSONString(rightItems);
    }

    /**
     * 根据权益组ID获取权益项
     *
     * @param groupId 权益组ID
     * @return 对应的权益项，如果不存在则返回null
     */
    public RightItem getItemById(int groupId) {
        return rightItems.stream()
                .filter(item -> item.i == groupId)
                .findFirst()
                .orElse(null);
    }

    /**
     * 添加或更新权益项
     * 如果权益项已存在，则更新其属性；如果不存在，则添加新的权益项
     *
     * @param rightItem 要添加的权益项，不能为null
     * @param isOil     是否是加油包
     * @return 更新后的权益项JSON字符串
     * @throws NullPointerException 如果rightItem为null
     */
    public String addRightItem(RightItem rightItem, boolean isOil) {
        Objects.requireNonNull(rightItem, "RightItem cannot be null");

        boolean exists = false;
        for (RightItem existingItem : rightItems) {
            if (existingItem.i == rightItem.i) {
                updateExistingItem(existingItem, rightItem, isOil);
                exists = true;
                break;
            }
        }

        if (!exists) {
            rightItems.add(rightItem);
        }

        return getJString();
    }

    /**
     * 更新已存在的权益项
     * 更新包括：重置时间、权益数量、重置周期和重置数量
     *
     * @param existingItem 已存在的权益项
     * @param newItem      新的权益项
     * @param isOil        是否是加油包
     */
    public void updateExistingItem(RightItem existingItem, RightItem newItem, boolean isOil) {
        if (isOil) {
            existingItem.setO(existingItem.getO() + newItem.o);
        } else {
            // 更新时间
            if (!StringUtils.isEmpty(existingItem.getT())) {
                if (DateTimeUtils.parseFromString(newItem.getT()).isBefore(DateTimeUtils.parseFromString(existingItem.getT()))) {
                    existingItem.setT(newItem.getT());
                } else {
                    existingItem.setT(existingItem.getT());
                }
            } else {
                if (!StringUtils.isEmpty(newItem.getT())) {
                    existingItem.setT(newItem.getT());
                }
            }

            // 更新权益数量
            //existingItem.setQ(existingItem.getQ() + newItem.q);
            existingItem.setQ(newItem.q);
            existingItem.setO(existingItem.getO() + newItem.o);
            existingItem.setRt(newItem.rt);
            existingItem.setF(newItem.getF());

            // 更新重置周期
            if (newItem.getR() > 0) {
                if (existingItem.getR() > 0) {
                    existingItem.setR(Math.min(newItem.getR(), existingItem.getR()));
                } else {
                    existingItem.setR(newItem.getR());
                }
            }
            // 更新重置数量
            //existingItem.setRq(existingItem.getRq() + newItem.getRq());
            existingItem.setRq(newItem.getRq());
        }
    }

    /**
     * 消费权益
     * 根据权益类型和VIP类型消费指定数量的权益
     *
     * @param groupId      权益组ID
     * @param quantity     消费数量，必须大于0
     * @param rightType    权益类型（无限制/VIP/SVIP/加油包等）
     * @param vipType      VIP类型
     * @param expendStatus 扣减状态
     * @param isCheck      是否校验
     * @return 是否消费成功
     */
    public boolean expendRight(int groupId,
                               float quantity,
                               int rightType,
                               int vipType,
                               ExpendRightsResponse expendStatus,
                               boolean isCheck) {
        if (quantity <= 0) {
            log.warn("Invalid quantity: {}", quantity);
            return false;
        }
        return rightItems.stream()
                .filter(item -> item.i == groupId)
                .findFirst()
                .map(item -> processRightExpenditure(item, quantity, rightType, vipType, expendStatus, isCheck))
                .orElse(false);
    }

    /**
     * 处理权益消费
     * 根据权益类型分发到不同的消费处理方法
     *
     * @param rightItem    权益项
     * @param quantity     消费数量
     * @param rightType    权益类型
     * @param vipType      VIP类型
     * @param expendStatus 扣减状态
     * @param isCheck      是否校验
     * @return 是否消费成功
     */
    private boolean processRightExpenditure(RightItem rightItem,
                                            float quantity,
                                            int rightType,
                                            int vipType,
                                            ExpendRightsResponse expendStatus,
                                            boolean isCheck) {
        switch (rightType) {
            case Constant.RightType.NOLIMIT:
                return consumeNoLimitRight(rightItem, quantity, expendStatus, isCheck);
            case Constant.RightType.VIP:
                return consumeVipRight(rightItem, quantity, vipType, expendStatus, isCheck);
            case Constant.RightType.SVIP:
                return consumeSVipRight(rightItem, quantity, vipType, expendStatus, isCheck);
            case Constant.RightType.OIL:
                return consumeOilRight(rightItem, quantity, expendStatus, isCheck);
            case Constant.RightType.VIPOIL:
                return consumeVipOilRight(rightItem, quantity, vipType, expendStatus, isCheck);
            case Constant.RightType.SVIPOIL:
                return consumeSVipOilRight(rightItem, quantity, vipType, expendStatus, isCheck);
            default:
                log.warn("Unknown right type: {}", rightType);
                return false;
        }
    }

    /**
     * 重置指定权益组的所有权益
     * 检查重置时间，如果已过期则重置权益数量
     *
     * @param groupId 权益组ID
     */
    public void resetRight(int groupId) {
        rightItems.stream()
                .filter(item -> item.i == groupId)
                .forEach(this::resetRightItem);
    }

    /**
     * 重置免费权益
     * 如果当前时间超过重置时间，则更新重置时间并恢复权益数量
     *
     * @param rightItem 重置免费权益
     */
    private void resetFreeRightItem(RightItem rightItem) {
        if (!StringUtils.isEmpty(rightItem.getT())) {
            if (LocalDateTime.now().isAfter(DateTimeUtils.parseFromString(rightItem.getT()))) {
                rightItem.setT(DateTimeUtils.afterCurrentDateTimeZero(rightItem.getR()));
                rightItem.setF(rightItem.getRq());
            }
        }
    }

    /**
     * 重置单个权益项
     * 如果当前时间超过重置时间，则更新重置时间并恢复权益数量
     *
     * @param rightItem 要重置的权益项
     */
    private void resetRightItem(RightItem rightItem) {
        if (!StringUtils.isEmpty(rightItem.getT())) {
            if (LocalDateTime.now().isAfter(DateTimeUtils.parseFromString(rightItem.getT()))) {
                rightItem.setT(DateTimeUtils.afterCurrentDateTimeZero(rightItem.getR()));
                rightItem.setQ(rightItem.getRq());
            } else {
                //如果是历史数据，不是凌晨过期，需要改成凌晨
                String zeroTimeStr = DateTimeUtils.parseFromString(rightItem.getT()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
                if (!zeroTimeStr.equals(rightItem.getT())) {
                    rightItem.setT(zeroTimeStr);
                }
            }
        }
    }

    /**
     * 根据VIP状态重置所有权益
     * 根据权益的重置类型和用户的VIP状态决定是否重置
     *
     * @param vipType 会员等级
     */
    public void resetAllRight(int vipType) {
        rightItems.forEach(item -> {
            switch (item.rt) {
                case Constant.ResetType.FREE:
                    resetFreeRightItem(item);
                    break;
                case Constant.ResetType.OIL:
                    break;
                case Constant.ResetType.VIPRESET:
                    if (vipType == Constant.VIPType.VIP || vipType == Constant.VIPType.SVIP) {
                        resetRightItem(item);
                    }
                    break;
                case Constant.ResetType.SVIPRESET:
                    if (vipType == Constant.VIPType.SVIP) {
                        resetRightItem(item);
                    }
                    break;
            }
        });
    }

    /**
     * 消费无限制权益
     * 按优先级依次尝试消费周期重置权益、加油包和免费权益
     *
     * @param rightItem    权益项
     * @param quantity     消费数量
     * @param expendStatus 扣减状态
     * @param isCheck      是否校验
     * @return 是否消费成功
     */
    private boolean consumeNoLimitRight(RightItem rightItem,
                                        float quantity,
                                        ExpendRightsResponse expendStatus,
                                        boolean isCheck) {
        // 权益余值
        float haveRights = rightItem.getF() + rightItem.getQ() + rightItem.getO();
        // 赋值扣除总数
        float remaining = quantity;
        // 先扣除权益
        if (rightItem.getQ() >= remaining) {
            rightItem.setQ(rightItem.getQ() - remaining);
            return true;
        }
        if (rightItem.getQ() > 0) {
            remaining -= rightItem.getQ();
            rightItem.setQ(0);
        }
        // 扣除加油包
        if (remaining > 0) {
            if (rightItem.getO() >= remaining) {
                rightItem.setO(rightItem.getO() - remaining);
                expendStatus.setExstatus(Constant.ExpendRightsStatus.VIP_QUALITY);
                return true;
            }
            if (rightItem.getO() > 0) {
                remaining -= rightItem.getO();
                rightItem.setO(0);
                expendStatus.setExstatus(Constant.ExpendRightsStatus.VIP_QUALITY);
            }
        }
        // 扣除免费字段
        if (remaining > 0 && rightItem.getF() >= remaining) {
            rightItem.setF(rightItem.getF() - remaining);
            return true;
        } else {
            // 如果是扣减，则返回成功
            if (!isCheck && haveRights > 0) {
                rightItem.setQ(0);
                rightItem.setO(0);
                rightItem.setF(0);
                return true;
            }
        }
        return false;
    }

    /**
     * 消费VIP权益
     * VIP用户消费周期重置权益，非VIP用户消费免费权益
     *
     * @param rightItem    权益项
     * @param quantity     消费数量
     * @param vipType      VIP类型
     * @param expendStatus 扣减状态
     * @param isCheck      是否校验
     * @return 是否消费成功
     */
    private boolean consumeVipRight(RightItem rightItem, float quantity, int vipType, ExpendRightsResponse expendStatus, boolean isCheck) {
        // 权益余值
        float haveRights = rightItem.getF();
        if (vipType == Constant.VIPType.VIP || vipType == Constant.VIPType.SVIP) {
            haveRights = rightItem.getQ();
            if (rightItem.getQ() >= quantity) {
                rightItem.setQ(rightItem.getQ() - quantity);
                return true;
            }
        } else if (rightItem.getF() >= quantity) {
            rightItem.setF(rightItem.getF() - quantity);
            return true;
        }
        if (!isCheck && haveRights > 0) {
            if (vipType == Constant.VIPType.VIP || vipType == Constant.VIPType.SVIP) {
                rightItem.setQ(0);
            } else {
                rightItem.setF(0);
            }
            return true;
        }
        expendStatus.setExstatus(Constant.ExpendRightsStatus.VIP_QUALITY);
        return false;
    }

    /**
     * 消费SVIP权益
     * SVIP用户消费周期重置权益，非SVIP用户消费免费权益
     *
     * @param rightItem    权益项
     * @param quantity     消费数量
     * @param vipType      VIP类型
     * @param expendStatus 扣减状态
     * @return 是否消费成功
     */
    private boolean consumeSVipRight(RightItem rightItem,
                                     float quantity,
                                     int vipType,
                                     ExpendRightsResponse expendStatus,
                                     boolean isCheck) {
        // 权益余值
        float haveRights = rightItem.getF();
        if (vipType == Constant.VIPType.SVIP) {
            haveRights = rightItem.getQ();
            if (rightItem.getQ() >= quantity) {
                rightItem.setQ(rightItem.getQ() - quantity);
                return true;
            }
        } else if (rightItem.getF() >= quantity) {
            rightItem.setF(rightItem.getF() - quantity);
            return true;
        }
        if (!isCheck && haveRights > 0) {
            if (vipType == Constant.VIPType.SVIP) {
                rightItem.setQ(0);
            } else {
                rightItem.setF(0);
            }
            return true;
        }
        expendStatus.setExstatus(Constant.ExpendRightsStatus.VIP_QUALITY);
        return false;
    }

    /**
     * 消费加油包权益
     * 仅消费加油包权益
     *
     * @param rightItem    权益项
     * @param quantity     消费数量
     * @param expendStatus 扣减状态
     * @return 是否消费成功
     */
    private boolean consumeOilRight(RightItem rightItem,
                                    float quantity,
                                    ExpendRightsResponse expendStatus,
                                    boolean isCheck) {
        // 权益余值
        float haveRights = rightItem.getO();
        if (rightItem.getO() >= quantity) {
            rightItem.setO(rightItem.getO() - quantity);
            return true;
        }
        if (!isCheck && haveRights > 0) {
            rightItem.setO(0);
            return true;
        }
        expendStatus.setExstatus(Constant.ExpendRightsStatus.VIP_QUALITY_OIL);
        return false;
    }

    /**
     * 消费VIP加油包权益
     * VIP用户优先消费周期重置权益，其次消费加油包权益；非VIP用户消费免费权益
     *
     * @param rightItem    权益项
     * @param quantity     消费数量
     * @param vipType      VIP类型
     * @param expendStatus 扣减状态
     * @return 是否消费成功
     */
    private boolean consumeVipOilRight(RightItem rightItem,
                                       float quantity,
                                       int vipType,
                                       ExpendRightsResponse expendStatus,
                                       boolean isCheck) {
        // 权益余值
        float haveRights = rightItem.getF();
        if (vipType == Constant.VIPType.VIP || vipType == Constant.VIPType.SVIP) {
            haveRights = rightItem.getQ() + rightItem.getO();
            // 赋值扣除总数
            float remaining = quantity;
            // 先扣除权益
            if (rightItem.getQ() >= remaining) {
                rightItem.setQ(rightItem.getQ() - remaining);
                return true;
            }
            if (rightItem.getQ() > 0) {
                remaining -= rightItem.getQ();
                rightItem.setQ(0);
            }
            // 扣除加油包
            if (remaining > 0) {
                if (rightItem.getO() >= remaining) {
                    rightItem.setO(rightItem.getO() - remaining);
                    expendStatus.setExstatus(Constant.ExpendRightsStatus.VIP_QUALITY);
                    return true;
                }
            }
        } else if (rightItem.getF() >= quantity) {
            rightItem.setF(rightItem.getF() - quantity);
            return true;
        }
        if (!isCheck && haveRights > 0) {
            if (vipType == Constant.VIPType.VIP || vipType == Constant.VIPType.SVIP) {
                rightItem.setQ(0);
                rightItem.setO(0);
            } else {
                rightItem.setF(0);
            }
            return true;
        }
        expendStatus.setExstatus(Constant.ExpendRightsStatus.VIP_QUALITY_OIL);
        return false;
    }

    /**
     * 消费SVIP加油包权益
     * SVIP用户优先消费周期重置权益，其次消费加油包权益；非SVIP用户消费免费权益
     *
     * @param rightItem    权益项
     * @param quantity     消费数量
     * @param vipType      VIP类型
     * @param expendStatus 扣减状态
     * @return 是否消费成功
     */
    private boolean consumeSVipOilRight(RightItem rightItem,
                                        float quantity,
                                        int vipType,
                                        ExpendRightsResponse expendStatus,
                                        boolean isCheck) {
        // 权益余值
        float haveRights = rightItem.getF();
        if (vipType == Constant.VIPType.SVIP) {
            haveRights = rightItem.getQ() + rightItem.getO();
            // 赋值扣除总数
            float remaining = quantity;
            // 先扣除权益
            if (rightItem.getQ() >= remaining) {
                rightItem.setQ(rightItem.getQ() - remaining);
                return true;
            }
            if (rightItem.getQ() > 0) {
                remaining -= rightItem.getQ();
                rightItem.setQ(0);
            }
            // 扣除加油包
            if (remaining > 0) {
                if (rightItem.getO() >= remaining) {
                    rightItem.setO(rightItem.getO() - remaining);
                    expendStatus.setExstatus(Constant.ExpendRightsStatus.VIP_QUALITY);
                    return true;
                }
            }
        } else if (rightItem.getF() >= quantity) {
            rightItem.setF(rightItem.getF() - quantity);
            return true;
        }
        if (!isCheck && haveRights > 0) {
            if (vipType == Constant.VIPType.SVIP) {
                rightItem.setQ(0);
                rightItem.setO(0);
            } else {
                rightItem.setF(0);
            }
            return true;
        }
        expendStatus.setExstatus(Constant.ExpendRightsStatus.VIP_QUALITY_OIL);
        return false;
    }
}
