package cn.mingyang.cloud.usercenter.controller.feign;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.comEnum.CommandType;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.center.common.util.MyUtils;
import cn.mingyang.cloud.center.common.util.ValidateUtils;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.api.request.FeignRequest;
import cn.mingyang.cloud.usercenter.api.response.UserRightResponse;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import cn.mingyang.cloud.usercenter.comEnum.FunctionEnum;
import cn.mingyang.cloud.usercenter.request.AddUserRightsRequest;
import cn.mingyang.cloud.usercenter.request.ExpendUserRightsRequest;
import cn.mingyang.cloud.usercenter.request.SyncUserRightsRequest;
import cn.mingyang.cloud.usercenter.request.UserRightsRecordRequest;
import cn.mingyang.cloud.usercenter.response.UserRightRecordResponse;
import cn.mingyang.cloud.usercenter.service.RightsService;
import com.alibaba.nacos.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * Feign权益服务
 */
@RestController
@RequestMapping("/inner/rights")
@Slf4j
public class FeignRightController {


    private final RightsService rightsService;

    private final ValidateUtils validateUtils;

    public FeignRightController(RightsService rightsService, ValidateUtils validateUtils) {
        this.rightsService = rightsService;
        this.validateUtils = validateUtils;
    }

    /**
     * 获取用户权益
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10006&*")
    public CommonResult<UserRightResponse> getUserRight(@RequestBody @Valid FeignRequest feignRequest) {
        log.info("Feign 获取用户权益, 请求信息： request: {} ", JSONUtil.toJsonStr(feignRequest));
        CmdRequest cmdRequest = BeanUtil.toBean(feignRequest.getCmd(), CmdRequest.class);
        cmdRequest.setCno(CommandType.GetUserRight.getCode());
        ComRequest comRequest = BeanUtil.toBean(feignRequest.getCom(), ComRequest.class);
        // 校验CMD
        MyUtils.checkCommand(cmdRequest, comRequest);
        //先重置用户权益
        rightsService.resetRight(cmdRequest, comRequest);
        return rightsService.getUserRightInfo(cmdRequest, comRequest);
    }

    /**
     * 扣除权益
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10083&*")
    public CommonResult<?> expendUserRights(@RequestBody @Valid FeignRequest feignRequest) {
        log.info("Feign 扣除权益, 请求信息： request: {} ", JSONUtil.toJsonStr(feignRequest));
        CmdRequest cmdRequest = BeanUtil.toBean(feignRequest.getCmd(), CmdRequest.class);
        cmdRequest.setCno(CommandType.ExpendUserRights.getCode());
        ComRequest comRequest = BeanUtil.toBean(feignRequest.getCom(), ComRequest.class);
        // 校验CMD
        MyUtils.checkCommand(cmdRequest, comRequest);
        //参数校验
        ExpendUserRightsRequest expendRequest = validateUtils.validateParam(comRequest.getParam(), ExpendUserRightsRequest.class);
        if (!StringUtils.isEmpty(cmdRequest.getVersion())) {
            // 推文版本9.0，调用老的权益
            if (cmdRequest.getVersion().equals("9.0") && List.of(Constant.AllDs.AITW, Constant.AllDs.MIX).contains(cmdRequest.getDs())) {
                FunctionEnum functionEnum = FunctionEnum.getFunctionEnumByCmd(expendRequest.getFunctionid());
                if (null != functionEnum && functionEnum.getExpendType() != 3) {
                    return rightsService.expendRightByTw(cmdRequest, false, comRequest, expendRequest);
                }
            } else if (cmdRequest.getVersion().equals("9.1") && List.of(Constant.AllDs.AITW, Constant.AllDs.MIX).contains(cmdRequest.getDs())) {
                FunctionEnum functionEnum = FunctionEnum.getFunctionEnumByCmd(expendRequest.getFunctionid());
                if (null != functionEnum && functionEnum.getExpendType() == 1) {
                    expendRequest.setOldQuantity(expendRequest.getQuantity());
                    expendRequest.setQuantity(BigDecimal.valueOf(expendRequest.getQuantity())
                            .divide(BigDecimal.valueOf(10)).setScale(0, RoundingMode.UP).intValue());
                }
            }
        }
        //先重置用户权益
        rightsService.resetRight(cmdRequest, comRequest);
        return rightsService.expendRight(cmdRequest, false, comRequest, expendRequest);
    }

    /**
     * 校验用户权益
     *
     * @param feignRequest 命令请求
     * @return 返回信息
     */
    @PostMapping("/c=10086&*")
    public CommonResult<?> checkUserRights(@RequestBody @Valid FeignRequest feignRequest) {
        log.info("Feign 校验用户权益, 请求信息： request: {} ", JSONUtil.toJsonStr(feignRequest));
        CmdRequest cmdRequest = BeanUtil.toBean(feignRequest.getCmd(), CmdRequest.class);
        cmdRequest.setCno(CommandType.CheckUserRights.getCode());
        ComRequest comRequest = BeanUtil.toBean(feignRequest.getCom(), ComRequest.class);
        // 校验CMD
        MyUtils.checkCommand(cmdRequest, comRequest);
        //参数校验
        ExpendUserRightsRequest expendRequest = validateUtils.validateParam(comRequest.getParam(), ExpendUserRightsRequest.class);
        // 推文版本9.0，调用老的权益
        if (!StringUtils.isEmpty(cmdRequest.getVersion())) {
            if (cmdRequest.getVersion().equals("9.0") && List.of(Constant.AllDs.AITW, Constant.AllDs.MIX).contains(cmdRequest.getDs())) {
                FunctionEnum functionEnum = FunctionEnum.getFunctionEnumByCmd(expendRequest.getFunctionid());
                if (null != functionEnum && functionEnum.getExpendType() != 3) {
                    return rightsService.expendRightByTw(cmdRequest, true, comRequest, expendRequest);
                }
            } else if (cmdRequest.getVersion().equals("9.1") && List.of(Constant.AllDs.AITW, Constant.AllDs.MIX).contains(cmdRequest.getDs())) {
                FunctionEnum functionEnum = FunctionEnum.getFunctionEnumByCmd(expendRequest.getFunctionid());
                if (null != functionEnum && functionEnum.getExpendType() == 1) {
                    expendRequest.setOldQuantity(expendRequest.getQuantity());
                    expendRequest.setQuantity(BigDecimal.valueOf(expendRequest.getQuantity())
                            .divide(BigDecimal.valueOf(10)).setScale(0, RoundingMode.UP).intValue());
                }
            }
        }
        //先重置用户权益
        rightsService.resetRight(cmdRequest, comRequest);
        return rightsService.expendRight(cmdRequest, true, comRequest, expendRequest);
    }

    /**
     * 获取功能价格列表
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10087&*")
    public CommonResult<?> getAllFunctionRights(@RequestBody @Valid FeignRequest feignRequest) {
        log.info("Feign 获取功能价格列表, 请求信息： request: {} ", JSONUtil.toJsonStr(feignRequest));
        CmdRequest cmdRequest = BeanUtil.toBean(feignRequest.getCmd(), CmdRequest.class);
        cmdRequest.setCno(CommandType.GetAllFunctionRights.getCode());
        ComRequest comRequest = BeanUtil.toBean(feignRequest.getCom(), ComRequest.class);
        // 校验CMD
        MyUtils.checkCommand(cmdRequest, comRequest);
        return rightsService.getAllFunctionPrice(cmdRequest, comRequest);
    }

    /**
     * 重置用户权益
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10088&*")
    public CommonResult<UserRightResponse> resetUserRights(@RequestBody @Valid FeignRequest feignRequest) {
        log.info("Feign 重置用户权益, 请求信息： request: {} ", JSONUtil.toJsonStr(feignRequest));
        CmdRequest cmdRequest = BeanUtil.toBean(feignRequest.getCmd(), CmdRequest.class);
        cmdRequest.setCno(CommandType.ResetUserRights.getCode());
        ComRequest comRequest = BeanUtil.toBean(feignRequest.getCom(), ComRequest.class);
        // 校验CMD
        MyUtils.checkCommand(cmdRequest, comRequest);
        return rightsService.resetRight(cmdRequest, comRequest);
    }

    /**
     * 新增用户权益
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10082&*")
    public CommonResult<?> addUserRights(@RequestBody @Valid FeignRequest feignRequest) {
        log.info("Feign 新增用户权益, 请求信息： request: {} ", JSONUtil.toJsonStr(feignRequest));
        CmdRequest cmdRequest = BeanUtil.toBean(feignRequest.getCmd(), CmdRequest.class);
        cmdRequest.setCno(CommandType.AddUserRights.getCode());
        ComRequest comRequest = BeanUtil.toBean(feignRequest.getCom(), ComRequest.class);
        // 校验CMD
        MyUtils.checkCommand(cmdRequest, comRequest);
        //参数校验
        AddUserRightsRequest addRightsRequest = validateUtils.validateParam(comRequest.getParam(), AddUserRightsRequest.class);
        return rightsService.addUserRights(cmdRequest, comRequest, addRightsRequest);
    }


    /**
     * 用户权益同步和转移
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10089&*")
    public CommonResult<?> syncUserRights(@RequestBody @Valid FeignRequest feignRequest) {
        log.info("Feign 用户权益同步和转移, 请求信息： request: {} ", JSONUtil.toJsonStr(feignRequest));
        CmdRequest cmdRequest = BeanUtil.toBean(feignRequest.getCmd(), CmdRequest.class);
        cmdRequest.setCno(CommandType.SyncUserRights.getCode());
        ComRequest comRequest = BeanUtil.toBean(feignRequest.getCom(), ComRequest.class);
        // 校验CMD
        MyUtils.checkCommand(cmdRequest, comRequest);
        //参数校验
        SyncUserRightsRequest syncUser = validateUtils.validateParam(comRequest.getParam(), SyncUserRightsRequest.class);
        return rightsService.syncUserRights(cmdRequest, comRequest, syncUser);
    }


    /**
     * 删除用户权益
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10092&*")
    public CommonResult<?> deleteUserRights(@RequestBody @Valid FeignRequest feignRequest) {
        log.info("Feign 删除用户权益, 请求信息： request: {} ", JSONUtil.toJsonStr(feignRequest));
        CmdRequest cmdRequest = BeanUtil.toBean(feignRequest.getCmd(), CmdRequest.class);
        cmdRequest.setCno(CommandType.DeleteUserRights.getCode());
        ComRequest comRequest = BeanUtil.toBean(feignRequest.getCom(), ComRequest.class);
        // 校验CMD
        MyUtils.checkCommand(cmdRequest, comRequest);
        return rightsService.deleteUserAllRights(cmdRequest, comRequest);
    }


    /**
     * 用户权益记录
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10093&*")
    public CommonResult<UserRightRecordResponse> userRightRecord(@RequestBody @Valid FeignRequest feignRequest) {
        log.info("Feign 用户权益记录, 请求信息： request: {} ", JSONUtil.toJsonStr(feignRequest));
        CmdRequest cmdRequest = BeanUtil.toBean(feignRequest.getCmd(), CmdRequest.class);
        cmdRequest.setCno(CommandType.UserRightRecord.getCode());
        ComRequest comRequest = BeanUtil.toBean(feignRequest.getCom(), ComRequest.class);
        // 校验CMD
        MyUtils.checkCommand(cmdRequest, comRequest);
        // 校验参数
        UserRightsRecordRequest recordRequest = validateUtils.validateParam(comRequest.getParam(), UserRightsRecordRequest.class);
        return rightsService.userRightRecord(cmdRequest, comRequest, recordRequest);
    }

}
