package cn.mingyang.cloud.usercenter.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 支付下单请求
 */
@Data
public class PayOrderRequest {

    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式：paycode不能为空")
    private String paycode;

    /**
     * 商品ID
     */
    @NotNull(message = "商品ID：productid不能为空")
    private Integer productid;

    /**
     * 支付平台
     */
    private String payplatform;

    /**
     * 优惠券 ID
     */
    private Integer coupoid;

    /**
     * 条码
     */
    private String authcode;


    /**
     * OpenID 微信小程序必传
     */
    private String openid;


    /**
     * 是否订阅订单 0 否 1是
     */
    private Integer issubscribe;

    /**
     * 签约协议号
     */
    private String agreementno;

    /**
     * 订单编号
     */
    private String outtradeno;

}
