package cn.mingyang.cloud.usercenter.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.comEnum.BaseErrorCodeEnum;
import cn.mingyang.cloud.center.common.exception.MyBaseException;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import cn.mingyang.cloud.usercenter.comEnum.PayType;
import cn.mingyang.cloud.usercenter.dao.domain.ProductDoMain;
import cn.mingyang.cloud.usercenter.dao.domain.UserDetail;
import cn.mingyang.cloud.usercenter.dao.entity.*;
import cn.mingyang.cloud.usercenter.dao.mapper.PayMapper;
import cn.mingyang.cloud.usercenter.request.PayOrderRequest;
import com.alibaba.druid.util.StringUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单Service层
 */
@Service
@Slf4j
public class OrderService {

    private final PayMapper payMapper;
    private final DbCacheService dbCacheService;
    private final BaseService baseService;


    public OrderService(PayMapper payMapper,
                        DbCacheService dbCacheService,
                        BaseService baseService) {
        this.payMapper = payMapper;
        this.dbCacheService = dbCacheService;
        this.baseService = baseService;
    }


    /**
     * 获取支付价格
     *
     * @param payRequest 支付信息
     * @param product    产品操作信息
     * @return 支付价格
     */
    @DS("#pid")
    public Float getPayPrice(String pid, PayOrderRequest payRequest, ProductDoMain product) {
        try {
            float couponPrice = 0.0f;
            if (null != payRequest.getCoupoid() && payRequest.getCoupoid() != 0) {
                OrgCoupon orgCoupon = payMapper.getCouponById(payRequest.getCoupoid());
                if (orgCoupon == null) {
                    log.warn("[获取支付价格] 未找到优惠券 pid={}, coupoid={}", pid, payRequest.getCoupoid());
                    throw new MyBaseException(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "优惠券不存在");
                }
                couponPrice = orgCoupon.getPrice();
            }
            // 将原始值转换为 BigDecimal
            BigDecimal promotionPrice = new BigDecimal(String.valueOf(product.getRealprice()));
            BigDecimal coupon = new BigDecimal(String.valueOf(couponPrice));
            BigDecimal diff = promotionPrice.subtract(coupon);
            // 币种转化（将原币种 -> CNY）。例如 USD -> CNY 则乘以 USD->CNY 的汇率
            if (!StringUtils.isEmpty(product.getCurrency())
                    && !product.getCurrency().equals(Constant.CurrencyType.CNY)) {
                BigDecimal rateToCny = dbCacheService.getCnyRate(product.getCurrency());
                diff = diff.multiply(rateToCny);
            }
            BigDecimal priceBD = diff.setScale(2, RoundingMode.UP);
            Float price = priceBD.floatValue();
            log.info("[获取支付价格] pid={}, productid={}, coupoid={}, promotionPrice={}, couponPrice={}, currency = {}, totalPrice={}", pid,
                    payRequest.getProductid(), payRequest.getCoupoid(), product.getRealprice(), couponPrice, product.getCurrency(), price);
            return price;
        } catch (Exception e) {
            log.error("[获取支付价格] 计算异常", e);
            throw new MyBaseException(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "获取支付价格异常");
        }
    }

    /**
     * 获取商品操作信息
     *
     * @param pid   命令参数
     * @param query 请求参数
     * @param osid  系统ID
     * @param lg    语种
     * @param df    渠道
     * @return 商品操作信息
     */
    @DS("#pid")
    public ProductDoMain getProductInfo(String pid,
                                        ProductDoMain query,
                                        String appid,
                                        String osid,
                                        String lg,
                                        String df) {
        //判断是否是推文，如果是，则查新商品；否，则查老商品
        List<ProductDoMain> productDoMains = dbCacheService.queryNewProduct(pid, query);

        // 首先按appid和osid过滤
        List<ProductDoMain> productByAppId = productDoMains.stream()
                .filter(p -> p.getAppid().equals(appid) && p.getOsid().equals(osid))
                .collect(Collectors.toList());

        // 如果找不到匹配的appid和osid，则使用默认值
        if (CollectionUtil.isEmpty(productByAppId)) {
            productByAppId = productDoMains.stream()
                    .filter(p -> p.getAppid().equals(Constant.DEFAULT) && p.getOsid().equals(Constant.DEFAULT))
                    .collect(Collectors.toList());
        }

        // 如果仍然为空，直接抛出异常
        if (CollectionUtil.isEmpty(productByAppId)) {
            log.warn("未查询到商品信息, 商品ID: {}", query.getProductid());
            throw new MyBaseException(BaseErrorCodeEnum.NULL_PRODUCT_ERROR.getCode(),
                    BaseErrorCodeEnum.NULL_PRODUCT_ERROR.getMessage());
        }

        // 过滤语种
        List<ProductDoMain> filteredByLg = productByAppId;
        if (!StringUtils.isEmpty(lg)) {
            filteredByLg = productByAppId.stream()
                    .filter(prd -> null == prd.getLg() || lg.equals(prd.getLg()))
                    .collect(Collectors.toList());
        }

        // 过滤渠道
        List<ProductDoMain> filteredByDf = filteredByLg;
        if (!StringUtils.isEmpty(df)) {
            filteredByDf = filteredByLg.stream()
                    .filter(prd -> null == prd.getDf() || df.equals(prd.getDf()))
                    .collect(Collectors.toList());

            // 如果指定渠道找不到，尝试使用默认渠道
            if (CollectionUtil.isEmpty(filteredByDf)) {
                filteredByDf = filteredByLg.stream()
                        .filter(prd -> null == prd.getDf() || Constant.DEFAULT.equals(prd.getDf()))
                        .collect(Collectors.toList());
            }
        }

        if (CollectionUtil.isEmpty(filteredByDf)) {
            log.warn("未查询到商品信息, 商品ID: {}", query.getProductid());
            throw new MyBaseException(BaseErrorCodeEnum.NULL_PRODUCT_ERROR.getCode(),
                    BaseErrorCodeEnum.NULL_PRODUCT_ERROR.getMessage());
        }

        ProductDoMain productDoMain = filteredByDf.get(0);
        log.info("查询到的商品信息： {}", JSONUtil.toJsonStr(productDoMain));
        return productDoMain;
    }


    /**
     * 获取商品操作信息
     *
     * @param pid   命令参数
     * @param query 请求参数
     * @return 商品操作信息
     */
    @DS("#pid")
    public ProductDoMain getOldProductInfo(String pid, ProductDoMain query) {
        ProductDoMain productDoMain;
        OrgProduct orgProduct = null;
        if (null != query.getProductid()) {
            orgProduct = payMapper.getProductInfoById(query.getProductid());
        }
        if (!StringUtils.isEmpty(query.getProductname())) {
            orgProduct = payMapper.getProductInfoByName(query.getProductname());
        }
        if (null == orgProduct) {
            log.warn("未查询到老商品信息, 商品ID: {}", query.getProductid());
            throw new MyBaseException(BaseErrorCodeEnum.NULL_PRODUCT_ERROR.getCode(), BaseErrorCodeEnum.NULL_PRODUCT_ERROR.getMessage());
        }
        productDoMain = new ProductDoMain();
        productDoMain.setProductid(orgProduct.getId());
        productDoMain.setProductname(orgProduct.getProductName());
        productDoMain.setTitle(orgProduct.getTitle());
        productDoMain.setQuantity(orgProduct.getQuantity());
        productDoMain.setDuration(orgProduct.getQuantity() * 30);
        productDoMain.setOrginalprice(orgProduct.getPrice());
        productDoMain.setRealprice(orgProduct.getPromotionPrice());
        productDoMain.setAppid(orgProduct.getAppid());
        productDoMain.setOsid(orgProduct.getOsid());
        productDoMain.setDf(orgProduct.getDf());
        switch (orgProduct.getProductType()) {
            case "2":
                productDoMain.setProducttype(Constant.ProductType.VIP);
            case "7":
                productDoMain.setProducttype(Constant.ProductType.SVIP);
            case "8":
                productDoMain.setProducttype(Constant.ProductType.OIL);
            default:
                log.warn("使用商品类型原始值： {}", orgProduct.getProductType());
                productDoMain.setProducttype(Integer.valueOf(orgProduct.getProductType()));
        }
        productDoMain.setAlias(orgProduct.getProductName());
        log.info("查询到老的商品信息： {}", JSONUtil.toJsonStr(productDoMain));
        return productDoMain;
    }


    /**
     * 创建支付订单
     *
     * @param payRequest 支付信息
     * @param cmd        命令
     * @param com        通用信息
     * @param payType    支付类型，1:微信 2:支付宝，非空
     * @param mchId      商户号
     * @return 支付价格
     */
    @DS("#cmd.ds")
    public OrgUserOrder createPayOrder(PayOrderRequest payRequest, CmdRequest cmd, ComRequest com, Integer payType, String mchId) {
        //获取商品信息
        ProductDoMain queryProduct = new ProductDoMain();
        queryProduct.setProductid(payRequest.getProductid());
        ProductDoMain productDoMain = getProductInfo(
                cmd.getDs(),
                queryProduct,
                cmd.getAppId(),
                com.getBase().getOsid(),
                com.getBase().getLg(),
                com.getBase().getDf());
        //创建支付订单
        OrgUserOrder payOrder = new OrgUserOrder();
        //订单编号
        if (StringUtils.isEmpty(payRequest.getOuttradeno())) {
            payOrder.setOutTradeNo(this.produceOrderTradeNo(com.getBase().getUserid()));
        } else {
            payOrder.setOutTradeNo(payRequest.getOuttradeno());
        }
        //用户ID
        payOrder.setUserid(Integer.valueOf(com.getBase().getUserid()));
        //支付金额
        payOrder.setFee(this.getPayPrice(cmd.getDs(), payRequest, productDoMain));
        //支付类型
        payOrder.setType(payType);
        //订单创建时间
        payOrder.setTime(LocalDateTime.now());
        //订单状态
        payOrder.setStatus(1);
        //设备ID
        payOrder.setDeviceid(com.getBase().getDeviceid());
        //是否需要通知添加，默认0
        payOrder.setNeednotifyadd(0);
        //应用ID
        payOrder.setAppid(cmd.getAppId());
        //操作系统ID
        payOrder.setOsid(com.getBase().getOsid());
        //渠道
        payOrder.setDf(com.getBase().getDf());
        //原始价格
        payOrder.setOriginPrice(productDoMain.getOrginalprice());
        //商品名称
        payOrder.setProductName(productDoMain.getProductname());
        //商品数量
        payOrder.setQuantity(productDoMain.getQuantity());
        //微信openid
        payOrder.setOpenid(payOrder.getOpenid());
        //商户id
        payOrder.setMchId(mchId);
        payOrder.setOldDf("");
        payOrder.setAdState(0);
        payOrder.setSubscribePrice(0.00f);
        payOrder.setTitle(productDoMain.getTitle());
        payOrder.setIsSubscribe(productDoMain.getIssubscribe());
        payMapper.insertUserOrder(payOrder);
        return payOrder;
    }

    /**
     * 创建订阅支付订单
     *
     * @param subscribe 订阅支付信息
     * @return 支付价格
     */
    @DS("#pid")
    public OrgUserOrder createPayOrderBySubscribe(String pid, PaySubscribe subscribe) {
        //查询商品信息
        ProductDoMain productInfoDoMain = dbCacheService.getProductType(pid, subscribe.getProductid());
        if (null == productInfoDoMain) {
            log.error("根据商品id查询商品信息为空，商品ID: {}", subscribe.getProductid());
            return null;
        }
        //创建支付订单
        OrgUserOrder payOrder = new OrgUserOrder();
        //订单变化
        payOrder.setOutTradeNo(this.produceOrderTradeNo(subscribe.getUserid() + ""));
        //用户ID
        payOrder.setUserid(subscribe.getUserid());
        //支付金额
        payOrder.setFee(subscribe.getFee());
        //支付类型
        payOrder.setType(Objects.requireNonNull(PayType.fromCode(subscribe.getPlatformcode())).getIndex());
        //订单创建时间
        payOrder.setTime(LocalDateTime.now());
        //订单状态
        payOrder.setStatus(Constant.PayStatus.UNPAID);
        //设备ID
        payOrder.setDeviceid("");
        //是否需要通知添加，默认0
        payOrder.setNeednotifyadd(0);
        //应用ID
        payOrder.setAppid(subscribe.getAppid());
        //操作系统ID
        payOrder.setOsid(subscribe.getOsid());
        //渠道
        payOrder.setDf(subscribe.getDf());
        //原始价格
        payOrder.setOriginPrice(subscribe.getFee());
        //商品名称
        payOrder.setProductName(productInfoDoMain.getProductname());
        //商品数量
        payOrder.setQuantity(productInfoDoMain.getQuantity());
        //微信openid
        payOrder.setOpenid(payOrder.getOpenid());
        //商户id
        payOrder.setMchId(subscribe.getMchid());
        payOrder.setOldDf("");
        payOrder.setAdState(0);
        payOrder.setSubscribePrice(subscribe.getFee());
        payOrder.setIsSubscribe(Constant.IsSubscribe.YES);
        payMapper.insertUserOrder(payOrder);
        log.info("创建订阅带支付订单，订单信息： {}", JSONUtil.toJsonStr(payOrder));
        return payOrder;
    }


    /**
     * 初始化订阅订单
     *
     * @param product   订阅商品
     * @param userOrder 用户订单
     * @param payConfig 支付渠道配置
     * @return 订阅订单
     */
    public PaySubscribe initSubscribeOrder(ProductDoMain product,
                                           OrgUserOrder userOrder,
                                           PayConfig payConfig) {
        PaySubscribe subscribe = new PaySubscribe();
        subscribe.setPlatformcode(payConfig.getPlatformcode());
        subscribe.setMchid(payConfig.getMchid());
        subscribe.setAppid(userOrder.getAppid());
        subscribe.setDf(userOrder.getDf());
        subscribe.setUserid(userOrder.getUserid());
        subscribe.setOsid(userOrder.getOsid());
        subscribe.setProductid(product.getProductid());
        subscribe.setNextdeducttime(LocalDateTime.now()
                .plusDays(product.getDuration()));
        subscribe.setStatus(Constant.SignStatus.SIGNING);
        subscribe.setFee(product.getRealprice());
        subscribe.setDuration(product.getDuration());
        subscribe.setRetry(Constant.Retry.YES);
        subscribe.setFirstouttradeno(userOrder.getOutTradeNo());
        subscribe.setCurrency(product.getCurrency());
        log.info("初始化订阅单价成功, 订单信息： {}", JSONUtil.toJsonStr(subscribe));
        return subscribe;
    }

    /**
     * 创建订阅订单
     *
     * @param pid       业务ID
     * @param subscribe 订阅记录
     * @return 订阅订单
     */
    @DS("#pid")
    public void createSubscribeOrder(String pid, PaySubscribe subscribe) {
        // 查询是否已有订阅记录
        PaySubscribe dbSubscribe = payMapper.selectPaySubscribeByUserIdAndProductId(subscribe.getUserid(), subscribe.getProductid());
        // 没有订阅信息或者订阅状态为已取消，则可以新建订阅
        if (null == dbSubscribe || Objects.equals(dbSubscribe.getStatus(), Constant.SignStatus.CANCEL)) {
            payMapper.insertPaySubscribe(subscribe);
        }
        log.info("创建订阅订单成功, 订单信息： {}", JSONUtil.toJsonStr(subscribe));
    }

    /**
     * 更新订阅订单
     *
     * @param pid        业务ID
     * @param firstOrder 首个订单
     */
    @DS("#pid")
    public void updateSubscribeOrder(String pid, String firstOrder, String configvalues) {
        payMapper.updatePaySubscribeMessage(firstOrder, configvalues);
        log.info("更新订阅订单成功, 订单信息： {}", firstOrder);
    }

    /**
     * 生成订单号
     *
     * @param userId 用户ID
     * @return 订单号
     */
    private String produceOrderTradeNo(String userId) {
        String timeStr = DateUtil.format(LocalDateTime.now(), "yyMMddHHmmss");
        int random = (int) (Math.random() * 90000) + 10000;
        String orderNo = timeStr + random + userId;
        log.info("[订单号生成] userId={}, orderNo={}", userId, orderNo);
        return orderNo;
    }


    /**
     * 处理支付回调
     *
     * @param pid        业务标识
     * @param outTradeNo 订单编号
     * @param payType    支付方式 wx: 微信 ali: 支付宝
     */
    @DS("#pid")
    @Transactional
    public UserDetail handlePayNotice(String pid, String outTradeNo, String payType) {
        //查询订单
        OrgUserOrder userOrder = payMapper.getUserOrderByOutTradeNo(outTradeNo);
        if (userOrder == null) {
            log.warn("[支付回调] 未找到订单 outTradeNo={}", outTradeNo);
            throw new MyBaseException(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "未找到订单: " + outTradeNo);
        }
        if (userOrder.getStatus() != null && userOrder.getStatus() == 2) {
            log.info("[支付回调] 订单已是已付款状态，无需重复处理 outTradeNo={}", outTradeNo);
            return null;
        }
        //查询商品
        ProductDoMain queryProduct = new ProductDoMain();
        queryProduct.setProductname(userOrder.getProductName());
        ProductDoMain product = getProductInfo(pid, queryProduct, userOrder.getAppid(), userOrder.getOsid(), null, userOrder.getDf());
        if (product == null) {
            log.warn("[支付回调] 未找到商品 product={}", userOrder.getProductName());
            throw new MyBaseException(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "未找到商品: " + userOrder.getProductName());
        }
        // 1、更新订单状态为已付款
        int updateRows = payMapper.updateOrderStatusByOutTradeNo(userOrder.getOutTradeNo(), 2);
        log.info("[支付回调] 更新订单状态，outTradeNo={}, status=2, 影响行数={}", userOrder.getOutTradeNo(), updateRows);
        // 2、写入单据流水
        OrgBills bills = new OrgBills();
        bills.setUserid(userOrder.getUserid());
        bills.setTime(LocalDateTime.now());
        bills.setFee(userOrder.getFee());
        bills.setTradeno(userOrder.getOutTradeNo());
        bills.setDescription("支付订单, 商品名称：" + product.getTitle());
        bills.setType(this.forwardPayInt(payType));
        bills.setAppid(userOrder.getAppid());
        bills.setDf(userOrder.getDf());
        bills.setMchId(userOrder.getMchId());
        payMapper.insertOrgBills(bills);
        log.info("[支付回调] 写入单据流水成功 outTradeNo={}", userOrder.getOutTradeNo());
        // 3、写入订单订阅信息
        if (product.getProducttype() != Constant.ProductType.OIL) {
            OrgSubscription orgSubscription = payMapper.selectSubscribeByUserId(userOrder.getUserid());
            LocalDateTime endTime = LocalDateTime.now();
            if (null != orgSubscription
                    && null != orgSubscription.getEndtime()
                    && orgSubscription.getEndtime().isAfter(LocalDateTime.now())) {
                endTime = orgSubscription.getEndtime();
            }
            OrgSubscription subscription = new OrgSubscription();
            subscription.setUserid(userOrder.getUserid());
            subscription.setOutTradeNo(userOrder.getOutTradeNo());
            subscription.setCreatetime(LocalDateTime.now());
            subscription.setFee(userOrder.getFee());
            subscription.setEndtime(endTime.plusDays(product.getDuration()));
            subscription.setStatus("1"); // 1付费用户
            subscription.setPayment(payType);
            subscription.setValid("1"); // 1有效
            subscription.setAppselflimit("");
            subscription.setVideoDuration(0);
            subscription.setAiimgNum(0);
            // 4、将其他状态改为0
            payMapper.updateSubscribeByUserId(userOrder.getUserid(), 0);
            payMapper.insertOrgSubscription(subscription);
        }
        log.info("[支付回调] 写入订单订阅信息成功 outTradeNo={}", userOrder.getOutTradeNo());
        // 4、推文则推送PHP
        baseService.pushOldRights(
                userOrder.getOutTradeNo(),
                userOrder.getAppid(),
                userOrder.getUserid() + "",
                product.getAlias(), pid);
        // 5、返回用户信息
        UserDetail userDetail = new UserDetail();
        userDetail.setAppid(userOrder.getAppid());
        userDetail.setUserid(userOrder.getUserid());
        userDetail.setOsid(userOrder.getOsid());
        userDetail.setDf(userOrder.getDf());
        userDetail.setProducttype(product.getProducttype());
        userDetail.setProductid(product.getProductid());
        log.info("[支付回调] 返回用户信息： {}", JSONUtil.toJsonStr(userDetail));
        return userDetail;
    }


    // 13:微信 12:支付宝 14：苹果
    private int forwardPayInt(String payType) {
        if (payType.equals(PayType.WX.getCode()) || payType.equals(PayType.TL.getCode())) {
            return 13;
        }
        if (payType.equals(PayType.ALI.getCode())) {
            return 12;
        }
        if (payType.equals(PayType.APPLE.getCode())) {
            return 14;
        }
        return 13;
    }


    /**
     * 获取需要支付订阅的记录
     *
     * @param pid        业务ID
     * @param deductTime 扣减时间
     * @return 订阅列表
     */
    @DS("#pid")
    public List<PaySubscribe> selectNeedPaySubscribe(String pid, String deductTime) {
        return payMapper.selectNeedPaySubscribe(deductTime);
    }

    /**
     * 获取需要支付订阅的记录
     *
     * @param pid         业务ID
     * @param mchid       商户号
     * @param userid      用户id
     * @param deducttime  扣款时间
     * @param productname 产品名称
     * @return 订阅列表
     */
    @DS("#pid")
    public List<OrgUserOrder> selectNoPaySubscribeOrder(String pid,
                                                        String mchid,
                                                        Integer userid,
                                                        String deducttime,
                                                        String productname) {
        return payMapper.selectNoPaySubscribeOrder(mchid, userid, deducttime, productname);
    }

    /**
     * 通过商家AppID获取支付配置
     */
    @DS("#pid")
    public PayConfig getPayChannelConfig(String pid, String appId, String payChannel) {
        if (StrUtil.isBlank(appId) || StrUtil.isBlank(payChannel)) {
            log.warn("获取支付配置参数为空, appId={}, payChannel={}", appId, payChannel);
            return null;
        }
        return payMapper.getPayChannelConfigByPayChannel(appId, payChannel);
    }

    /**
     * 更新下次扣款时间
     *
     * @param pid            业务ID
     * @param nextdeducttime 扣款时间
     * @return 成功失败
     */
    @DS("#pid")
    public int updatePaySubscribeNextDeductTime(String pid, Integer id, String nextdeducttime) {
        return payMapper.updatePaySubscribeNextDeductTime(id, nextdeducttime);
    }

    /**
     * 根据首次订单号查询支付订阅记录
     *
     * @param pid             业务ID
     * @param firstouttradeno 首次单号
     * @return 订阅结果
     */
    @DS("#pid")
    public PaySubscribe selectPaySubscribeByFirstOutTradeNo(String pid, String firstouttradeno) {
        return payMapper.selectPaySubscribeByFirstOutTradeNo(firstouttradeno);
    }

    /**
     * 根据订单编号查询订单信息
     *
     * @param pid        业务ID
     * @param outtradeno 订单编号
     * @return 订单信息
     */
    @DS("#pid")
    public OrgUserOrder getUserOrder(String pid, String outtradeno) {
        return payMapper.getUserOrder(outtradeno);
    }


}
