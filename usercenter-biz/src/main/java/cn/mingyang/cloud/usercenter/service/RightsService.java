package cn.mingyang.cloud.usercenter.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.comEnum.BaseErrorCodeEnum;
import cn.mingyang.cloud.center.common.dao.entity.FunctionPriceItem;
import cn.mingyang.cloud.center.common.exception.MyBaseException;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.center.common.service.RedisService;
import cn.mingyang.cloud.center.common.util.DateTimeUtils;
import cn.mingyang.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.framework.common.util.object.BeanUtils;
import cn.mingyang.cloud.usercenter.api.response.UserRightResponse;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import cn.mingyang.cloud.usercenter.comEnum.FunctionEnum;
import cn.mingyang.cloud.usercenter.dao.domain.RightItems;
import cn.mingyang.cloud.usercenter.dao.domain.RightsTemp;
import cn.mingyang.cloud.usercenter.dao.domain.UserRightsCache;
import cn.mingyang.cloud.usercenter.dao.entity.SellPointInfo;
import cn.mingyang.cloud.usercenter.dao.entity.UserRightInfo;
import cn.mingyang.cloud.usercenter.dao.entity.UserRightsRecord;
import cn.mingyang.cloud.usercenter.dao.mapper.RightsMapper;
import cn.mingyang.cloud.usercenter.request.AddUserRightsRequest;
import cn.mingyang.cloud.usercenter.request.ExpendUserRightsRequest;
import cn.mingyang.cloud.usercenter.request.SyncUserRightsRequest;
import cn.mingyang.cloud.usercenter.request.UserRightsRecordRequest;
import cn.mingyang.cloud.usercenter.response.ExpendRightsResponse;
import cn.mingyang.cloud.usercenter.response.GetUserRightResponse;
import cn.mingyang.cloud.usercenter.response.UserRightRecordResponse;
import com.alibaba.druid.util.StringUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 权益处理Service
 * 处理用户权益相关的业务逻辑，包括：
 * 1. 获取用户权益信息
 * 2. 扣除用户权益
 * 3. 重置用户权益
 * 4. 获取功能价格列表
 */
@Slf4j
@Service
public class RightsService {

    @Value("${rights.allow.sellGroup:}")
    private String allowSellGroup;

    private final RightsMapper rightsMapper;
    private final RedisService redisService;
    private final BaseService baseService;
    private final DbCacheService dbCacheService;

    public RightsService(RightsMapper rightsMapper,
                         RedisService redisService,
                         BaseService baseService,
                         DbCacheService dbCacheService) {
        this.rightsMapper = rightsMapper;
        this.redisService = redisService;
        this.baseService = baseService;
        this.dbCacheService = dbCacheService;
    }

    /**
     * 获取用户权益信息
     *
     * @param cmd        命令请求对象
     * @param comRequest 通用请求对象
     * @return 用户权益信息
     */
    @DS("#cmd.ds")
    public CommonResult<UserRightResponse> getUserRightInfo(CmdRequest cmd, ComRequest comRequest) {
        String userId = comRequest.getBase().getUserid();
        log.info("Getting user right info for userId: {}", userId);
        try {
            // 构建缓存key
            String userRightCacheKey = UserRightsCache.buildCacheKey(cmd.getDs(), Integer.parseInt(userId));
            // 获取现有权益缓存
            UserRightsCache existCache = dbCacheService.getExistingCache(userRightCacheKey);
            if (null == existCache) {
                return CommonResult.error(GlobalErrorCodeConstants.HAS_NOT_PERMISSIONS);
            }
            return CommonResult.success(initRightResponse(cmd.getDs(), existCache));
        } catch (Exception e) {
            log.error("Error getting user right info for userId: {}, error: {}", userId, e.getMessage(), e);
            return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR);
        }
    }


    /**
     * 推文版本9.0，调用老的权益
     *
     * @param cmd           命令参数
     * @param isCheck       是否校验
     * @param comRequest    基础参数
     * @param expendRequest 扣减参数
     * @return 处理结果
     */
    public CommonResult<?> expendRightByTw(CmdRequest cmd, boolean isCheck, ComRequest comRequest, ExpendUserRightsRequest expendRequest) {
        int functionId = expendRequest.getFunctionid();
        int quantity = expendRequest.getQuantity();
        FunctionEnum functionEnum = FunctionEnum.getFunctionEnumByCmd(functionId);
        if (null != functionEnum && functionEnum.getExpendType() != 3) {
            if (isCheck) {
                return baseService.checkRights(cmd, comRequest, quantity, functionId);
            }
            return baseService.userPolicyOldConsume(cmd, comRequest, quantity, functionId);
        }
        return CommonResult.success(null);
    }

    /**
     * 扣除用户权益
     *
     * @param cmd        命令请求对象
     * @param isCheck    是否仅检查
     * @param comRequest 通用请求对象
     * @return 处理结果
     */
    @DS("#cmd.ds")
    @Transactional
    public CommonResult<?> expendRight(CmdRequest cmd, boolean isCheck, ComRequest comRequest, ExpendUserRightsRequest expendRequest) {
        String userId = comRequest.getBase().getUserid();
        int functionId = expendRequest.getFunctionid();
        int quantity = expendRequest.getQuantity();
        String osid = comRequest.getBase().getOsid();
        // 校验会员等级不需要更新数据库和缓存
        if (null != expendRequest.getCheckvip() && expendRequest.getCheckvip() == 1) {
            isCheck = true;
        }
        // 记录权益消费
        List<UserRightsRecord> rightsRecords = new ArrayList<>();
        log.info("Processing right expenditure - userId: {}, functionId: {}, quantity: {}, isCheck: {}",
                userId, functionId, quantity, isCheck);
        try {
            // 获取用户权益信息缓存
            String userRightCacheKey = UserRightsCache.buildCacheKey(cmd.getDs(), Integer.parseInt(userId));
            // 获取现有权益缓存
            UserRightsCache existCache = dbCacheService.getExistingCache(userRightCacheKey);
            if (null == existCache) {
                return CommonResult.error(GlobalErrorCodeConstants.HAS_NOT_PERMISSIONS);
            }
            // 赋值金币权益缓存，放在多扣除
            UserRightsCache coinsCache = BeanUtils.toBean(existCache, UserRightsCache.class);

            // 获取功能价格信息
            List<FunctionPriceItem> functionItems = getFunctionPriceItem(cmd.getAppId(), osid, functionId);
            if (CollectionUtil.isEmpty(functionItems)) {
                log.error("Function price not found for functionId: {}", functionId);
                return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST);
            }

            // 按照排序进行循环消费
            String errorCode;
            Map<Integer, List<FunctionPriceItem>> functionPriceMap = functionItems.stream().collect(Collectors.groupingBy(FunctionPriceItem::getOrder));

            //扣除权益
            errorCode = expendRightsByOrder(functionPriceMap.get(0), existCache, expendRequest, rightsRecords, isCheck);
            //权益扣除失败，扣除金币
            if (null != errorCode && CollectionUtil.isNotEmpty(functionPriceMap.get(1))) {
                // PPT 最后消耗金币
                if (cmd.getDs().equals(Constant.AllDs.PPT)) {
                    //清空之前记录，生成新的记录
                    if (CollectionUtil.isNotEmpty(rightsRecords)) {
                        rightsRecords.clear();
                    }
                    //固定消耗金币
                    FunctionPriceItem prt = functionPriceMap.get(1).get(0);
                    //计算消耗总额
                    float total = rightsToCoins(functionPriceMap.get(0), prt, expendRequest.getQuantity());
                    errorCode = expendRights(functionPriceMap.get(1).get(0), coinsCache, expendRequest, total, isCheck, rightsRecords);
                    existCache = BeanUtils.toBean(coinsCache, UserRightsCache.class);
                }
            }
            // 返回结果
            if (null == errorCode) {
                //如果不是校验，则更新缓存
                if (!isCheck && quantity > 0) {
                    //同步到数据库(先同步到数据库，后期数据量大，考虑异步任务执行)
                    rightsMapper.syncDatabaseFromRedis(existCache);
                    //更新缓存
                    dbCacheService.saveCache(UserRightsCache.buildCacheKey(cmd.getDs(), Integer.parseInt(userId)), existCache);
                    //如果是推文，则扣减老权益
                    if (List.of(Constant.AllDs.AITW, Constant.AllDs.MIX).contains(cmd.getDs())) {
                        Integer oldQuantity = quantity;
                        if (null != expendRequest.getOldQuantity() && expendRequest.getOldQuantity() > 0) {
                            oldQuantity = expendRequest.getOldQuantity();
                        }
                        baseService.userPolicyConsume(cmd, comRequest, oldQuantity, functionId);
                    }
                    //写入记录
                    // 记录用户权益初始化记录
                    if (CollectionUtil.isNotEmpty(rightsRecords)) {
                        rightsMapper.batchInsertUserRightsRecord(rightsRecords);
                    }
                    //写入到同步缓存
                    //userRightsCacheSync(cmd.getDs() + "_" + appId + "_" + userId);
                }
                //返回参数
                return CommonResult.success(initRightResponse(cmd.getDs(), dbCacheService.getExistingCache(userRightCacheKey)));
            } else {
                return CommonResult.error(errorCode, functionPriceMap.get(0).get(0).getErrmessage());
            }
        } catch (MyBaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error processing right expenditure for userId: {}, error: {}", userId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建返回信息
     *
     * @param existCache 权益缓存
     * @return 返回信息
     */
    private UserRightResponse initRightResponse(String pid, UserRightsCache existCache) {
        if (null == existCache) {
            return null;
        }
        // 查询卖点信息
        List<SellPointInfo> sellPointInfos = dbCacheService.getAllSellPoint(pid, new ArrayList<>());
        GetUserRightResponse rightResponse = GetUserRightResponse.buildUserRightDetailResponseByCache(existCache, sellPointInfos);
        UserRightResponse response = BeanUtils.toBean(rightResponse, UserRightResponse.class);
        response.setUserid(existCache.getUserid() + "");
        if (CollectionUtil.isNotEmpty(rightResponse.getCoinsdetail())) {
            response.setCoinsdetail(JSONUtil.parseArray(rightResponse.getCoinsdetail()));
        }
        if (CollectionUtil.isNotEmpty(rightResponse.getNumberdetail())) {
            response.setNumberdetail(JSONUtil.parseArray(rightResponse.getNumberdetail()));
        }
        if (CollectionUtil.isNotEmpty(rightResponse.getTimesdetail())) {
            response.setTimesdetail(JSONUtil.parseArray(rightResponse.getTimesdetail()));
        }
        return response;
    }

    /**
     * 权益转金币
     *
     * @param priceItems 功能价格
     * @param prt        固定功能价格
     * @param quantity   消耗数量
     * @return 消耗金币数
     */
    private float rightsToCoins(List<FunctionPriceItem> priceItems, FunctionPriceItem prt, Integer quantity) {
        float expendSum = 0f;
        for (FunctionPriceItem priceItem : priceItems) {
            // 如果是数量，按固定值
            if (priceItem.getExpendtype() == Constant.SellType.NUMBERCHARGING) {
                expendSum = expendSum + (prt.getExpend() * quantity);
            } else {
                expendSum = expendSum + (priceItem.getExpend() * quantity);
            }
        }
        return expendSum;
    }

    /**
     * 根据订单顺序消费权益
     *
     * @param priceItems    功能价格表
     * @param existCache    权益缓存
     * @param expendRequest 消费请求
     * @param rightsRecords 消费记录
     * @param isCheck       是否校验
     * @return 结果
     */
    private String expendRightsByOrder(List<FunctionPriceItem> priceItems,
                                       UserRightsCache existCache,
                                       ExpendUserRightsRequest expendRequest,
                                       List<UserRightsRecord> rightsRecords,
                                       boolean isCheck) {
        String errorCode = null;
        for (FunctionPriceItem priceItem : priceItems) {
            // 计算总消耗
            float total = priceItem.getExpend() * expendRequest.getQuantity();
            errorCode = expendRights(priceItem, existCache, expendRequest, total, isCheck, rightsRecords);
            if (null != errorCode) {
                break;
            }
        }
        return errorCode;
    }

    /**
     * 初始化权益消费记录
     *
     * @param userId     用户ID
     * @param total      消耗总量
     * @param functionId 功能ID
     * @param sellgroup  卖点ID
     * @param desc       描述
     * @return 记录
     */
    private UserRightsRecord initExpendRightsRecord(Integer userId, float total, Integer functionId, Integer sellgroup, String desc) {
        UserRightsRecord rightsRecord = new UserRightsRecord();
        rightsRecord.setUserid(userId);
        rightsRecord.setFunctionid(functionId);
        rightsRecord.setSellgroup(sellgroup);
        rightsRecord.setQuantity(total);
        rightsRecord.setRecordtype(Constant.AddRightsFlag.EXPEND);
        rightsRecord.setDesc(desc);
        return rightsRecord;
    }


    /**
     * 扣除权益
     *
     * @param priceItem     功能价格
     * @param existCache    用户权益
     * @param expendRequest 请求信息
     * @param total         消耗总量
     * @param isCheck       是否校验
     * @return 是否成功
     */
    private String expendRights(FunctionPriceItem priceItem,
                                UserRightsCache existCache,
                                ExpendUserRightsRequest expendRequest,
                                float total,
                                boolean isCheck,
                                List<UserRightsRecord> rightsRecords) {
        //只校验会员等级
        if (null != expendRequest.getCheckvip() && expendRequest.getCheckvip() == 1) {
            //会员等级
            int userType = existCache.getVipType();
            //权益限制
            int rightType = priceItem.getRighttype();
            log.info("校验会员等级, userId: {}, functionId: {}, checkVip: {}, userType: {}, rightType: {}",
                    existCache.getUserid(), priceItem.getFunctionid(), expendRequest.getCheckvip(), userType, rightType);
            //没有限制，都可以用
            if (rightType == Constant.RightType.NOLIMIT) {
                return null;
            }
            //如果是VIP，则校验VIP是否可用
            if (userType == Constant.VIPType.VIP && Arrays.asList(Constant.RightType.VIP, Constant.RightType.VIPOIL).contains(rightType)) {
                return null;
            }
            //如果是SVIP，则都可以用
            if (userType == Constant.VIPType.SVIP) {
                return null;
            }
            return GlobalErrorCodeConstants.USER_NOT_IS_VIP.getCode();
        }
        log.info("Calculated total expenditure: {} for userId: {} for functionId: {} for sellGroupId: {}", total, existCache.getUserid(), priceItem.getFunctionid(), priceItem.getSellgroup());
        // 权益字符串
        String rightString = getRightStringByType(existCache, priceItem.getExpendtype());
        if (StringUtils.isEmpty(rightString)) {
            log.warn("Empty right string for type: {}", priceItem.getExpendtype());
            return GlobalErrorCodeConstants.NOT_HAVE_THIS_RIGHTS.getCode();
        }
        //进行资源扣减
        RightItems rightItems = new RightItems(rightString);
        if (CollectionUtil.isEmpty(rightItems.getRightItems())) {
            log.warn("Empty right list for type: {}", priceItem.getExpendtype());
            return GlobalErrorCodeConstants.NOT_HAVE_THIS_RIGHTS.getCode();
        }
        // 设置消费类型状态
        ExpendRightsResponse expendResponse = new ExpendRightsResponse();
        expendResponse.setExstatus(0);
        boolean isExpend = processRightExpenditure(existCache, priceItem, total, expendResponse, rightItems, isCheck, rightsRecords);
        if (!isExpend) {
            log.warn("Failed to expend right userId: {} for sellgroup: {}, total: {}", existCache.getUserid(), priceItem.getSellgroup(), total);
            if (existCache.getVipType() == Constant.VIPType.NOTVIP) {
                return GlobalErrorCodeConstants.ATTEMPTS_TIMES_USE_UP.getCode();
            } else {
                return GlobalErrorCodeConstants.DAILY_TIMES_USE_UP.getCode();
            }
        }
        //更新权益
        updateRightString(existCache, priceItem.getExpendtype(), rightItems.getJString());
        return null;
    }

    /**
     * 获取用户权益信息并进行验证
     */
    public UserRightInfo getUserRightInfoWithValidation(String db, String userId, List<UserRightsRecord> rightsRecords) {
        UserRightInfo userRightInfo = rightsMapper.getUserRightInfo(Integer.parseInt(userId));
        // 如果缓存和数据库都没有权益，则从老的权益同步
        UserRightInfo oldRightInfo = getOldRights(db, Integer.parseInt(userId), rightsRecords, userRightInfo);
        if (userRightInfo == null) {
            if (null != oldRightInfo) {
                //写入到权益表中
                rightsMapper.insertUserRightsInfo(oldRightInfo);
                log.info("Old user right sync into new right, userId: {} rights: {}", userId, JSONUtil.toJsonStr(oldRightInfo));
            }
            return oldRightInfo;
        } else {//加校验，放在数据库和缓存都有权益，但是与老会员时间对不上，也需要从老的再同步一次；
            // 老的用户权益
            GetUserRightResponse oldRightResponse = GetUserRightResponse.buildUserRightResponse(oldRightInfo);
            // 数据库用户权益
            GetUserRightResponse userRightResponse = GetUserRightResponse.buildUserRightResponse(userRightInfo);
            if (!StringUtils.isEmpty(userRightResponse.getVipendtime())) {
                if (oldRightResponse != null && !StringUtils.isEmpty(oldRightResponse.getVipendtime())) {
                    // 新老权益都有VIP信息，但是时间不一样，也要从老的同步
                    if (!DateTimeUtils.compareDateIsSample(oldRightResponse.getVipendtime(), userRightResponse.getVipendtime())) {
                        log.info("Old user right sync into new right by datetime different, userId: {} rights: {}", userId, JSONUtil.toJsonStr(userRightInfo));
                        return oldRightInfo;
                    }
                }
            } else {
                // 新权益不是VIP,老权益是VIP,需要同步老权益
                if (oldRightResponse != null && !StringUtils.isEmpty(oldRightResponse.getVipendtime())) {
                    log.info("Old user right sync into new right by viptime, userId: {} rights: {}", userId, JSONUtil.toJsonStr(oldRightInfo));
                    return oldRightInfo;
                }
            }
            // 新老权益SVIP时间不一致，需要同步老权益
            if (oldRightResponse != null && !Objects.equals(userRightResponse.getSvipendtime(), oldRightResponse.getSvipendtime())) {
                log.info("Old user right sync into new right by sviptime, userId: {} rights: {}", userId, JSONUtil.toJsonStr(oldRightInfo));
                return oldRightInfo;
            }
        }
        return userRightInfo;
    }


    /**
     * 获取功能价格信息
     */
    private List<FunctionPriceItem> getFunctionPriceItem(String appId, String osid, int functionId) {
        List<FunctionPriceItem> functionPriceItems = rightsMapper.getFunctionPriceByFunctionId(functionId);
        if (CollectionUtil.isEmpty(functionPriceItems)) {
            log.warn("No function price items found for functionId: {}", functionId);
            return null;
        }
        // 查找应用特定的价格项,先应用，如果应用没有则查default
        List<FunctionPriceItem> priceItems = functionPriceItems.stream()
                .filter(item -> item.getAppid().equals(appId) && item.getOsid().equals(osid))
                .collect(Collectors.toList());
        // 根据应用和默认osid获取
        if (CollectionUtil.isEmpty(priceItems)) {
            priceItems = functionPriceItems.stream()
                    .filter(item -> item.getAppid().equals(appId) && item.getOsid().equals(Constant.DEFAULT))
                    .collect(Collectors.toList());
        }
        // 根据默认应用和osid获取
        if (CollectionUtil.isEmpty(priceItems)) {
            priceItems = functionPriceItems.stream()
                    .filter(item -> item.getAppid().equals(Constant.DEFAULT) && item.getOsid().equals(osid))
                    .collect(Collectors.toList());
        }
        // 根据默认应用和默认osid获取
        if (CollectionUtil.isEmpty(priceItems)) {
            priceItems = functionPriceItems.stream()
                    .filter(item -> item.getAppid().equals(Constant.DEFAULT) && item.getOsid().equals(Constant.DEFAULT))
                    .collect(Collectors.toList());
        }
        return priceItems.stream().sorted(Comparator.comparing(FunctionPriceItem::getOrder)).collect(Collectors.toList());
    }

    /**
     * 处理权益消耗
     */
    private boolean processRightExpenditure(
            UserRightsCache existCache,
            FunctionPriceItem priceItem,
            float total,
            ExpendRightsResponse expendResponse,
            RightItems rightItems,
            boolean isCheck,
            List<UserRightsRecord> rightsRecords) {
        // 设置默认消费组
        int sellGroup = priceItem.getSellgroup();
        int rightType = priceItem.getRighttype();
        // 非会员且配置了共享消费组，则替换成共享消费组
        if (null != priceItem.getSharesellgroup()
                && priceItem.getSharesellgroup() > 0
                && existCache.getVipType() == Constant.VIPType.NOTVIP) {
            sellGroup = priceItem.getSharesellgroup();
            rightType = Constant.RightType.NOLIMIT;
            log.info("forward calculated total expenditure: {} for userId: {} for functionId: {} for sellGroupId: {}",
                    total, existCache.getUserid(), priceItem.getFunctionid(), sellGroup);
        }
        // 处理权益消耗
        boolean flag = rightItems.expendRight(sellGroup, total, rightType, existCache.getVipType(), expendResponse, isCheck);
        // 如果成功，则写入记录
        if (flag) {
            rightsRecords.add(initExpendRightsRecord(existCache.getUserid(), total, priceItem.getFunctionid(), sellGroup, priceItem.getNote()));
        }
        return flag;
    }

    /**
     * 根据类型获取权益字符串
     */
    private String getRightStringByType(UserRightsCache existCache, int type) {
        switch (type) {
            case Constant.SellType.COINS:
                return existCache.getCoins();
            case Constant.SellType.NUMBERCHARGING:
                return existCache.getNumbercharging();
            case Constant.SellType.TIMESCHARGING:
                return existCache.getTimescharging();
            default:
                log.warn("Unknown right type: {}", type);
                return null;
        }
    }

    /**
     * 更新权益字符串
     */
    private void updateRightString(UserRightsCache existCache, int type, String rightString) {
        switch (type) {
            case Constant.SellType.COINS:
                existCache.setCoins(rightString);
                break;
            case Constant.SellType.NUMBERCHARGING:
                existCache.setNumbercharging(rightString);
                break;
            case Constant.SellType.TIMESCHARGING:
                existCache.setTimescharging(rightString);
                break;
            default:
                log.warn("Unknown right type for update: {}", type);
        }
    }

    /**
     * 获取所有功能价格列表
     *
     * @param cmd 命令请求对象
     * @param com 通用请求对象
     * @return 功能价格列表
     */
    @DS("#cmd.ds")
    public CommonResult<List<FunctionPriceItem>> getAllFunctionPrice(CmdRequest cmd, ComRequest com) {
        String appId = cmd.getAppId();
        String osid = com.getBase().getOsid();
        log.info("Getting all function prices for appId: {}, osid: {}", appId, osid);
        try {
            List<FunctionPriceItem> functionPriceItems = rightsMapper.getFunctionPriceByAppid(appId, osid);
            if (CollectionUtil.isEmpty(functionPriceItems)) {
                log.info("No function prices found for appId: {}, trying default", appId);
                functionPriceItems = rightsMapper.getFunctionPriceByAppid(Constant.DEFAULT, osid);
                if (CollectionUtil.isEmpty(functionPriceItems)) {
                    functionPriceItems = rightsMapper.getFunctionPriceByAppid(Constant.DEFAULT, Constant.DEFAULT);
                }
            }
            if (CollectionUtil.isEmpty(functionPriceItems)) {
                return CommonResult.error(BaseErrorCodeEnum.NULL_FUNCTION_EXIST.getCode(), BaseErrorCodeEnum.NULL_FUNCTION_EXIST.getMessage());
            }
            log.info("Found {} function prices", functionPriceItems.size());
            return CommonResult.success(functionPriceItems);
        } catch (Exception e) {
            log.error("Error getting function prices for appId: {}, error: {}", appId, e.getMessage(), e);
            return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 重置用户权益
     *
     * @param cmd        命令请求对象
     * @param comRequest 通用请求对象
     * @return 重置后的用户权益信息
     */
    @DS("#cmd.ds")
    @Transactional
    public CommonResult<UserRightResponse> resetRight(CmdRequest cmd, ComRequest comRequest) {
        String userId = comRequest.getBase().getUserid();
        log.info("Processing right reset for userId: {}", userId);
        try {
            // 拿到重置锁，如果没拿到则跳过
            boolean isReset = dbCacheService.checkRested(cmd.getDs() + "_" + userId);
            if (!isReset) {
                log.warn("Reset right is end, not need reset, user: {}", cmd.getDs() + "_" + userId);
                return CommonResult.success(null);
            }
            // 记录重置权益
            List<UserRightsRecord> rightsRecords = new ArrayList<>();
            // 获取现有权益缓存,如果缓存没有，则从数据库中获取
            UserRightsCache existCache;
            // 获取用户权益信息
            UserRightInfo userRightInfo = getUserRightInfoWithValidation(cmd.getDs(), userId, rightsRecords);
            if (userRightInfo == null) {
                return CommonResult.error(GlobalErrorCodeConstants.USER_NOT_FOUND);
            }
            existCache = BeanUtils.toBean(userRightInfo, UserRightsCache.class);
            // 重置权益缓存
            resetRightsCache(cmd.getDs(), userRightInfo, existCache);
            // 更新数据库
            rightsMapper.syncDatabaseFromRedis(existCache);
            // 保存到缓存
            dbCacheService.saveCache(UserRightsCache.buildCacheKey(cmd.getDs(), Integer.parseInt(userId)), existCache);
            // 记录用户权益初始化记录
            if (CollectionUtil.isNotEmpty(rightsRecords)) {
                rightsMapper.batchInsertUserRightsRecord(rightsRecords);
            }
            return CommonResult.success(initRightResponse(cmd.getDs(),
                    dbCacheService.getExistingCache(UserRightsCache.buildCacheKey(cmd.getDs(), Integer.parseInt(userId)))));
        } catch (MyBaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error resetting rights for userId: {}, error: {}", userId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 重置权益缓存
     *
     * @param pid           数据源
     * @param userRightInfo 用户信息
     * @param existCache    权益缓存
     */
    private void resetRightsCache(String pid, UserRightInfo userRightInfo, UserRightsCache existCache) {
        log.info("[resetRightsCache] userId: {}", userRightInfo.getUserid());
        //查询所有卖点信息
        List<SellPointInfo> pointInfos = dbCacheService.getAllSellPoint(pid, new ArrayList<>());
        //重置金币
        existCache.setCoins(resetRights(
                userRightInfo,
                existCache.getCoins(),
                pointInfos.stream().filter(x -> x.getSelltype() == Constant.SellType.COINS
                        && x.getResettype() == Constant.ResetType.VIPRESET).toList()));
        //重置数量
        existCache.setNumbercharging(resetRights(
                userRightInfo,
                existCache.getNumbercharging(),
                pointInfos.stream().filter(x -> x.getSelltype() == Constant.SellType.NUMBERCHARGING
                        && x.getResettype() == Constant.ResetType.VIPRESET).toList()));
        //重置次数
        existCache.setTimescharging(resetRights(
                userRightInfo,
                existCache.getTimescharging(),
                pointInfos.stream().filter(x -> x.getSelltype() == Constant.SellType.TIMESCHARGING
                        && x.getResettype() == Constant.ResetType.VIPRESET).toList()));
    }


    /**
     * 重置权益
     *
     * @param rightString 权益字符串
     */
    private String resetRights(UserRightInfo userRightInfo, String rightString, List<SellPointInfo> pointInfos) {
        if (!StringUtils.isEmpty(rightString)) {
            RightItems rightItems = new RightItems(rightString);
            rightItems.resetAllRight(userRightInfo.getVipType());
            // 如果是非会员，则清空重置权益和加油包;会员则清空免费
            if (userRightInfo.getVipType() == Constant.VIPType.NOTVIP) {
                for (RightItems.RightItem rightItem : rightItems.getRightItems()) {
                    rightItem.setQ(0f);
                    // 产品确认，会员到期加油包不清零
                    //rightItem.setO(0f);
                }
            } else {
                for (RightItems.RightItem rightItem : rightItems.getRightItems()) {
                    if (rightItem.getF() > 0) {
                        rightItem.setF(0f);
                    }
                }
            }
            // 如果存在没有写入的权益，则更新到权益表中;同时如果重置数量不一致，则更新
            if (CollectionUtil.isNotEmpty(pointInfos)) {
                // 遍历所有卖点信息
                for (SellPointInfo sp : pointInfos) {
                    // 判断点位是否存在，不存在则新增
                    boolean isExists = false;
                    for (RightItems.RightItem rightItem : rightItems.getRightItems()) {
                        if (sp.getSellgroup() == rightItem.getI()) {
                            // 判断vip数量是否一致
                            if (userRightInfo.getVipType() == Constant.VIPType.VIP
                                    && sp.getQuantity() != rightItem.getRq()) {
                                rightItem.setRq(sp.getQuantity());
                            }
                            // 判断svip数量是否一致
                            if (userRightInfo.getVipType() == Constant.VIPType.SVIP
                                    && sp.getSvipquantity() > 0
                                    && sp.getSvipquantity() != rightItem.getRq()) {
                                rightItem.setRq(sp.getSvipquantity());
                            }
                            isExists = true;
                        }
                    }
                    // 不存在则新增
                    if (!isExists) {
                        RightItems.RightItem rightItem = new RightItems.RightItem();
                        rightItem.setI(sp.getSellgroup());
                        // 会员则写入会员权益
                        if (userRightInfo.getVipType() != Constant.VIPType.NOTVIP) {
                            rightItem.setT(DateTimeUtils.afterCurrentDateTimeZero(sp.getResetunit()));
                            rightItem.setR(sp.getResetunit());
                            rightItem.setRq(sp.getQuantity());
                            rightItem.setQ(sp.getQuantity());
                            if (userRightInfo.getVipType() == Constant.VIPType.SVIP) {
                                rightItem.setRq(sp.getSvipquantity());
                                rightItem.setQ(sp.getSvipquantity());
                            }
                            rightItem.setRt(sp.getResettype());
                        } else {
                            rightItem.setT("");
                            rightItem.setO(0);
                            rightItem.setF(sp.getFree());
                            rightItem.setRt(1);
                            rightItem.setR(-1);
                        }
                        if (rightItem.getF() <= 0 && rightItem.getQ() <= 0) {
                            continue;
                        }
                        if (CollectionUtil.isEmpty(rightItems.getRightItems())) {
                            rightItems.setRightItems(new ArrayList<>());
                        }
                        rightItems.getRightItems().add(rightItem);
                    }
                }
            }
            return rightItems.getJString();
        } else {
            RightItems rightItems = new RightItems();
            // 遍历所有卖点信息
            for (SellPointInfo sp : pointInfos) {
                RightItems.RightItem rightItem = new RightItems.RightItem();
                rightItem.setI(sp.getSellgroup());
                // 会员则写入会员权益
                if (userRightInfo.getVipType() != Constant.VIPType.NOTVIP) {
                    rightItem.setT(DateTimeUtils.afterCurrentDateTimeZero(sp.getResetunit()));
                    rightItem.setR(sp.getResetunit());
                    rightItem.setRq(sp.getQuantity());
                    rightItem.setQ(sp.getQuantity());
                    if (userRightInfo.getVipType() == Constant.VIPType.SVIP) {
                        rightItem.setRq(sp.getSvipquantity());
                        rightItem.setQ(sp.getSvipquantity());
                    }
                    rightItem.setRt(sp.getResettype());
                } else {
                    rightItem.setT("");
                    rightItem.setO(0);
                    rightItem.setF(sp.getFree());
                    rightItem.setRt(1);
                    rightItem.setR(-1);
                }
                if (rightItem.getF() <= 0 && rightItem.getQ() <= 0) {
                    continue;
                }
                if (CollectionUtil.isEmpty(rightItems.getRightItems())) {
                    rightItems.setRightItems(new ArrayList<>());
                }
                rightItems.getRightItems().add(rightItem);
            }
            if (CollectionUtil.isNotEmpty(rightItems.getRightItems())) {
                return rightItems.getJString();
            }
        }
        return rightString;
    }


    /**
     * 权益数据同步到数据库
     *
     * @param db     数据源
     * @param appId  应用ID
     * @param userId 用户ID
     */
    @DS("#db")
    public void userRightsSync(String db, String appId, String userId) {
        // 获取现有权益缓存,如果缓存没有，则从数据库中获取
        String userRightCacheKey = UserRightsCache.buildCacheKey(db, Integer.parseInt(userId));
        UserRightsCache existCache = dbCacheService.getExistingCache(userRightCacheKey);
        rightsMapper.syncDatabaseFromRedis(existCache);
    }


    /**
     * 获取老的权益
     *
     * @param db           数据源
     * @param userId       用户ID
     * @param newRightInfo 新权益系统中的用户信息
     * @return 权益信息
     */
    private UserRightInfo getOldRights(String db, Integer userId, List<UserRightsRecord> rightsRecords, UserRightInfo newRightInfo) {
        //查询卖点
        List<SellPointInfo> pointInfos = dbCacheService.getAllSellPoint(db, new ArrayList<>());
        // 如果权益表中没有用户权益信息，则从原始表中获取
        RightsTemp rightsTemp;
        switch (db) {
            case Constant.AllDs.PPT -> {
                rightsTemp = rightsMapper.selectPptOldRights(userId);
                if (null != rightsTemp) {
                    return getUserRightInfoByPpt(rightsTemp, pointInfos);
                }
            }
            case Constant.AllDs.AITW -> {
                rightsTemp = rightsMapper.selectAiTwOldRights(userId);
                if (null != rightsTemp) {
                    return getUserRightInfoByTw(rightsTemp, pointInfos, rightsRecords, newRightInfo);
                }
            }
            case Constant.AllDs.MIX -> {
                // 写作查询老权益和推文一致
                rightsTemp = rightsMapper.selectAiTwOldRights(userId);
                if (null != rightsTemp) {
                    return getUserRightInfoByMix(rightsTemp, pointInfos, rightsRecords, newRightInfo);
                }
            }
            case Constant.AllDs.GOL -> {
                rightsTemp = rightsMapper.selectGolOldRights(userId);
                if (null != rightsTemp) {
                    return getUserRightInfoByGol(rightsTemp, pointInfos);
                }
            }
            case Constant.AllDs.IFLYTTS -> {
                rightsTemp = rightsMapper.selectIflyttsOldRights(userId);
                if (null != rightsTemp) {
                    return getUserRightInfoByIflytts(rightsTemp, pointInfos);
                }
            }
        }
        return null;
    }

    /**
     * PPT 权益初始化
     *
     * @param rightsTemp 查询到老权益
     * @param pointInfos 卖点信息
     * @return 用户信息
     */
    private UserRightInfo getUserRightInfoByPpt(RightsTemp rightsTemp, List<SellPointInfo> pointInfos) {
        UserRightInfo rightInfo = new UserRightInfo();
        rightInfo.setUserid(rightsTemp.getUserId());
        rightInfo.setVipendtime(LocalDateTimeUtil.format(rightsTemp.getEndtime(), "yyyy-MM-dd HH:mm:ss"));
        rightInfo.setSvipendtime(LocalDateTimeUtil.format(rightsTemp.getSviptime(), "yyyy-MM-dd HH:mm:ss"));
        rightInfo.setCoins(initUserRightsQuality(rightsTemp.getCoins(), 25, Constant.EditRightsFlag.OIL));
        rightInfo.setTimescharging(initTimes(rightInfo, pointInfos));
        rightInfo.setNumbercharging(initNum(rightInfo, pointInfos));
        return rightInfo;
    }

    /**
     * AITW(推文) 权益初始化
     *
     * @param rightsTemp 查询到老权益
     * @param pointInfos 卖点信息
     * @return 用户信息
     */
    private UserRightInfo getUserRightInfoByTw(RightsTemp rightsTemp, List<SellPointInfo> pointInfos, List<UserRightsRecord> rightsRecords, UserRightInfo newRightInfo) {
        //查询卖点
        UserRightInfo rightInfo = new UserRightInfo();
        rightInfo.setUserid(rightsTemp.getUserId());
        rightInfo.setVipendtime(LocalDateTimeUtil.format(rightsTemp.getEndtime(), "yyyy-MM-dd HH:mm:ss"));
        rightInfo.setSvipendtime(LocalDateTimeUtil.format(rightsTemp.getSviptime(), "yyyy-MM-dd HH:mm:ss"));
        //金币
        rightInfo.setCoins(initCoins(rightInfo, pointInfos));
        //次数
        rightInfo.setTimescharging(initTimes(rightInfo, pointInfos));
        //数量
        rightInfo.setNumbercharging(initNum(rightInfo, pointInfos));
        UserRightsRecord userRightsRecord = new UserRightsRecord();
        userRightsRecord.setUserid(rightInfo.getUserid());
        userRightsRecord.setDesc("会员权益，每月赠送1500积分");
        userRightsRecord.setRecordtype(Constant.AddRightsFlag.ADD_GIFT);
        userRightsRecord.setFunctionid(0);
        if (rightInfo.isVip()) {
            userRightsRecord.setQuantity(1500f);
        } else {
            userRightsRecord.setDesc("新用户免费获得5积分，可用于创作");
            userRightsRecord.setQuantity(5f);
        }
        userRightsRecord.setSellgroup(3);
        if (null == newRightInfo) {
            List<UserRightsRecord> records = dbCacheService.getUserRightsRecords(Constant.AllDs.AITW, userRightsRecord);
            if (CollectionUtil.isEmpty(records)) {
                rightsRecords.add(userRightsRecord);
            }
        }
        //原始是VIP(指的是userId 小于16307570)
        if (null == newRightInfo
                && rightInfo.isVip()
                && rightInfo.getUserid() < 16307570) {
            rightInfo.setNumbercharging(initNumOilByAiTw(rightInfo.getNumbercharging()));
            UserRightsRecord addRights = BeanUtils.toBean(userRightsRecord, UserRightsRecord.class);
            addRights.setDesc("权益转化，一次性赠送500积分");
            addRights.setFunctionid(0);
            addRights.setQuantity(500f);
            addRights.setSellgroup(3);
            List<UserRightsRecord> records = dbCacheService.getUserRightsRecords(Constant.AllDs.AITW, addRights);
            if (CollectionUtil.isEmpty(records)) {
                rightsRecords.add(addRights);
            }
        }
        return rightInfo;
    }

    /**
     * MIX(写作) 权益初始化
     *
     * @param rightsTemp 查询到老权益
     * @param pointInfos 卖点信息
     * @return 用户信息
     */
    private UserRightInfo getUserRightInfoByMix(RightsTemp rightsTemp, List<SellPointInfo> pointInfos, List<UserRightsRecord> rightsRecords, UserRightInfo newRightInfo) {
        //查询卖点
        UserRightInfo rightInfo = new UserRightInfo();
        rightInfo.setUserid(rightsTemp.getUserId());
        rightInfo.setVipendtime(LocalDateTimeUtil.format(rightsTemp.getEndtime(), "yyyy-MM-dd HH:mm:ss"));
        rightInfo.setSvipendtime(LocalDateTimeUtil.format(rightsTemp.getSviptime(), "yyyy-MM-dd HH:mm:ss"));
        //金币
        rightInfo.setCoins(initCoins(rightInfo, pointInfos));
        //次数
        rightInfo.setTimescharging(initTimes(rightInfo, pointInfos));
        //数量
        rightInfo.setNumbercharging(initNum(rightInfo, pointInfos));
        UserRightsRecord userRightsRecord = new UserRightsRecord();
        userRightsRecord.setUserid(rightInfo.getUserid());
        userRightsRecord.setDesc("会员权益，每月赠送1500积分");
        userRightsRecord.setRecordtype(Constant.AddRightsFlag.ADD_GIFT);
        userRightsRecord.setFunctionid(0);
        if (rightInfo.isVip()) {
            userRightsRecord.setQuantity(1500f);
        } else {
            userRightsRecord.setDesc("新用户免费获得5积分，可用于创作");
            userRightsRecord.setQuantity(5f);
        }
        userRightsRecord.setSellgroup(3);
        if (null == newRightInfo) {
            List<UserRightsRecord> records = dbCacheService.getUserRightsRecords(Constant.AllDs.MIX, userRightsRecord);
            if (CollectionUtil.isEmpty(records)) {
                rightsRecords.add(userRightsRecord);
            }
        }
        //原始是VIP(指的是userId 小于23843014)
        if (null == newRightInfo
                && rightInfo.isVip()
                && rightInfo.getUserid() < 23843014) {
            rightInfo.setNumbercharging(initNumOilByAiTw(rightInfo.getNumbercharging()));
            UserRightsRecord addRights = BeanUtils.toBean(userRightsRecord, UserRightsRecord.class);
            addRights.setDesc("权益转化，一次性赠送500积分");
            addRights.setFunctionid(0);
            addRights.setQuantity(500f);
            addRights.setSellgroup(3);
            List<UserRightsRecord> records = dbCacheService.getUserRightsRecords(Constant.AllDs.MIX, addRights);
            if (CollectionUtil.isEmpty(records)) {
                rightsRecords.add(addRights);
            }
        }
        return rightInfo;
    }

    /**
     * Gol(杭州) 权益初始化
     *
     * @param rightsTemp 查询到老权益
     * @param pointInfos 卖点信息
     * @return 用户信息
     */
    private UserRightInfo getUserRightInfoByGol(RightsTemp rightsTemp, List<SellPointInfo> pointInfos) {
        //查询卖点
        UserRightInfo rightInfo = new UserRightInfo();
        rightInfo.setUserid(rightsTemp.getUserId());
        rightInfo.setVipendtime(LocalDateTimeUtil.format(rightsTemp.getEndtime(), "yyyy-MM-dd HH:mm:ss"));
        rightInfo.setSvipendtime(LocalDateTimeUtil.format(rightsTemp.getSviptime(), "yyyy-MM-dd HH:mm:ss"));
        //金币
        rightInfo.setCoins(initCoins(rightInfo, pointInfos));
        //次数
        rightInfo.setTimescharging(initTimes(rightInfo, pointInfos));
        //数量
        rightInfo.setNumbercharging(initNum(rightInfo, pointInfos));
        return rightInfo;
    }

    /**
     * IFLYTTS(配音) 权益初始化
     *
     * @param rightsTemp 查询到老权益
     * @param pointInfos 卖点信息
     * @return 用户信息
     */
    private UserRightInfo getUserRightInfoByIflytts(RightsTemp rightsTemp, List<SellPointInfo> pointInfos) {
        //查询卖点
        UserRightInfo rightInfo = new UserRightInfo();
        rightInfo.setUserid(rightsTemp.getUserId());
        rightInfo.setVipendtime(LocalDateTimeUtil.format(rightsTemp.getEndtime(), "yyyy-MM-dd HH:mm:ss"));
        rightInfo.setSvipendtime(LocalDateTimeUtil.format(rightsTemp.getSviptime(), "yyyy-MM-dd HH:mm:ss"));
        //金币
        rightInfo.setCoins(initCoins(rightInfo, pointInfos));
        //次数
        rightInfo.setTimescharging(initTimes(rightInfo, pointInfos));
        //数量
        rightInfo.setNumbercharging(initNum(rightInfo, pointInfos));
        return rightInfo;
    }


    /**
     * 初始化加油包
     *
     * @param numbers 数量
     * @return 更新后的权益
     */
    private String initNumOilByAiTw(String numbers) {
        RightItems rightItems;
        if (!StringUtils.isEmpty(numbers)) {
            rightItems = new RightItems(numbers);
            rightItems.getRightItems().forEach(rt -> {
                if (rt.getI() == 3) {
                    rt.setO(500);
                }
            });
        } else {
            return numbers;
        }
        return rightItems.getJString();
    }


    /**
     * 初始化加油包
     *
     * @param quality   加油包量
     * @param sellGroup 卖点ID
     * @param isOil     是否加油包 1 是 2 否
     * @return 结果
     */
    private String initUserRightsQuality(Float quality, Integer sellGroup, Integer isOil) {
        if (null == quality || quality <= 0) {
            return null;
        }
        RightItems rightItems = new RightItems();
        RightItems.RightItem rightItem = new RightItems.RightItem();
        rightItem.setI(sellGroup);
        rightItem.setT("");
        rightItem.setR(-1);
        rightItem.setRq(0);
        if (isOil == 1) {
            rightItem.setO(quality);
        } else {
            rightItem.setQ(quality);
        }
        rightItem.setF(0);
        rightItem.setQ(0);
        rightItem.setRt(1);
        rightItems.setRightItems(List.of(rightItem));
        return rightItems.getJString();
    }


    /**
     * 初始化金币
     *
     * @param rightInfo
     * @return
     */
    private String initCoins(UserRightInfo rightInfo, List<SellPointInfo> pointInfos) {
        // 是金币且循环资源包
        pointInfos = pointInfos.stream().filter(
                x -> x.getSelltype() == Constant.SellType.COINS
                        && x.getResettype() == Constant.ResetType.VIPRESET).toList();
        if (CollectionUtil.isEmpty(pointInfos)) {
            return null;
        }
        return initRightsByPoint(rightInfo, pointInfos);
    }


    /**
     * 获取次数
     *
     * @param rightInfo
     * @return
     */
    private String initTimes(UserRightInfo rightInfo, List<SellPointInfo> pointInfos) {
        // 是次数且循环资源包
        pointInfos = pointInfos.stream().filter(
                x -> x.getSelltype() == Constant.SellType.TIMESCHARGING
                        && x.getResettype() == Constant.ResetType.VIPRESET).toList();
        if (CollectionUtil.isEmpty(pointInfos)) {
            return null;
        }
        return initRightsByPoint(rightInfo, pointInfos);
    }

    /**
     * 获取数量
     *
     * @param rightInfo
     * @return
     */
    private String initNum(UserRightInfo rightInfo, List<SellPointInfo> pointInfos) {
        //是数量且是循环资源包
        pointInfos = pointInfos.stream().filter(
                x -> x.getSelltype() == Constant.SellType.NUMBERCHARGING
                        && x.getResettype() == Constant.ResetType.VIPRESET).toList();
        if (CollectionUtil.isEmpty(pointInfos)) {
            return null;
        }
        return initRightsByPoint(rightInfo, pointInfos);
    }

    /**
     * 初始化权益信息
     *
     * @param rightInfo  用户信息
     * @param pointInfos 卖点信息
     * @return 权益字符串
     */
    private String initRightsByPoint(UserRightInfo rightInfo, List<SellPointInfo> pointInfos) {
        RightItems rightItems = new RightItems();
        //结果集
        List<RightItems.RightItem> rts = new ArrayList<>();
        Map<Integer, List<SellPointInfo>> sellGroupMap = pointInfos.stream().collect(Collectors.groupingBy(SellPointInfo::getSellgroup));
        for (Map.Entry<Integer, List<SellPointInfo>> entry : sellGroupMap.entrySet()) {
            if (CollectionUtil.isEmpty(entry.getValue())) {
                continue;
            }
            SellPointInfo pointInfo = entry.getValue().get(0);
            RightItems.RightItem rightItem = new RightItems.RightItem();
            rightItem.setI(pointInfo.getSellgroup());
            // VIP或者是SVIP则写入会员权益
            if (rightInfo.isVip() || rightInfo.isSvip()) {
                float quantity = rightInfo.isSvip() ? pointInfo.getSvipquantity() : pointInfo.getQuantity();
                rightItem.setT(DateTimeUtils.afterCurrentDateTimeZero(pointInfo.getResetunit()));
                rightItem.setR(pointInfo.getResetunit());
                rightItem.setRq(quantity);
                rightItem.setQ(quantity);
                rightItem.setRt(pointInfo.getResettype());
            } else {
                rightItem.setT("");
                rightItem.setO(0);
                rightItem.setF(pointInfo.getFree());
                rightItem.setRt(1);
                rightItem.setR(-1);
            }
            if (rightItem.getF() <= 0 && rightItem.getQ() <= 0) {
                continue;
            }
            rts.add(rightItem);
        }
        if (CollectionUtil.isEmpty(rts)) {
            return null;
        }
        rightItems.setRightItems(rts);
        return rightItems.getJString();
    }


    /**
     * 增加或减少用户权益
     *
     * @param cmd              命令请求对象
     * @param comRequest       通用请求对象
     * @param addRightsRequest 新增用户权益
     * @return 用户权益信息
     */
    @DS("#cmd.ds")
    @Transactional
    public CommonResult<UserRightResponse> addUserRights(CmdRequest cmd, ComRequest comRequest, AddUserRightsRequest addRightsRequest) {
        // 用户ID
        int userId = Integer.parseInt(comRequest.getBase().getUserid());
        // 记录权益消费
        List<UserRightsRecord> rightsRecords = new ArrayList<>();
        try {
            UserRightInfo userRightInfo = rightsMapper.getUserRightInfo(userId);
            if (userRightInfo == null) {
                // 查询老权益，不再做新增插入
                userRightInfo = getUserRightInfoWithValidation(cmd.getDs(), userId + "", rightsRecords);
                if (userRightInfo == null) {
                    return CommonResult.error(GlobalErrorCodeConstants.USER_NOT_FOUND);
                }
                //写入或更新用户权益
                rightsMapper.insertUserRightsInfo(userRightInfo);
                //更新缓存
                dbCacheService.saveCache(UserRightsCache.buildCacheKey(cmd.getDs(), userId),
                        BeanUtils.toBean(userRightInfo, UserRightsCache.class));
                // 记录用户权益初始化记录
                if (CollectionUtil.isNotEmpty(rightsRecords)) {
                    rightsMapper.batchInsertUserRightsRecord(rightsRecords);
                }
                return CommonResult.success(initRightResponse(cmd.getDs(),
                        dbCacheService.getExistingCache(UserRightsCache.buildCacheKey(cmd.getDs(), userId))));
            }
            // 是否需要更新
            boolean isUpdate = false;
            //编辑权益
            if (MapUtil.isNotEmpty(addRightsRequest.getUserrights())) {
                for (Map.Entry<Integer, AddUserRightsRequest.EditUserRights> entry : addRightsRequest.getUserrights().entrySet()) {
                    AddUserRightsRequest.EditUserRights userRights = entry.getValue();
                    float total = 0;
                    //编辑金币
                    if (null != userRights.getAddcoins() && userRights.getAddcoins() != 0) {
                        total = userRights.getAddcoins();
                        if (StringUtils.isEmpty(userRightInfo.getCoins())) {
                            userRightInfo.setCoins(initUserRightsQuality(userRights.getAddcoins(), entry.getKey(), entry.getValue().getIsoil()));
                        } else {
                            RightItems rightItems = new RightItems(userRightInfo.getCoins());
                            rightItems.getRightItems().forEach(rightItem -> {
                                if (rightItem.getI() == entry.getKey()) {
                                    if (Objects.equals(userRights.getIsoil(), Constant.EditRightsFlag.OIL)) {
                                        rightItem.setO(rightItem.getO() + userRights.getAddcoins());
                                        if (rightItem.getO() < 0) {
                                            rightItem.setO(0);
                                        }
                                    } else {
                                        rightItem.setQ(rightItem.getQ() + userRights.getAddcoins());
                                        if (rightItem.getQ() < 0) {
                                            rightItem.setQ(0);
                                        }
                                    }
                                }
                            });
                            userRightInfo.setCoins(rightItems.getJString());
                        }
                    }
                    //编辑数量
                    if (null != userRights.getAddnum() && userRights.getAddnum() != 0) {
                        total = userRights.getAddnum();
                        if (StringUtils.isEmpty(userRightInfo.getNumbercharging())) {
                            userRightInfo.setNumbercharging(initUserRightsQuality(userRights.getAddnum(), entry.getKey(), entry.getValue().getIsoil()));
                        } else {
                            RightItems rightItems = new RightItems(userRightInfo.getNumbercharging());
                            rightItems.getRightItems().forEach(rightItem -> {
                                if (rightItem.getI() == entry.getKey()) {
                                    if (Objects.equals(userRights.getIsoil(), Constant.EditRightsFlag.OIL)) {
                                        rightItem.setO(rightItem.getO() + userRights.getAddnum());
                                        if (rightItem.getO() < 0) {
                                            rightItem.setO(0);
                                        }
                                    } else {
                                        rightItem.setQ(rightItem.getQ() + userRights.getAddnum());
                                        if (rightItem.getQ() < 0) {
                                            rightItem.setQ(0);
                                        }
                                    }
                                }
                            });
                            userRightInfo.setNumbercharging(rightItems.getJString());
                        }
                    }
                    //编辑次数
                    if (null != userRights.getAddtimes() && userRights.getAddtimes() != 0) {
                        total = userRights.getAddtimes();
                        if (StringUtils.isEmpty(userRightInfo.getTimescharging())) {
                            userRightInfo.setTimescharging(initUserRightsQuality(userRights.getAddtimes(), entry.getKey(), entry.getValue().getIsoil()));
                        } else {
                            RightItems rightItems = new RightItems(userRightInfo.getTimescharging());
                            rightItems.getRightItems().forEach(rightItem -> {
                                if (rightItem.getI() == entry.getKey()) {
                                    if (Objects.equals(userRights.getIsoil(), Constant.EditRightsFlag.OIL)) {
                                        rightItem.setO(rightItem.getO() + userRights.getAddtimes());
                                        if (rightItem.getO() < 0) {
                                            rightItem.setO(0);
                                        }
                                    } else {
                                        rightItem.setQ(rightItem.getQ() + userRights.getAddtimes());
                                        if (rightItem.getQ() < 0) {
                                            rightItem.setQ(0);
                                        }
                                    }
                                }
                            });
                            userRightInfo.setTimescharging(rightItems.getJString());
                        }
                    }
                    if (total != 0) {
                        //写入调整记录
                        rightsRecords.add(initErpRightsRecord(userId, total, entry.getKey(), "客服权益调整"));
                        isUpdate = true;
                    }
                }
            }
            //编辑VIP时间（如果本来不是会员，则删除权益，重新同步；如果本来是会员则更新会员时间）
            if (null != addRightsRequest.getAddviptime() && addRightsRequest.getAddviptime() != 0) {
                if (userRightInfo.getVipType() == Constant.VIPType.NOTVIP) {
                    // 删除源用户权益
                    rightsMapper.deleteUserRights(userId);
                    // 删除源用户缓存
                    redisService.delete(UserRightsCache.buildCacheKey(cmd.getDs(), userId));
                } else {
                    if (StringUtils.isEmpty(userRightInfo.getVipendtime())) {
                        userRightInfo.setVipendtime(DateTimeUtils.currentDateTime());
                    }
                    userRightInfo.setVipendtime(DateTimeUtils.afterDateTime(userRightInfo.getVipendtime(), addRightsRequest.getAddviptime()));
                    isUpdate = true;
                }
                //写入调整记录
                rightsRecords.add(initErpRightsRecord(userId, addRightsRequest.getAddviptime(), 0, "客服调整VIP到期时间"));
            }
            //编辑SVIP时间（如果本来不是会员，则删除权益，重新同步；如果本来是会员则更新会员时间）
            if (null != addRightsRequest.getAddsviptime() && addRightsRequest.getAddsviptime() != 0) {
                if (userRightInfo.getVipType() == Constant.VIPType.NOTVIP) {
                    // 删除源用户权益
                    rightsMapper.deleteUserRights(userId);
                    // 删除源用户缓存
                    redisService.delete(UserRightsCache.buildCacheKey(cmd.getDs(), userId));
                } else {
                    if (StringUtils.isEmpty(userRightInfo.getSvipendtime())) {
                        userRightInfo.setSvipendtime(DateTimeUtils.currentDateTime());
                    }
                    userRightInfo.setSvipendtime(DateTimeUtils.afterDateTime(userRightInfo.getSvipendtime(), addRightsRequest.getAddsviptime()));
                    isUpdate = true;
                }
                //写入调整记录
                rightsRecords.add(initErpRightsRecord(userId, addRightsRequest.getAddsviptime(), 0, "客服调整SVIP到期时间"));
            }
            // 删除重置限制
            dbCacheService.unlockRested(cmd.getDs() + "_" + userId);
            // 更新数据库和缓存
            if (isUpdate) {
                log.info("增加或减少用户权益, 权益信息： {}", JSONUtil.toJsonStr(userRightInfo));
                // 写入到数据库
                rightsMapper.updateUserRightInfo(userRightInfo);
                // 保存到缓存
                dbCacheService.saveCache(UserRightsCache.buildCacheKey(cmd.getDs(), userId),
                        BeanUtils.toBean(userRightInfo, UserRightsCache.class));
                // 写入调整记录
                if (CollectionUtil.isNotEmpty(rightsRecords)) {
                    rightsMapper.batchInsertUserRightsRecord(rightsRecords);
                }
            }
            //返回参数
            return CommonResult.success(initRightResponse(cmd.getDs(),
                    dbCacheService.getExistingCache(UserRightsCache.buildCacheKey(cmd.getDs(), userId))));
        } catch (MyBaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error resetting rights for userId: {}", userId, e);
            throw e;
        }
    }

    /**
     * 客服调整权益
     *
     * @param userId    用户ID
     * @param total     消耗总量
     * @param sellGroup 价格配置
     * @param desc      描述
     * @return 记录
     */
    private UserRightsRecord initErpRightsRecord(Integer userId, float total, Integer sellGroup, String desc) {
        UserRightsRecord rightsRecord = new UserRightsRecord();
        rightsRecord.setUserid(userId);
        rightsRecord.setFunctionid(0);
        rightsRecord.setSellgroup(sellGroup);
        rightsRecord.setQuantity(total);
        rightsRecord.setRecordtype(Constant.AddRightsFlag.ADD_ERP);
        rightsRecord.setDesc(desc);
        return rightsRecord;
    }

    /**
     * 用户权益同步和转移
     *
     * @param cmd      命令请求对象
     * @param com      通用请求对象
     * @param syncUser 新增用户权益
     * @return 用户权益信息
     */
    @DS("#cmd.ds")
    @Transactional
    public CommonResult<?> syncUserRights(CmdRequest cmd, ComRequest com, SyncUserRightsRequest syncUser) {
        try {
            // 查询源用户权益
            List<UserRightsRecord> rightsRecords = new ArrayList<>();
            UserRightInfo userRightInfo = getUserRightInfoWithValidation(cmd.getDs(), syncUser.getOrguserid() + "", rightsRecords);
            if (userRightInfo == null) {
                return CommonResult.error(GlobalErrorCodeConstants.USER_NOT_FOUND);
            }
            // 如果目标用户已经存在
            UserRightInfo targetUserRight = rightsMapper.getUserRightInfo(syncUser.getTargetuserid());
            if (null != targetUserRight) {
                rightsMapper.deleteUserRights(targetUserRight.getUserid());
            }
            userRightInfo.setUserid(syncUser.getTargetuserid());
            rightsMapper.insertUserRightsInfo(userRightInfo);
            dbCacheService.saveCache(UserRightsCache.buildCacheKey(cmd.getDs(), syncUser.getTargetuserid()),
                    BeanUtils.toBean(userRightInfo, UserRightsCache.class));
            // 权益转移
            if (syncUser.getSyncflag().equals(Constant.SyncRightsFlag.TRANSFER)) {
                // 删除源用户权益
                rightsMapper.deleteUserRights(syncUser.getOrguserid());
                // 删除源用户缓存
                redisService.delete(UserRightsCache.buildCacheKey(cmd.getDs(), syncUser.getOrguserid()));
            }
            return CommonResult.success(null);
        } catch (MyBaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error sync rights for userId: {}", JSONUtil.toJsonStr(syncUser), e);
            throw e;
        }
    }

    /**
     * 删除用户权益
     *
     * @param cmd 命令请求对象
     * @param com 通用请求对象
     * @return 用户权益信息
     */
    @DS("#cmd.ds")
    public CommonResult<?> deleteUserAllRights(CmdRequest cmd, ComRequest com) {
        try {
            // 删除源用户权益
            rightsMapper.deleteUserRights(Integer.valueOf(com.getBase().getUserid()));
            // 删除源用户缓存
            redisService.delete(UserRightsCache.buildCacheKey(cmd.getDs(), Integer.parseInt(com.getBase().getUserid())));
            // 删除用户锁
            dbCacheService.unlockRested(cmd.getDs() + "_" + com.getBase().getUserid());
            return CommonResult.success(null);
        } catch (MyBaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error sync rights for userId: {}", com.getBase().getUserid(), e);
            return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_SERVER_ERROR);
        }
    }


    /**
     * 用户权益记录
     *
     * @param cmd           命令请求对象
     * @param com           通用请求对象
     * @param recordRequest 权益记录请求
     * @return 用户权益信息
     */
    @DS("#cmd.ds")
    public CommonResult<UserRightRecordResponse> userRightRecord(CmdRequest cmd, ComRequest com, UserRightsRecordRequest recordRequest) {
        UserRightRecordResponse response = new UserRightRecordResponse();
        if (List.of(Constant.AllDs.AITW, Constant.AllDs.MIX).contains(cmd.getDs())) {
            recordRequest.setFilterSellGroup(new ArrayList<>());
            if (!StringUtils.isEmpty(allowSellGroup)) {
                for (String ag : allowSellGroup.split(",")) {
                    recordRequest.getFilterSellGroup().add(Integer.parseInt(ag));
                }
            }
        }
        response.setPageNum(recordRequest.getPageNum());
        // 总记录数
        Integer total = rightsMapper.selectUserRightsRecordCount(recordRequest);
        response.setTotal(total);
        if (total <= 0) {
            response.setRecords(new ArrayList<>());
            return CommonResult.success(response);
        }
        recordRequest.setTotal(total);
        List<UserRightsRecord> rightsRecords = rightsMapper.selectUserRightsRecord(recordRequest);
        if (CollectionUtil.isEmpty(rightsRecords)) {
            response.setRecords(new ArrayList<>());
            return CommonResult.success(response);
        }
        List<UserRightRecordResponse.RightRecordDetail> recordDetails = BeanUtils.toBean(rightsRecords, UserRightRecordResponse.RightRecordDetail.class);
        if (CollectionUtil.isNotEmpty(recordDetails)) {
            //查询所有卖点信息
            for (UserRightRecordResponse.RightRecordDetail detail : recordDetails) {
                detail.setTimeStr(LocalDateTimeUtil.format(detail.getTime(), "yyyy-MM-dd HH:mm:ss"));
                detail.setTimeformat(LocalDateTimeUtil.format(detail.getTime(), "yyyy-MM-dd HH:mm:ss"));
                detail.setConsumeType(detail.getRecordtype());
                detail.setFromDesc("");
            }
        }
        response.setRecords(recordDetails);
        return CommonResult.success(response);
    }

}
