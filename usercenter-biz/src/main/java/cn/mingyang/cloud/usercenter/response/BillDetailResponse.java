package cn.mingyang.cloud.usercenter.response;


import lombok.Data;

/**
 * 订单详情
 */
@Data
public class BillDetailResponse {

    /**
     * 订单编号
     */
    private String outtradeno;

    /**
     * 订单描述
     */
    private String desc;

    /**
     * 支付费用
     */
    private Float fee;

    /**
     * 下单时间
     */
    private Long ordertime;

    /**
     * 发票状态，0 未申请 1 待审核 2 驳回 3 申请通过
     */
    private int invoicestatus;

    /**
     * 订单类型
     */
    private int billtype;

}
