package cn.mingyang.cloud.usercenter.dao.domain;

import lombok.Data;

/**
 * 通联支付响应参数
 */
@Data
public class UnionPayResponse {

    /**
     * 返回码 SUCCESS/FAIL
     */
    private String retcode;

    /**
     * 返回码说明
     */
    private String retmsg;

    /**
     * 商户号
     */
    private String cusid;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 交易单号
     */
    private String trxid;

    /**
     * 渠道平台交易单号
     */
    private String chnltrxid;

    /**
     * 商户交易单号
     */
    private String reqsn;

    /**
     * 随机字符串
     */
    private String randomstr;

    /**
     * 交易状态
     */
    private String trxstatus;

    /**
     * 交易完成时间
     */
    private String fintime;

    /**
     * 错误原因
     */
    private String errmsg;

    /**
     * 支付串
     */
    private String payinfo;

    /**
     * 交易类型
     */
    private String trxcode;

    /**
     * 签名
     */
    private String sign;

    /**
     * 判断交易是否成功
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(retcode);
    }
}
