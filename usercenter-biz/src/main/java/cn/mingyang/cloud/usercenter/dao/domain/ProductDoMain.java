package cn.mingyang.cloud.usercenter.dao.domain;


import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 产品信息
 */
@Data
public class ProductDoMain implements Serializable {

    @Serial
    private static final long serialVersionUID = 123456789L;

    /**
     * 商品id
     */
    private Integer productid;

    /**
     * 商品名称
     */
    private String productname;

    /**
     * 产品标题
     */
    private String title;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 商品时长，单位天
     */
    private Integer duration;

    /**
     * 原始价格
     */
    private Float orginalprice;

    /**
     * 促销价格
     */
    private Float realprice;

    /**
     * 首次价格
     */
    private Float firstpromoteprice;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 系统ID
     */
    private String osid;

    /**
     * 渠道
     */
    private String df;

    /**
     * 商品类型 101： vip 102：svip 103：加油包
     */
    private Integer producttype;

    /**
     * 别名
     */
    private String alias;

    /**
     * 是否是自动订阅： 0：不是 1： 是
     */
    private Integer issubscribe;

    /**
     * 客户端是否展示： 0：不展示 1：展示
     */
    private Integer isview;

    /**
     * 币种
     */
    private String currency;

    /**
     * 语种
     */
    private String lg;
}
