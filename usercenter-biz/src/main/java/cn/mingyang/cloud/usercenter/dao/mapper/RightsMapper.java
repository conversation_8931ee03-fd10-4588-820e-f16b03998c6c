package cn.mingyang.cloud.usercenter.dao.mapper;

import cn.mingyang.cloud.center.common.dao.entity.FunctionPriceItem;
import cn.mingyang.cloud.usercenter.dao.domain.RightsTemp;
import cn.mingyang.cloud.usercenter.dao.domain.UserRightsCache;
import cn.mingyang.cloud.usercenter.dao.entity.SellPointInfo;
import cn.mingyang.cloud.usercenter.dao.entity.UserRightInfo;
import cn.mingyang.cloud.usercenter.dao.entity.UserRightsRecord;
import cn.mingyang.cloud.usercenter.request.UserRightsRecordRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权益查询Mapper
 */
@Mapper
public interface RightsMapper {


    /**
     * 根据用户ID获取用户权益
     *
     * @param userid 用户ID
     * @return
     */
    UserRightInfo getUserRightInfo(@Param("userid") Integer userid);


    /**
     * 写入权益或更新权益
     *
     * @param param
     */
    void insertUserRightsInfo(@Param("param") UserRightInfo param);

    /**
     * 更新用户权益
     *
     * @param param
     */
    void updateUserRightInfo(@Param("param") UserRightInfo param);

    /**
     * 更加appId和osid获取功能
     *
     * @param appid
     * @param osid
     * @return
     */
    List<FunctionPriceItem> getFunctionPriceByAppid(@Param("appid") String appid, @Param("osid") String osid);

    /**
     * 根据功能ID获取功能价格
     *
     * @param functionId
     * @return
     */
    List<FunctionPriceItem> getFunctionPriceByFunctionId(@Param("functionid") int functionId);


    /**
     * 根据功能ID获取功能价格
     *
     * @param sellGroups 卖点组
     * @return 列表
     */
    List<SellPointInfo> getSellPointBySellGroup(@Param("sellGroups") List<Integer> sellGroups);


    /**
     * 权益缓存同步到数据库
     *
     * @param param
     */
    void syncDatabaseFromRedis(@Param("param") UserRightsCache param);

    /**
     * 查询待同步用户列表
     *
     * @param offset 偏移量
     * @param limit  每页大小
     * @return 用户权益列表
     */
    List<RightsTemp> selectNeedSyncUserList(@Param("offset") Integer offset, @Param("limit") Integer limit);

    /**
     * 查询待同步用户总数
     *
     * @return 总数
     */
    Integer selectNeedSyncUserCount();


    /**
     * 更新用户同步状态
     *
     * @param userIds 用户ID
     */
    void updateSyncUserRight(@Param("userIds") List<Integer> userIds);

    /**
     * 批量插入用户权益
     *
     * @param list 用户权益列表
     */
    void batchInsertUserRights(@Param("list") List<UserRightInfo> list);


    /**
     * PPT查询老权益
     *
     * @param userid 用户ID
     * @return 用户权益列表
     */
    RightsTemp selectPptOldRights(@Param("userid") Integer userid);

    /**
     * AITW查询老权益
     *
     * @param userid 用户ID
     * @return 用户权益列表
     */
    RightsTemp selectAiTwOldRights(@Param("userid") Integer userid);

    /**
     * 查询古戈尔老权益
     *
     * @param userid 用户ID
     * @return 用户权益列表
     */
    RightsTemp selectGolOldRights(@Param("userid") Integer userid);

    /**
     * 查询配音老权益
     *
     * @param userid 用户ID
     * @return 用户权益列表
     */
    RightsTemp selectIflyttsOldRights(@Param("userid") Integer userid);

    /**
     * 删除用户权益
     *
     * @param userid 用户ID
     */
    void deleteUserRights(@Param("userid") Integer userid);

    /**
     * 批量插入用户权益记录
     *
     * @param list 用户权益记录列表
     */
    void batchInsertUserRightsRecord(@Param("list") List<UserRightsRecord> list);

    /**
     * 查询用户权益记录
     *
     * @param recordRequest 记录请求
     * @return 用户权益记录列表
     */
    List<UserRightsRecord> selectUserRightsRecord(@Param("record") UserRightsRecordRequest recordRequest);


    /**
     * 查询用户权益记录总数
     *
     * @param recordRequest 记录请求
     * @return 用户权益记录列表
     */
    Integer selectUserRightsRecordCount(@Param("record") UserRightsRecordRequest recordRequest);


    /**
     * 查询用户权益记录
     *
     * @param recordRequest 记录请求
     * @return 用户权益记录列表
     */
    List<UserRightsRecord> selectRightRecord(@Param("record") UserRightsRecord recordRequest);
}
