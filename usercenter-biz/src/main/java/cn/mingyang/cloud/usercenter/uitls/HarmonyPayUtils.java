package cn.mingyang.cloud.usercenter.uitls;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.cert.*;
import java.security.interfaces.ECPrivateKey;
import java.security.interfaces.ECPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.time.Duration;
import java.util.*;

/**
 * 鸿蒙支付工具类
 */
public class HarmonyPayUtils {


    /**
     * JWS HEADER PARAM X5C: Indicates the X.509 certificate chain.
     * The sequence is leaf certificate, intermediate certificate, and root certificate.
     */
    private static final String HEADER_PARAM_X5C = "x5c";
    private static final int X5C_CHAIN_LENGTH = 3;
    /**
     * JWS HEADER PARAM ALG：Algorithm type. The value is always ES256.
     */
    private static final String HEADER_PARAM_ALG_ES256 = "ES256";
    private static final String LEAF_CERT_OID = "1.3.6.1.4.1.2011.2.415.1.1";
    private static final Boolean CRL_SOFT_FAIL_ENABLED = false;

    /**
     * 获取请求Token
     *
     * @param kid          密钥ID
     * @param iss          标识密钥颁发者ID
     * @param aid          appId
     * @param privateKey   秘钥
     * @param activeSecond 有效时间
     * @param bodyStr      消息体
     * @return 字符串
     */
    public static String genJwtToken(String kid, String iss, String aid, String privateKey, long activeSecond, String bodyStr) throws Exception {
        try {
            // Fetch the Private Key Content in PEM format.
            privateKey = privateKey.replace("-----BEGIN PRIVATE KEY-----", "")
                    .replaceAll("\\R+", "")
                    .replace("-----END PRIVATE KEY-----", "");
            KeyFactory keyFactory = KeyFactory.getInstance("EC");
            byte[] privateKeyBytes = Base64.getDecoder().decode(privateKey);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            ECPrivateKey ecPrivateKey = (ECPrivateKey) keyFactory.generatePrivate(keySpec);
            Map<String, Object> jwtPayload = new HashMap<>(HarmonyPayUtils.getJwtPayload(iss, aid));
            long signTime = System.currentTimeMillis() / Duration.ofSeconds(1).toMillis();
            String digest = DigestUtils.sha256Hex(bodyStr);
            jwtPayload.put("iat", signTime);
            jwtPayload.put("exp", signTime + activeSecond);
            jwtPayload.put("digest", digest);
            return JWT.create().withHeader(HarmonyPayUtils.getJwtHeader(kid)).withPayload(jwtPayload).sign(Algorithm.ECDSA256(ecPrivateKey));
        } catch (Exception e) {
            // TODO: Need to replace it with the actual business logic.
            throw new Exception(e);
        }
    }

    /**
     * 获取JWT 头信息
     *
     * @param kid 密钥ID
     * @return 头信息
     */
    public static Map<String, Object> getJwtHeader(String kid) {
        Map<String, Object> jwtHeader = new HashMap<>();
        // Algorithm type. The value is always ES256.
        jwtHeader.put("alg", "ES256");
        // Token type. The value is always JWT.
        jwtHeader.put("typ", "JWT");
        // Key ID.
        jwtHeader.put("kid", kid);
        return jwtHeader;
    }

    /**
     * 获取JWT 消息体
     *
     * @param iss 标识密钥颁发者ID
     * @param aid appId
     * @return 头信息
     */
    public static Map<String, Object> getJwtPayload(String iss, String aid) {
        Map<String, Object> jwtPayload = new HashMap<>();
        // Key issuer ID.
        jwtPayload.put("iss", iss);
        // Expected receiver of the JWT. The value is fixed at iap-v1.
        jwtPayload.put("aud", "iap-v1");
        // Time when the JWT is issued. The value is a UTC timestamp, in seconds.
        // Re-put the value in the genJwt method.
        jwtPayload.put("iat", 0);
        // Time when the JWT expires. The value is a UTC timestamp, in seconds. exp-iat indicates the validity period of the JWT, which cannot exceed one hour.
        // Re-put the value in the genJwt method.
        jwtPayload.put("exp", 0);
        // App ID.
        jwtPayload.put("aid", aid);
        // Hash value of the request body (JSON character string), which is used to verify the integrity of the body. The algorithm is SHA-256.
        jwtPayload.put("digest", "");
        return jwtPayload;
    }


    /**
     * Used to verify JWS and decode the payload.
     *
     * @param jwsStr JWS string
     * @return payload string
     * @throws Exception exception
     */
    public static String checkAndDecodeJWS(String path, String jwsStr) throws Exception {
        if (jwsStr == null || jwsStr.isEmpty()) {
            throw new Exception("jwsStr was null");
        }
        DecodedJWT decodedJWT = JWT.decode(jwsStr);
        if (!HEADER_PARAM_ALG_ES256.equals(decodedJWT.getAlgorithm())) {
            throw new Exception("alg must be ES256");
        }
        String[] x5cChain = decodedJWT.getHeaderClaim(HEADER_PARAM_X5C).asArray(String.class);
        if (x5cChain == null) {
            throw new Exception("x5c chain was null");
        }
        // Verify the x5c certificate chain and obtain the public key.
        PublicKey publicKey = verifyChainAndGetPubKey(path, x5cChain);
        // Use the public key to verify the signature of the jws.
        JWTVerifier jwtVerifier = JWT.require(Algorithm.ECDSA256((ECPublicKey) publicKey)).build();
        jwtVerifier.verify(decodedJWT);
        // Decode and return the payload.
        return new String(Base64.getUrlDecoder().decode(decodedJWT.getPayload()), StandardCharsets.UTF_8);
    }

    private static PublicKey verifyChainAndGetPubKey(String path, String[] certificates) throws Exception {
        CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
        List<Certificate> certificateList = new LinkedList<>();
        for (String certificate : certificates) {
            InputStream inputStream = new ByteArrayInputStream(Base64.getDecoder().decode(certificate));
            certificateList.add(certificateFactory.generateCertificate(inputStream));
        }
        if (certificateList.size() != X5C_CHAIN_LENGTH) {
            throw new Exception("invalid cert chain length");
        }
        PKIXCertPathValidatorResult certPathValidatorResult;
        try {
            PKIXParameters parameters = loadRootCAAndPKIX(path);
            CertPathValidator validator = CertPathValidator.getInstance("PKIX");
            parameters.setRevocationEnabled(true);
            PKIXRevocationChecker revocationChecker = (PKIXRevocationChecker) validator.getRevocationChecker();
            // TODO: Determine whether to set PKIXRevocationChecker.Option.SOFT_FAIL
            //  (ignore CRL certificate download exception) based on application Security Policy.
            if (CRL_SOFT_FAIL_ENABLED) {
                revocationChecker.setOptions(new HashSet<>(
                        Arrays.asList(PKIXRevocationChecker.Option.PREFER_CRLS, PKIXRevocationChecker.Option.NO_FALLBACK,
                                PKIXRevocationChecker.Option.SOFT_FAIL)));
            } else {
                revocationChecker.setOptions(new HashSet<>(
                        Arrays.asList(PKIXRevocationChecker.Option.PREFER_CRLS, PKIXRevocationChecker.Option.NO_FALLBACK)));
            }
            parameters.addCertPathChecker(revocationChecker);
            CertPath certPath = certificateFactory.generateCertPath(certificateList.subList(0, X5C_CHAIN_LENGTH - 1));
            certPathValidatorResult = (PKIXCertPathValidatorResult) validator.validate(certPath, parameters);
        } catch (Exception e) {
            throw new Exception(e);
        }
        Certificate iapCert = certificateList.get(0);
        if (!(iapCert instanceof X509Certificate x509Certificate)) {
            throw new Exception("leaf certificate must be X509 format");
        }
        if (x509Certificate.getNonCriticalExtensionOIDs() == null ||
                !x509Certificate.getNonCriticalExtensionOIDs().contains(LEAF_CERT_OID)) {
            throw new CertPathValidatorException("OID not found");
        }
        return certPathValidatorResult.getPublicKey();
    }

    private static PKIXParameters loadRootCAAndPKIX(String path) throws Exception {
        PKIXParameters parameters;
        try (InputStream fis = Files.newInputStream(Paths.get(path))) {
            CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
            Certificate trustCert = certificateFactory.generateCertificate(fis);
            if (!(trustCert instanceof X509Certificate)) {
                throw new RuntimeException("root certificate must be X509 format");
            }
            Set<TrustAnchor> trustAnchors = new HashSet<>();
            trustAnchors.add(new TrustAnchor((X509Certificate) trustCert, null));
            parameters = new PKIXParameters(trustAnchors);
        }
        return parameters;
    }
}
