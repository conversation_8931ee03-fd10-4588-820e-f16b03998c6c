package cn.mingyang.cloud.usercenter.dao.entity;

import cn.mingyang.cloud.center.common.util.DateTimeUtils;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import com.alibaba.druid.util.StringUtils;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户权益信息
 */
@Data
public class UserRightInfo {

    /**
     * ID
     */
    private int id;

    /**
     * 用户id
     */
    private int userid;

    /**
     * 金币
     */
    private String coins;


    /**
     * 按数计费单元（JSON格式：{sellgroup: quantity, resettime: 时间戳, resettime: 时间戳}）
     */
    private String numbercharging;


    /**
     * 次数计费单元（JSON格式：{sellgroup: quantity, resettime: 时间戳, resettime: 时间戳}）
     */
    private String timescharging;


    /**
     * vip到期时间
     */
    private String vipendtime;

    /**
     * svip到期时间
     */
    private String svipendtime;

    /**
     * 创建时间
     */
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    private LocalDateTime updatetime;


    public boolean isSvip() {
        if (StringUtils.isEmpty(svipendtime)) {
            return false;
        }
        return LocalDateTime.now().isBefore(DateTimeUtils.parseFromString(svipendtime));
    }

    public boolean isVip() {
        if (StringUtils.isEmpty(vipendtime)) {
            return false;
        }
        return LocalDateTime.now().isBefore(DateTimeUtils.parseFromString(vipendtime));
    }

    public int getVipType() {
        if (isSvip()) {
            return Constant.VIPType.SVIP;
        } else if (isVip()) {
            return Constant.VIPType.VIP;
        } else {
            return Constant.VIPType.NOTVIP;
        }
    }


}
