package cn.mingyang.cloud.usercenter.service;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.dao.entity.FunctionForward;
import cn.mingyang.cloud.center.common.request.BaseRequest;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.center.common.util.ValidateUtils;
import cn.mingyang.cloud.framework.common.exception.ErrorCode;
import cn.mingyang.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.center.common.comEnum.BaseErrorCodeEnum;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import cn.mingyang.cloud.usercenter.comEnum.FunctionEnum;
import cn.mingyang.cloud.usercenter.dao.mapper.FunctionForwardMapper;
import cn.mingyang.cloud.usercenter.request.*;
import com.alibaba.nacos.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 请求转发Service
 */
@Slf4j
@Service
public class BaseService {


    @Value("${forward.server.gol.url}")
    public String forwardUrl;


    public final FunctionForwardMapper forwardMapper;
    private final ValidateUtils validateUtils;
    private final CloseableHttpClient httpClient;

    public BaseService(FunctionForwardMapper forwardMapper,
                       ValidateUtils validateUtils, CloseableHttpClient httpClient) {
        this.forwardMapper = forwardMapper;
        this.validateUtils = validateUtils;
        this.httpClient = httpClient;
    }

    /**
     * 发送短信验证码
     */
    public CommonResult<?> SendAuthCode(CmdRequest cmd, ComRequest comRequest) {
        //参数校验
        validateUtils.validateParam(comRequest.getParam(), SendAuthCodeRequest.class);
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * 统一注册 & 登录
     */
    public CommonResult<?> ThirdPartyLogin(CmdRequest cmd, ComRequest comRequest) {
        //参数校验
        validateUtils.validateParam(comRequest.getParam(), ThirdPartyLoginRequest.class);
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * 用户信息更新
     */
    public CommonResult<?> UserUpdate(CmdRequest cmd, ComRequest comRequest) {
        //参数校验
        validateUtils.validateParam(comRequest.getParam(), UserUpdateRequest.class);
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * 获取用户信息
     */
    public CommonResult<?> GetUserInfo(CmdRequest cmd, ComRequest comRequest) {
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * 用户注销
     */
    public CommonResult<?> UserUnregister(CmdRequest cmd, ComRequest comRequest) {
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * 用户权益信息
     */
    public CommonResult<?> GetUserRight(CmdRequest cmd, ComRequest comRequest) {
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * 手机号码一键登录
     */
    public CommonResult<?> AliGetPhoneNum(CmdRequest cmd, ComRequest comRequest) {
        //参数校验
        validateUtils.validateParam(comRequest.getParam(), AliGetPhoneNumRequest.class);
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * 获取产品信息
     */
    public CommonResult<?> getProductInfo(CmdRequest cmd, ComRequest comRequest) {
        //参数校验
        validateUtils.validateParam(comRequest.getParam(), GetProductInfoRequest.class);
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * 获取优惠券信息
     */
    public CommonResult<?> getCouponInfo(CmdRequest cmd, ComRequest comRequest) {
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * APP控制
     */
    public CommonResult<?> GrayControl(CmdRequest cmd, ComRequest comRequest) {
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }


    /**
     * 获取账单信息
     */
    public CommonResult<?> GetBills(CmdRequest cmd, ComRequest comRequest) {
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * 意见反馈
     */
    public CommonResult<?> SendAdvice(CmdRequest cmd, ComRequest comRequest) {
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * 企微活码 (客服)
     */
    public CommonResult<?> createQyQrCode(CmdRequest cmd, ComRequest comRequest) {
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * 老权益-用户权益
     *
     * @param cmd 路径参数
     * @param com 请求参数
     */
    public CommonResult<?> userPolicyInfo(CmdRequest cmd, ComRequest com) {
        if (cmd.getDs().equals(Constant.AllDs.AITW)) {
            cmd.setCno("1180");
        } else {
            cmd.setCno("1172");
        }
        TwComRequest twComRequest = new TwComRequest();
        twComRequest.setCmd("UserPolicyInfo");
        twComRequest.setBase(this.getTwObject(cmd, com));
        return forwardOldUserCenter(cmd, twComRequest);
    }

    /**
     * 老权益-权益消耗分页列表
     *
     * @param cmd 路径参数
     * @param com 请求参数
     */
    public CommonResult<?> userPolicyConsumeRecord(CmdRequest cmd, ComRequest com) {
        if (cmd.getDs().equals(Constant.AllDs.AITW)) {
            cmd.setCno("1182");
        } else {
            cmd.setCno("1174");
        }
        TwComRequest twComRequest = new TwComRequest();
        twComRequest.setCmd("UserPolicyConsumeRecord");
        twComRequest.setBase(this.getTwObject(cmd, com));
        twComRequest.setParam(com.getParam());
        return forwardOldUserCenter(cmd, twComRequest);
    }

    /**
     * 游客登录
     *
     * @param cmd 路径参数
     * @param com 请求参数
     */
    public CommonResult<?> visitorLogin(CmdRequest cmd, ComRequest com) {
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, com);
        return forwardRequest(forward, cmd, com);
    }

    /**
     * 获取推文Base
     *
     * @param cmd
     * @param com
     * @return
     */
    private JSONObject getTwObject(CmdRequest cmd, ComRequest com) {
        JSONObject base = new JSONObject();
        base.set("ap", "wifi");
        base.set("appid", cmd.getAppId());
        base.set("deviceid", com.getBase().getDeviceid());
        base.set("version", "19.0.1");
        base.set("sid", "b3bd52da41bb825f61e451595e7232c5");
        base.set("df", "default");
        base.set("lg", "zh");
        base.set("osid", "weixin");
        base.set("apiVersion", "4");
        base.set("ua", "Xiaomi|star|M2102K1C|14|1080*2297");
        base.set("userid", com.getBase().getUserid());
        base.set("sha1", "09ad6f7b007f4f2bab768ee2fba43cd30764ee50");
        base.set("sign", "26f7abe04753aa4f8e87be6c38717840");
        base.set("timestamp", System.currentTimeMillis() / 1000);
        base.set("requestId", System.currentTimeMillis() + "");
        return base;
    }

    /**
     * 权益消耗
     *
     * @param cmd 路径参数
     * @param com 请求参数
     */
    @Async
    public void userPolicyConsume(CmdRequest cmd, ComRequest com, Integer expendValue, Integer functionId) {
        if (cmd.getDs().equals(Constant.AllDs.AITW)) {
            cmd.setCno("1181");
        } else {
            cmd.setCno("1173");
        }
        TwComRequest twComRequest = new TwComRequest();
        twComRequest.setCmd("UserPolicyConsume");
        twComRequest.setBase(this.getTwObject(cmd, com));
        JSONObject param = new JSONObject();
        FunctionEnum functionEnum = FunctionEnum.getFunctionEnumByCmd(functionId);
        if (null == functionEnum || functionEnum.getExpendType() == 3) {
            log.warn("无效CMD或者扣除的是次数，不调用老系统， CMD: {}", functionId);
            return;
        }
        String expendValueStr;
        //扣除时长
        if (functionEnum.getExpendType() == 1) {
            expendValueStr = expendValue + "|" + 0;
        } else {
            //扣除绘画张数
            expendValueStr = 0 + "|" + expendValue;
        }
        param.set("consume_value", expendValueStr);
        param.set("consume_type", functionEnum.getType());
        twComRequest.setParam(param);
        //调用老权益进行消耗
        forwardOldUserCenter(cmd, twComRequest);
    }

    /**
     * 推送老权益
     *
     * @param outtradeno  订单编号
     * @param appId       应用ID
     * @param userId      用户ID
     * @param productName 商品名称
     * @param pid         业务ID
     */
    @Async
    public void pushOldRights(String outtradeno, String appId, String userId, String productName, String pid) {
        log.info("推送老权益, outtradeno: {}, appId: {}, userId: {}, productName: {}", outtradeno, appId, userId, productName);
        CmdRequest cmd = new CmdRequest("1232", "9.0", appId, System.currentTimeMillis() + "", pid);
        if (pid.equals(Constant.AllDs.MIX)) {
            cmd.setCno("1214");
        }
        TwComRequest twComRequest = new TwComRequest();
        twComRequest.setCmd("AddUserRights");
        ComRequest com = new ComRequest();
        BaseRequest base = new BaseRequest();
        base.setDeviceid("aitw_rights");
        base.setUserid(userId);
        com.setBase(base);
        twComRequest.setBase(this.getTwObject(cmd, com));
        JSONObject param = new JSONObject();
        param.set("outtradeno", outtradeno);
        param.set("productname", productName);
        twComRequest.setParam(param);
        //调用老权益进行消耗
        forwardOldUserCenter(cmd, twComRequest);
    }


    /**
     * 转发请求到目标URL
     */
    private CommonResult<?> forwardRequest(FunctionForward forward, CmdRequest cmd, ComRequest request) {
        try {
            // 1. 初始化转发URL
            //String forwardUrl = initForwardUrl(forward, cmd, request);
            String forwardUrl = forward.getForwardUrl();
            forwardUrl = forwardUrl + "?" + "c=" + cmd.getCno() + "&v=" + cmd.getVersion() + "&a=" + cmd.getAppId() + "&t=" + cmd.getTimestamp() + "&s=ser";
            log.info("[Forward] 转发请求到URL: {}", forwardUrl);

            // 2. 发送HTTP请求
            HttpPost httpPost = new HttpPost(forwardUrl);
            httpPost.setEntity(new StringEntity(JSONUtil.toJsonStr(request), "UTF-8"));
            httpPost.setHeader("Content-Type", "application/json");

            String response = httpClient.execute(httpPost, response1 -> EntityUtils.toString(response1.getEntity(), "UTF-8"));
            if (StringUtils.isEmpty(response)) {
                return CommonResult.error(BaseErrorCodeEnum.FORWARD_ERROR.getCode(), "转发请求失败,返回信息为空！");
            }
            //log.info("[Forward] 收到响应: {}", response);
            // 3. 解析响应
            return JSONUtil.toBean(response, CommonResult.class);
        } catch (Exception e) {
            log.error("[Forward] 转发请求异常", e);
            return CommonResult.error(BaseErrorCodeEnum.FORWARD_ERROR.getCode(), "转发请求失败：" + e.getMessage());
        }
    }


    /**
     * 获取转发配置
     */
    private FunctionForward getForwardConfig(CmdRequest cmdRequest, ComRequest comRequest) {
        FunctionForward forward = new FunctionForward();
        forward.setForwardUrl(this.forwardUrl);
        return forward;
    }


    /**
     * 转发请求到目标URL
     */
    private CommonResult<?> forwardOldUserCenter(CmdRequest cmd, TwComRequest request) {
        try {
            // 1. 初始化转发URL
            String forwardUrl = "http://bizt.peiyinapp.com/api/api.php";
            if (cmd.getDs().equals(Constant.AllDs.MIX)) {
                forwardUrl = "http://biz.aixiezuo888.com/chat/api.php";
            }
            forwardUrl = forwardUrl + "?" + "c=" + cmd.getCno() + "&v=1&a=" + cmd.getAppId() + "&t=" + cmd.getTimestamp() + "&s=ser";
            log.info("[Forward] 转发请求到URL: {} 请求参数: {}", forwardUrl, JSONUtil.toJsonStr(request));
            // 2. 发送HTTP请求
            HttpPost httpPost = new HttpPost(forwardUrl);
            httpPost.setEntity(new StringEntity(JSONUtil.toJsonStr(request), "UTF-8"));
            httpPost.setHeader("Content-Type", "application/json");
            String response = httpClient.execute(httpPost, response1 -> EntityUtils.toString(response1.getEntity(), "UTF-8"));
            if (StringUtils.isEmpty(response)) {
                return CommonResult.error(BaseErrorCodeEnum.FORWARD_ERROR.getCode(), "转发请求失败,返回信息为空！");
            }
            // 3. 解析响应
            CommonResult result = JSONUtil.toBean(response, CommonResult.class);
            result.setCode(JSONUtil.parseObj(response).getStr("status"));
            result.setMsg(JSONUtil.parseObj(response).getStr("descinfo"));
            return result;
        } catch (Exception e) {
            log.error("[Forward] 转发请求异常", e);
            return CommonResult.error(BaseErrorCodeEnum.FORWARD_ERROR.getCode(), "转发请求失败：" + e.getMessage());
        }
    }


    /**
     * 权益校验
     *
     * @return boolean
     */
    public CommonResult<?> checkRights(CmdRequest cmd, ComRequest com, Integer expendValue, Integer functionId) {
        //查询权益
        if (cmd.getDs().equals(Constant.AllDs.AITW)) {
            cmd.setCno("1180");
        } else {
            cmd.setCno("1172");
        }
        TwComRequest twComRequest = new TwComRequest();
        twComRequest.setCmd("UserPolicyInfo");
        twComRequest.setBase(this.getTwObject(cmd, com));
        twComRequest.setParam(com.getParam());
        CommonResult<?> result = forwardOldUserCenter(cmd, twComRequest);
        if (null != result.getData()) {
            log.info("老权益校验， functionId: {} expendValue: {} 返回： {}", functionId, expendValue, JSONUtil.toJsonStr(result));
            FunctionEnum functionEnum = FunctionEnum.getFunctionEnumByCmd(functionId);
            if (null == functionEnum || functionEnum.getExpendType() != 3) {
                JSONObject jsonObject = JSONUtil.parseObj(result.getData().toString());
                if (functionEnum != null && functionEnum.getExpendType() == 1) {
                    // 有效时长
                    Integer ableVideo = jsonObject.getInt("able_remain_video_duration");
                    if (cmd.getDs().equals(Constant.AllDs.MIX)) {
                        ableVideo = jsonObject.getInt("remain_today_video_duration");
                    }
                    if (ableVideo >= expendValue) {
                        return CommonResult.success(null);
                    }
                }
                if (functionEnum != null && functionEnum.getExpendType() == 2) {
                    // 有效绘画张数
                    Integer ableImg = jsonObject.getInt("able_remain_aiimg_num");
                    if (cmd.getDs().equals(Constant.AllDs.MIX)) {
                        ableImg = jsonObject.getInt("remain_today_aiimg_num");
                    }
                    if (ableImg >= expendValue) {
                        return CommonResult.success(null);
                    }
                }
            }
        }
        return CommonResult.error(ErrorCode.formCode(GlobalErrorCodeConstants.DAILY_TIMES_USE_UP.getCode()));
    }

    /**
     * 推文扣减老权益
     *
     * @param cmd         命令参数
     * @param com         基础参数
     * @param expendValue 消耗值
     * @param functionId  方法ID
     */
    public CommonResult<?> userPolicyOldConsume(CmdRequest cmd, ComRequest com, Integer expendValue, Integer functionId) {
        if (cmd.getDs().equals(Constant.AllDs.AITW)) {
            cmd.setCno("1181");
        } else {
            cmd.setCno("1173");
        }
        TwComRequest twComRequest = new TwComRequest();
        twComRequest.setCmd("UserPolicyConsume");
        twComRequest.setBase(this.getTwObject(cmd, com));
        JSONObject param = new JSONObject();
        FunctionEnum functionEnum = FunctionEnum.getFunctionEnumByCmd(functionId);
        String expendValueStr;
        //扣除时长
        if (null != functionEnum && functionEnum.getExpendType() == 1) {
            expendValueStr = expendValue + "|" + 0;
        } else {
            //扣除绘画张数
            expendValueStr = 0 + "|" + expendValue;
        }
        param.set("consume_value", expendValueStr);
        param.set("consume_type", functionEnum.getType());
        twComRequest.setParam(param);
        //调用老权益进行消耗
        return forwardOldUserCenter(cmd, twComRequest);
    }


}
