package cn.mingyang.cloud.usercenter.dao.mapper;

import cn.mingyang.cloud.usercenter.dao.entity.*;
import cn.mingyang.cloud.usercenter.response.PaySubscribeResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 支付Mapper
 */
@Mapper
public interface PayMapper {

    /**
     * 通过应用ID和支付渠道查询支付配置
     *
     * @param appId      应用ID
     * @param payChannel 支付渠道
     * @return 支付配置
     */
    PayConfig getPayChannelConfig(@Param("appId") String appId,
                                  @Param("payChannel") String payChannel);


    /**
     * 通过商家AppID获取支付配置
     *
     * @param payAppId   应用ID
     * @param payChannel 支付渠道
     * @return 支付配置
     */
    PayConfig getPayChannelConfigByPayChannel(@Param("payAppId") String payAppId,
                                              @Param("payChannel") String payChannel);


    /**
     * 根据订单编号查询订单信息
     *
     * @param outtradeno 订单编号
     * @return 用户订单
     */
    OrgUserOrder getUserOrder(@Param("outtradeno") String outtradeno);


    /**
     * 查询商品信息
     *
     * @param id 商品ID
     * @return 商品信息
     */
    OrgProduct getProductInfoById(@Param("id") Integer id);

    /**
     * 查询商品信息
     *
     * @param productName 商品名称
     * @return 商品信息
     */
    OrgProduct getProductInfoByName(@Param("productName") String productName);

    /**
     * 查询优惠卷信息
     *
     * @param id 优惠卷ID
     * @return 优惠卷信息
     */
    OrgCoupon getCouponById(@Param("id") Integer id);

    /**
     * 插入订单
     *
     * @param order 订单对象
     * @return 影响行数
     */
    int insertUserOrder(OrgUserOrder order);

    /**
     * 通过主键id查询订单
     *
     * @param id 订单主键
     * @return 订单对象
     */
    OrgUserOrder getUserOrderById(@Param("id") int id);

    /**
     * 根据订单号查询订单
     *
     * @param outTradeNo 订单号
     * @return 订单对象
     */
    OrgUserOrder getUserOrderByOutTradeNo(@Param("outTradeNo") String outTradeNo);

    /**
     * 插入单据流水
     */
    int insertOrgBills(OrgBills bills);

    /**
     * 插入订单订阅信息
     */
    int insertOrgSubscription(OrgSubscription subscription);

    /**
     * 根据订单号更新订单状态
     */
    int updateOrderStatusByOutTradeNo(@Param("outTradeNo") String outTradeNo, @Param("status") int status);

    /**
     * 根据订单号更新订单状态
     */
    int updateSubscribeByUserId(@Param("userid") int userid, @Param("valid") int valid);

    /**
     * 根据用户ID查询订阅表
     */
    OrgSubscription selectSubscribeByUserId(@Param("userid") int userid);

    /**
     * 插入支付订阅记录
     *
     * @param paySubscribe 支付订阅对象
     * @return 影响行数
     */
    int insertPaySubscribe(PaySubscribe paySubscribe);

    /**
     * 根据用户ID和商品ID查询支付订阅记录
     *
     * @param userid    用户ID
     * @param productid 商品ID
     * @return 支付订阅记录
     */
    PaySubscribe selectPaySubscribeByUserIdAndProductId(@Param("userid") Integer userid, @Param("productid") Integer productid);

    /**
     * 根据首次订单号查询支付订阅记录
     *
     * @param firstouttradeno 首次订单号
     * @return 支付订阅记录
     */
    PaySubscribe selectPaySubscribeByFirstOutTradeNo(@Param("firstouttradeno") String firstouttradeno);

    /**
     * 更新支付订阅状态
     *
     * @param id          订阅记录ID
     * @param status      状态
     * @param agreementno 签约协议号
     * @return 影响行数
     */
    int updatePaySubscribeStatus(@Param("id") Integer id,
                                 @Param("status") Integer status,
                                 @Param("agreementno") String agreementno);

    /**
     * 更新支付订阅下次扣款时间
     *
     * @param id             订阅记录ID
     * @param nextdeducttime 下次扣款时间
     * @return 影响行数
     */
    int updatePaySubscribeNextDeductTime(@Param("id") Integer id, @Param("nextdeducttime") String nextdeducttime);


    /**
     * 查询进入需要扣款的订阅记录
     *
     * @param deducttime 扣款时间
     * @return 支付订阅
     */
    List<PaySubscribe> selectNeedPaySubscribe(@Param("deducttime") String deducttime);


    /**
     * 查询是否有待支付的订阅订单
     *
     * @param mchid       商户号
     * @param userid      用户id
     * @param deducttime  扣款时间
     * @param productname 产品名称
     * @return 支付订单
     */
    List<OrgUserOrder> selectNoPaySubscribeOrder(@Param("mchid") String mchid,
                                                 @Param("userid") Integer userid,
                                                 @Param("deducttime") String deducttime,
                                                 @Param("productname") String productname);

    /**
     * 支付订阅列表
     *
     * @param userid       用户id
     * @param platformcode 平台编号
     * @return 订阅列表
     */
    List<PaySubscribeResponse> paymentSubscribeList(@Param("userid") Integer userid,
                                                    @Param("platformcode") String platformcode);

    /**
     * 根据订阅ID集合查询订阅记录
     */
    List<PaySubscribe> selectPaySubscribeByIds(@Param("ids") List<Integer> ids);


    /**
     * 更新支付订阅收据信息
     *
     * @param firstouttradeno 首次订单记录
     * @param configvalues    收据信息
     * @return 影响行数
     */
    int updatePaySubscribeMessage(@Param("firstouttradeno") String firstouttradeno,
                                  @Param("configvalues") String configvalues);
}
