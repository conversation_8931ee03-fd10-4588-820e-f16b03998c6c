package cn.mingyang.cloud.usercenter.comEnum;

import lombok.Getter;

/**
 * 重置单元
 */
@Getter
public enum ResetUnitType {
    DAY("day", "天"),
    WEEK("week", "周"),
    MONTH("month", "月"),
    YEAR("year", "年"),
    UNREST("unrest", "不重置");

    private final String code;

    private final String desc;

    ResetUnitType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ResetUnitType fromCode(String code) {
        for (ResetUnitType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

}
