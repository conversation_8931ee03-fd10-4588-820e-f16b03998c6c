package cn.mingyang.cloud.usercenter.request;


import lombok.Data;

import java.util.Map;

/**
 * 新增用户权益
 */
@Data
public class AddUserRightsRequest {

    /**
     * 编辑会员时间
     */
    private Integer addviptime;

    /**
     * 编辑SVIP时间
     */
    private Integer addsviptime;

    /**
     * 用户权益
     */
    private Map<Integer, EditUserRights> userrights;


    /**
     * 编辑用户权益
     */
    @Data
    public static class EditUserRights {

        /**
         * 0 否 1 是
         */
        private Integer isoil;

        /**
         * 编辑金币
         */
        private Float addcoins;

        /**
         * 编辑时长
         */
        private Float addnum;

        /**
         * 编辑次数
         */
        private Float addtimes;
    }

}
