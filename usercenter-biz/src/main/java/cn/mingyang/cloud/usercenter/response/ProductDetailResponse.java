package cn.mingyang.cloud.usercenter.response;


import lombok.Data;

/**
 * 商品详情
 */
@Data
public class ProductDetailResponse {

    /**
     * 商品 ID
     */
    private Long productid;

    /**
     * 商品类型，主要是前端不同页面过滤商品用的。详情见商品类型枚举
     */
    private Integer producttype;

    /**
     * 原价
     */
    private Float price;

    /**
     * 优惠价格（下单价格）
     */
    private Float promotionprice;

    /**
     * 标题
     */
    private String title;

    /**
     * 自动订阅，1 表示订阅，0 表示非订阅
     */
    private String autosubscribe;

    /**
     * 订阅价格
     */
    private Float subscribeprice;

    /**
     * 商品图片 (ios 卖笔需要用到)
     */
    private String productimg;

}
