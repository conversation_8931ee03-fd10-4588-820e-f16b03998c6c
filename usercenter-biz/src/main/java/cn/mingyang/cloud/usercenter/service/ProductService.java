package cn.mingyang.cloud.usercenter.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.comEnum.BaseErrorCodeEnum;
import cn.mingyang.cloud.center.common.exception.MyBaseException;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.center.common.util.DateTimeUtils;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.framework.common.util.object.BeanUtils;
import cn.mingyang.cloud.usercenter.api.response.ProductInfoResponse;
import cn.mingyang.cloud.usercenter.api.response.UserRightResponse;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import cn.mingyang.cloud.usercenter.dao.domain.ProductDoMain;
import cn.mingyang.cloud.usercenter.dao.domain.RightItems;
import cn.mingyang.cloud.usercenter.dao.domain.UserRightsCache;
import cn.mingyang.cloud.usercenter.dao.entity.ProductInfo;
import cn.mingyang.cloud.usercenter.dao.entity.SellPointInfo;
import cn.mingyang.cloud.usercenter.dao.entity.UserRightInfo;
import cn.mingyang.cloud.usercenter.dao.entity.UserRightsRecord;
import cn.mingyang.cloud.usercenter.dao.mapper.ProductMapper;
import cn.mingyang.cloud.usercenter.dao.mapper.RightsMapper;
import cn.mingyang.cloud.usercenter.request.BuyProductRequest;
import cn.mingyang.cloud.usercenter.request.GetProductInfoRequest;
import cn.mingyang.cloud.usercenter.response.GetUserRightResponse;
import com.alibaba.druid.util.StringUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * 商品服务类
 * 处理商品相关的业务逻辑，包括：
 * 1. 商品信息查询
 * 2. 商品购买
 * 3. 新用户权益赠送
 */
@Slf4j
@Service
public class ProductService {

    private final DbCacheService dbCacheService;
    private final RightsMapper rightsMapper;
    private final ProductMapper productMapper;

    public ProductService(DbCacheService dbCacheService, RightsMapper rightsMapper, ProductMapper productMapper) {
        this.dbCacheService = dbCacheService;
        this.rightsMapper = rightsMapper;
        this.productMapper = productMapper;
    }

    /**
     * 获取商品列表
     * 根据应用ID和操作系统ID获取商品信息，如果未找到则返回默认商品列表
     *
     * @param cmd 命令请求对象
     * @param com 通用请求对象
     * @return 商品列表
     */
    @DS("#cmd.ds")
    public CommonResult<List<ProductInfoResponse>> getProductInfo(CmdRequest cmd, GetProductInfoRequest productRequest, ComRequest com) {
        String appId = cmd.getAppId();
        String osId = com.getBase().getOsid();
        String lg = com.getBase().getLg();
        String df = com.getBase().getDf();
        log.info("Getting product info for appId: {}, osId: {}", appId, osId);
        List<ProductInfo> productInfos = dbCacheService.getProductInfoByAppid(cmd.getDs(), appId, osId, productRequest);
        log.debug("Found {} products for appId: {}", productInfos.size(), appId);
        if (productInfos.isEmpty()) {
            productInfos = dbCacheService.getProductInfoByAppid(cmd.getDs(), appId, Constant.DEFAULT, productRequest);
            if (productInfos.isEmpty()) {
                log.info("No products found for appId: {}, trying default products", appId);
                productInfos = dbCacheService.getProductInfoByAppid(cmd.getDs(), Constant.DEFAULT, Constant.DEFAULT, productRequest);
            }
        }
        // 判断语种
        if (CollectionUtil.isNotEmpty(productInfos)) {
            productInfos = productInfos.stream().filter(pr -> null == pr.getLg() || pr.getLg().equals(lg)).toList();
        }
        // 判断渠道
        if (CollectionUtil.isNotEmpty(productInfos)) {
            List<ProductInfo> beforeProductInfo = productInfos;
            productInfos = productInfos.stream().filter(pr -> null == pr.getDf() || pr.getDf().equals(df)).toList();
            if (CollectionUtil.isEmpty(productInfos)) {
                productInfos = beforeProductInfo.stream().filter(pr -> null == pr.getDf() || pr.getDf().equals(Constant.DEFAULT)).toList();
            }
        }
        log.info("Returning {} products for appId: {}", productInfos.size(), appId);
        if (CollectionUtil.isEmpty(productInfos)) {
            CommonResult.success(productInfos);
        }
        List<ProductInfoResponse> responses = BeanUtils.toBean(productInfos, ProductInfoResponse.class);
        List<SellPointInfo> pointInfos = dbCacheService.getAllSellPoint(cmd.getDs(), new ArrayList<>());
        if (CollectionUtil.isNotEmpty(responses)) {
            for (ProductInfoResponse pir : responses) {
                // 替换title
                List<ProductInfo> dbProds = productInfos.stream().filter(pi -> Objects.equals(pi.getProductid(), pir.getProductid())).toList();
                if (CollectionUtil.isNotEmpty(dbProds) && !StringUtils.isEmpty(dbProds.get(0).getClienttitle())) {
                    pir.setTitle(dbProds.get(0).getClienttitle());
                }
                // 添加卖点信息（备注、数量）
                if (CollectionUtil.isNotEmpty(pointInfos)) {
                    for (SellPointInfo sp : pointInfos) {
                        if (pir.getProductname().equals(sp.getSellname())) {
                            pir.setSellquantity(sp.getQuantity());
                            pir.setNote(sp.getNote());
                        }
                    }
                }
                //如果是IOS,则根据别名查询老的商品
                if (com.getBase().getOsid().equals("ios") && cmd.getDs().equals(Constant.AllDs.AITW)) {
                    ProductDoMain productDoMain = productMapper.queryOldProductByName(pir.getAlias());
                    if (null != productDoMain) {
                        pir.setOrgProductId(productDoMain.getProductid());
                        pir.setOldproductid(productDoMain.getProductid());
                    }
                }
            }
        }
        return CommonResult.success(responses.stream().sorted(Comparator.comparing(ProductInfoResponse::getSort)).toList());
    }

    /**
     * 购买商品
     * 处理商品购买流程，包括：
     * 1. 获取商品会员信息
     * 2. 更新用户权益信息
     * 3. 处理商品销售点信息
     *
     * @param buyProductRequest 购买商品请求
     * @param cmd               命令请求对象
     * @param comRequest        通用请求对象
     * @return 更新后的用户权益信息
     */
    @DS("#cmd.ds")
    @Transactional
    public CommonResult<UserRightResponse> buyProduct(BuyProductRequest buyProductRequest, CmdRequest cmd, ComRequest comRequest) {
        int productId = buyProductRequest.getProductid();
        String userId = comRequest.getBase().getUserid();
        String appId = cmd.getAppId();
        log.info("Processing product purchase - productId: {}, userId: {}", productId, userId);

        try {
            // 获取商品会员信息
            ProductDoMain productDoMain = dbCacheService.getProductType(cmd.getDs(), productId);
            if (productDoMain == null) {
                log.error("Product type not found for productId: {}", productId);
                return CommonResult.error(BaseErrorCodeEnum.NULL_PRODUCT_ERROR.getCode(), BaseErrorCodeEnum.NULL_PRODUCT_ERROR.getMessage());
            }
            log.debug("Retrieved VIP info for product: {}", productDoMain);

            // 用户权益缓存
            UserRightsCache addRightsCache = new UserRightsCache();
            addRightsCache.setUserid(Integer.parseInt(userId));

            // 获取用户权益信息
            UserRightInfo userRightInfo = getUserRightInfo(userId);
            boolean isNew = userRightInfo == null;
            if (isNew) {
                log.info("Creating new user right info for userId: {}", userId);
                userRightInfo = new UserRightInfo();
                userRightInfo.setUserid(Integer.parseInt(userId));
            } else {
                log.debug("Found existing user right info for userId: {}", userId);
            }

            // 更新会员信息
            updateVipInfo(userRightInfo, productDoMain);
            addRightsCache.setVipendtime(userRightInfo.getVipendtime());
            addRightsCache.setSvipendtime(userRightInfo.getSvipendtime());

            // 处理商品销售点信息
            List<SellPointInfo> pointInfos = processSellPoints(cmd.getDs(), appId, productId, userRightInfo);

            // 保存用户权益信息
            saveUserRightInfo(userRightInfo, isNew);

            // 增加购买记录
            rightsMapper.batchInsertUserRightsRecord(List.of(initProductRightsRecord(cmd.getDs(), userRightInfo, productDoMain, pointInfos)));

            // 更新用户权益缓存
            dbCacheService.saveCache(UserRightsCache.buildCacheKey(cmd.getDs(), Integer.parseInt(userId)),
                    BeanUtils.toBean(userRightInfo, UserRightsCache.class));

            log.info("Successfully processed product purchase for userId: {}, productId: {}", userId, productId);

            //返回参数
            return CommonResult.success(initRightResponse(cmd.getDs(),
                    dbCacheService.getExistingCache(UserRightsCache.buildCacheKey(cmd.getDs(), Integer.parseInt(userId)))));
        } catch (MyBaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error processing product purchase - productId: {}, userId: {}, error: {}",
                    productId, userId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 创建返回信息
     *
     * @param existCache 权益缓存
     * @return 返回信息
     */
    private UserRightResponse initRightResponse(String pid, UserRightsCache existCache) {
        if (null == existCache) {
            return null;
        }
        // 查询卖点信息
        List<SellPointInfo> sellPointInfos = dbCacheService.getAllSellPoint(pid, new ArrayList<>());
        GetUserRightResponse rightResponse = GetUserRightResponse.buildUserRightDetailResponseByCache(existCache, sellPointInfos);
        UserRightResponse response = BeanUtils.toBean(rightResponse, UserRightResponse.class);
        response.setUserid(existCache.getUserid() + "");
        if (CollectionUtil.isNotEmpty(rightResponse.getCoinsdetail())) {
            response.setCoinsdetail(JSONUtil.parseArray(rightResponse.getCoinsdetail()));
        }
        if (CollectionUtil.isNotEmpty(rightResponse.getNumberdetail())) {
            response.setNumberdetail(JSONUtil.parseArray(rightResponse.getNumberdetail()));
        }
        if (CollectionUtil.isNotEmpty(rightResponse.getTimesdetail())) {
            response.setTimesdetail(JSONUtil.parseArray(rightResponse.getTimesdetail()));
        }
        return response;
    }

    /**
     * 购买会员赠送的权益
     *
     * @param userRightInfo     用户信息
     * @param productInfoDoMain 会员信息信息
     * @return 权益信息记录
     */
    private UserRightsRecord initProductRightsRecord(String pid, UserRightInfo userRightInfo, ProductDoMain productInfoDoMain, List<SellPointInfo> pointInfos) {
        UserRightsRecord rightsRecord = new UserRightsRecord();
        rightsRecord.setUserid(userRightInfo.getUserid());
        rightsRecord.setFunctionid(0);
        rightsRecord.setQuantity(0f);
        rightsRecord.setSellgroup(0);
        float svipQuantity = 0;
        if (CollectionUtil.isNotEmpty(pointInfos)) {
            float quantity = BigDecimal.valueOf(pointInfos.stream().mapToDouble(SellPointInfo::getQuantity).sum()).
                    setScale(0, RoundingMode.UP).floatValue();
            svipQuantity = BigDecimal.valueOf(pointInfos.stream().mapToDouble(SellPointInfo::getSvipquantity).sum()).
                    setScale(0, RoundingMode.UP).floatValue();
            rightsRecord.setQuantity(quantity);
            rightsRecord.setSellgroup(pointInfos.get(0).getSellgroup());
        }
        switch (productInfoDoMain.getProducttype()) {
            case Constant.ProductType.OIL:
                rightsRecord.setDesc("购买加油包：" + productInfoDoMain.getTitle());
                rightsRecord.setRecordtype(Constant.AddRightsFlag.ADD_OIL);
                break;
            case Constant.ProductType.VIP:
                rightsRecord.setDesc("购买会员： " + productInfoDoMain.getTitle());
                rightsRecord.setRecordtype(Constant.AddRightsFlag.ADD_VIP);
                if (List.of(Constant.AllDs.AITW, Constant.AllDs.MIX).contains(pid)) {
                    rightsRecord.setDesc("开通会员后，每月赠送1500积分");
                    rightsRecord.setQuantity(1500f);
                    rightsRecord.setSellgroup(3);
                }
                break;
            case Constant.ProductType.SVIP:
                rightsRecord.setDesc("购买SVIP会员: " + productInfoDoMain.getTitle());
                rightsRecord.setRecordtype(Constant.AddRightsFlag.ADD_SVIP);
                rightsRecord.setQuantity(svipQuantity);
                break;
            default:
                log.warn("Unknown product type: {}", productInfoDoMain.getProducttype());
        }
        return rightsRecord;
    }


    /**
     * 获取用户权益信息
     *
     * @param userId 用户ID
     * @return 用户权益信息
     */
    private UserRightInfo getUserRightInfo(String userId) {
        log.debug("Getting user right info for userId: {}", userId);
        UserRightInfo userRightInfo = rightsMapper.getUserRightInfo(Integer.parseInt(userId));
        if (userRightInfo == null) {
            log.info("No user right info found for userId: {}", userId);
        } else {
            log.debug("Retrieved user right info for userId: {}", userId);
        }
        return userRightInfo;
    }

    /**
     * 更新会员信息
     * 根据商品类型更新用户的VIP或SVIP到期时间
     *
     * @param userRightInfo 用户权益信息
     * @param productDoMain 会员信息
     */
    private void updateVipInfo(UserRightInfo userRightInfo, ProductDoMain productDoMain) {
        log.debug("Updating VIP info for user: {}, product type: {}", userRightInfo.getUserid(), productDoMain.getProducttype());
        String endDay = null;
        switch (productDoMain.getProducttype()) {
            case Constant.ProductType.VIP:
                endDay = calculateEndTime(userRightInfo.getVipendtime(), productDoMain);
                userRightInfo.setVipendtime(endDay);
                break;
            case Constant.ProductType.SVIP:
                endDay = calculateEndTime(userRightInfo.getSvipendtime(), productDoMain);
                userRightInfo.setSvipendtime(endDay);
                break;
            default:
                log.warn("Unknown product type: {}", productDoMain.getProducttype());
        }
        if (endDay != null) {
            log.info("Updated VIP info for user: {}, new end time: {}", userRightInfo.getUserid(), endDay);
        }
    }

    /**
     * 计算会员到期时间
     *
     * @param currentEndTime 当前到期时间
     * @param productDoMain  会员信息
     * @return 新的到期时间
     */
    private String calculateEndTime(String currentEndTime, ProductDoMain productDoMain) {
        log.debug("Calculating end time - current: {}, duration: {}, quantity: {}",
                currentEndTime, productDoMain.getDuration(), productDoMain.getQuantity());

        String endDay;
        if (StringUtils.isEmpty(currentEndTime) || LocalDateTime.now().isAfter(DateTimeUtils.parseFromString(currentEndTime))) {
            endDay = DateTimeUtils.afterCurrentDateTime(productDoMain.getDuration());
        } else {
            endDay = DateTimeUtils.compareDate(currentEndTime,
                    DateTimeUtils.afterDateTime(currentEndTime, productDoMain.getDuration()));
        }
        log.debug("Calculated end time: {}", endDay);
        return endDay;
    }

    /**
     * 处理商品销售点信息
     * 根据销售点类型更新用户权益
     *
     * @param pid           数据源
     * @param appId         应用ID
     * @param productId     商品ID
     * @param userRightInfo 用户权益信息
     */
    private List<SellPointInfo> processSellPoints(String pid, String appId, int productId, UserRightInfo userRightInfo) {
        log.debug("Processing sell points for product: {}, user: {}", productId, userRightInfo.getUserid());

        List<SellPointInfo> sellPointInfos = dbCacheService.getSellPointByProductId(pid, productId);
        log.info("Found {} sell points for product: {}", sellPointInfos.size(), productId);

        if (CollectionUtil.isEmpty(sellPointInfos)) {
            throw new MyBaseException(BaseErrorCodeEnum.NULL_RIGHT_ERROR.getCode(), BaseErrorCodeEnum.NULL_RIGHT_ERROR.getMessage());
        }

        // 如果配置了固定应用的卖点，则只处理固定应用的卖点 todo 暂无需求，后面如果有需要可放开
//        if (!StringUtils.isEmpty(appId)) {
//            List<SellPointInfo> filterSellPointInfos = sellPointInfos.stream().filter(x -> x.getAppid().equals(appId)).toList();
//            if (CollectionUtil.isNotEmpty(filterSellPointInfos)) {
//                sellPointInfos = filterSellPointInfos;
//                log.info("Found {} sell points for product: {} and app: {}", sellPointInfos.size(), productId, appId);
//            }
//        }

        for (SellPointInfo sellPointInfo : sellPointInfos) {
            float quantity = calculateQuantity(sellPointInfo, userRightInfo);
            log.debug("Processing sell point: {}, calculated quantity: {}", sellPointInfo, quantity);
            updateUserRightBySellType(sellPointInfo, quantity, userRightInfo);
        }
        return sellPointInfos;
    }

    /**
     * 计算权益数量
     * 根据用户VIP状态计算实际权益数量
     *
     * @param sellPointInfo 销售点信息
     * @param userRightInfo 用户权益信息
     * @return 权益数量
     */
    private float calculateQuantity(SellPointInfo sellPointInfo, UserRightInfo userRightInfo) {
        float quantity = (sellPointInfo.getSvipquantity() > 0 && userRightInfo.isSvip())
                ? sellPointInfo.getSvipquantity()
                : sellPointInfo.getQuantity();
        log.debug("Calculated quantity: {} for sell point: {}, isSVIP: {}",
                quantity, sellPointInfo.getSellgroup(), userRightInfo.isSvip());
        return quantity;
    }

    /**
     * 根据销售类型更新用户权益
     *
     * @param sellPointInfo 销售点信息
     * @param quantity      权益数量
     * @param userRightInfo 用户权益信息
     */
    private void updateUserRightBySellType(SellPointInfo sellPointInfo,
                                           float quantity,
                                           UserRightInfo userRightInfo) {
        log.debug("Updating user right by sell type: {} for user: {}",
                sellPointInfo.getSelltype(), userRightInfo.getUserid());
        switch (sellPointInfo.getSelltype()) {
            case Constant.SellType.COINS:
                updateCoins(sellPointInfo, quantity, userRightInfo);
                break;
            case Constant.SellType.NUMBERCHARGING:
                updateNumberCharging(sellPointInfo, quantity, userRightInfo);
                break;
            case Constant.SellType.TIMESCHARGING:
                updateTimesCharging(sellPointInfo, quantity, userRightInfo);
                break;
            default:
                log.warn("Unknown sell type: {} for sell point: {}",
                        sellPointInfo.getSelltype(), sellPointInfo.getSellgroup());
        }

    }


    /**
     * 更新金币权益
     */
    private void updateCoins(SellPointInfo sellPointInfo, float quantity, UserRightInfo userRightInfo) {
        log.debug("Updating coins for user: {}, sell point: {}",
                userRightInfo.getUserid(), sellPointInfo.getSellgroup());

        String coinsString = StringUtils.isEmpty(userRightInfo.getCoins()) ? "" : userRightInfo.getCoins();
        RightItems rightItems = new RightItems(coinsString);
        RightItems.RightItem item = createRightItem(sellPointInfo, quantity);
        //是否是加油包
        boolean isOil = sellPointInfo.getResettype() == Constant.ResetType.OIL;
        userRightInfo.setCoins(rightItems.addRightItem(item, isOil));

        log.debug("Updated coins for user: {}, new value: {}",
                userRightInfo.getUserid(), userRightInfo.getCoins());
    }

    /**
     * 更新次数充值权益
     */
    private void updateNumberCharging(SellPointInfo sellPointInfo, float quantity, UserRightInfo userRightInfo) {
        log.debug("Updating number charging for user: {}, sell point: {}",
                userRightInfo.getUserid(), sellPointInfo.getSellgroup());

        String numberString = StringUtils.isEmpty(userRightInfo.getNumbercharging()) ? "" : userRightInfo.getNumbercharging();
        RightItems rightItems = new RightItems(numberString);
        RightItems.RightItem item = createRightItem(sellPointInfo, quantity);
        //是否是加油包
        boolean isOil = sellPointInfo.getResettype() == Constant.ResetType.OIL;
        userRightInfo.setNumbercharging(rightItems.addRightItem(item, isOil));

        log.debug("Updated number charging for user: {}, new value: {}",
                userRightInfo.getUserid(), userRightInfo.getNumbercharging());
    }

    /**
     * 更新时间充值权益
     */
    private void updateTimesCharging(SellPointInfo sellPointInfo, float quantity, UserRightInfo userRightInfo) {
        log.debug("Updating times charging for user: {}, sell point: {}",
                userRightInfo.getUserid(), sellPointInfo.getSellgroup());

        String timeString = StringUtils.isEmpty(userRightInfo.getTimescharging()) ? "" : userRightInfo.getTimescharging();
        RightItems rightItems = new RightItems(timeString);
        RightItems.RightItem item = createRightItem(sellPointInfo, quantity);
        //是否是加油包
        boolean isOil = sellPointInfo.getResettype() == Constant.ResetType.OIL;
        userRightInfo.setTimescharging(rightItems.addRightItem(item, isOil));

        log.debug("Updated times charging for user: {}, new value: {}",
                userRightInfo.getUserid(), userRightInfo.getTimescharging());
    }

    /**
     * 创建权益项
     */
    private RightItems.RightItem createRightItem(SellPointInfo sellPointInfo, float quantity) {
        log.debug("Creating right item for sell point: {}, quantity: {}",
                sellPointInfo.getSellgroup(), quantity);

        RightItems.RightItem item = new RightItems.RightItem();
        item.setI(sellPointInfo.getSellgroup());
        item.setT(DateTimeUtils.afterCurrentDateTimeZero(sellPointInfo.getResetunit()));
        item.setR(sellPointInfo.getResetunit());

        switch (sellPointInfo.getResettype()) {
            case Constant.ResetType.OIL:
                item.setO(quantity);
                break;
            case Constant.ResetType.VIPRESET:
            case Constant.ResetType.SVIPRESET:
                item.setQ(quantity);
                item.setF(0f);
                item.setRt(sellPointInfo.getResettype());
                break;
            default:
                log.warn("Unknown reset type: {}", sellPointInfo.getResettype());
        }

        if (sellPointInfo.getResetunit() > 0) {
            item.setRq(item.getQ());
            log.debug("Set reset quantity: {}", item.getQ());
        }

        return item;
    }

    /**
     * 保存用户权益信息
     */
    private void saveUserRightInfo(UserRightInfo userRightInfo, boolean isNew) {
        log.debug("Saving user right info for user: {}, isNew: {}",
                userRightInfo.getUserid(), isNew);

        try {
            if (isNew) {
                rightsMapper.insertUserRightsInfo(userRightInfo);
                log.info("Inserted new user right info for user: {}", userRightInfo.getUserid());
            } else {
                rightsMapper.updateUserRightInfo(userRightInfo);
                log.info("Updated existing user right info for user: {}", userRightInfo.getUserid());
            }
        } catch (Exception e) {
            log.error("Error saving user right info for user: {}, error: {}",
                    userRightInfo.getUserid(), e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 新会员赠送权益
     * 为新注册用户赠送初始权益
     *
     * @param cmd        命令请求对象
     * @param comRequest 通用请求对象
     * @return 用户权益信息
     */
    @DS("#cmd.ds")
    @Transactional
    public CommonResult<UserRightResponse> newUserGift(CmdRequest cmd, ComRequest comRequest) {
        String userId = comRequest.getBase().getUserid();
        String appId = cmd.getAppId();
        log.info("Processing new user gift for userId: {}, appId: {}", userId, appId);

        try {
            // 检查用户是否已存在
            UserRightInfo userRightInfo = getUserRightInfo(userId);
            if (userRightInfo != null) {
                log.warn("User already exists: {}", userId);
                return CommonResult.success(null);
            }

            // 用户权益缓存
            UserRightsCache addRightsCache = new UserRightsCache();
            addRightsCache.setUserid(Integer.parseInt(userId));

            // 创建新用户权益信息
            userRightInfo = new UserRightInfo();
            userRightInfo.setUserid(Integer.parseInt(userId));
            log.debug("Created new user right info for userId: {}", userId);

            // 获取并处理赠送商品信息
            List<SellPointInfo> sellPointInfos = processGiftProducts(cmd, comRequest, userRightInfo);

            // 保存用户权益信息
            UserRightInfo urInfo = rightsMapper.getUserRightInfo(userRightInfo.getUserid());
            if (null == urInfo) {
                rightsMapper.insertUserRightsInfo(userRightInfo);
                rightsMapper.batchInsertUserRightsRecord(List.of(initGiftRightsRecord(cmd.getDs(), userRightInfo, sellPointInfos)));
            }
            log.info("Successfully processed new user gift for userId: {}", userId);

            // 更新用户权益缓存
            dbCacheService.saveCache(UserRightsCache.buildCacheKey(cmd.getDs(), Integer.parseInt(userId)),
                    BeanUtils.toBean(userRightInfo, UserRightsCache.class));
            //返回参数
            return CommonResult.success(initRightResponse(cmd.getDs(),
                    dbCacheService.getExistingCache(UserRightsCache.buildCacheKey(cmd.getDs(), Integer.parseInt(userId)))));
        } catch (MyBaseException e) {
            throw e;
        } catch (Exception e) {
            log.error("Error processing new user gift for userId: {}, error: {}",
                    userId, e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 初始化新增用户权益信息
     *
     * @param userRightInfo 用户信息
     * @return 权益信息
     */
    private UserRightsRecord initGiftRightsRecord(String pid, UserRightInfo userRightInfo, List<SellPointInfo> sellPointInfos) {
        UserRightsRecord rightsRecord = new UserRightsRecord();
        rightsRecord.setUserid(userRightInfo.getUserid());
        rightsRecord.setFunctionid(0);
        rightsRecord.setQuantity(0f);
        rightsRecord.setSellgroup(0);
        rightsRecord.setDesc("新用户赠送");
        if (CollectionUtil.isNotEmpty(sellPointInfos)) {
            float free = BigDecimal.valueOf(sellPointInfos.stream().mapToDouble(SellPointInfo::getFree).sum())
                    .setScale(0, RoundingMode.UP).floatValue();
            rightsRecord.setQuantity(free);
            rightsRecord.setSellgroup(sellPointInfos.get(0).getSellgroup());
        }
        if (List.of(Constant.AllDs.AITW, Constant.AllDs.MIX).contains(pid)) {
            rightsRecord.setDesc("新用户免费获得5积分，可用于创作");
            rightsRecord.setSellgroup(3);
            rightsRecord.setQuantity(5f);
        }
        if (Objects.equals(Constant.AllDs.PPT, pid)) {
            rightsRecord.setSellgroup(25);
            rightsRecord.setQuantity(3f);
        }
        rightsRecord.setRecordtype(Constant.AddRightsFlag.ADD_GIFT);
        return rightsRecord;
    }

    /**
     * 处理赠送商品信息
     */
    private List<SellPointInfo> processGiftProducts(CmdRequest cmdRequest, ComRequest comRequest, UserRightInfo userRightInfo) {
        List<SellPointInfo> sellPointInfos = getGiftSellPoints(cmdRequest, comRequest);
        if (CollectionUtil.isEmpty(sellPointInfos)) {
            throw new MyBaseException(BaseErrorCodeEnum.NULL_RIGHT_ERROR.getCode(), BaseErrorCodeEnum.NULL_RIGHT_ERROR.getMessage());
        }
        log.info("Processing {} gift products for user: {}", sellPointInfos.size(), userRightInfo.getUserid());
        for (SellPointInfo sellPointInfo : sellPointInfos) {
            if (sellPointInfo.getFree() <= 0) {
                log.debug("Skipping gift product with no free quantity: {}", sellPointInfo.getSellgroup());
                continue;
            }
            processGiftSellPoint(sellPointInfo, userRightInfo);
        }
        return sellPointInfos;
    }


    /**
     * 获取赠送商品销售点信息
     */
    private List<SellPointInfo> getGiftSellPoints(CmdRequest cmdRequest, ComRequest comRequest) {
        String appId = cmdRequest.getAppId();
        String osId = comRequest.getBase().getOsid();
        log.debug("Getting gift sell points for appId: {}, osId: {}", appId, osId);
        List<SellPointInfo> sellPointInfos = dbCacheService.getGiftSellPoint(cmdRequest.getDs(), appId);
        log.debug("Found {} gift sell points", sellPointInfos.size());
        return sellPointInfos;
    }

    /**
     * 处理单个赠送商品销售点
     * 根据销售点类型处理不同类型的权益赠送
     *
     * @param sellPointInfo 销售点信息
     * @param userRightInfo 用户权益信息
     */
    private void processGiftSellPoint(SellPointInfo sellPointInfo, UserRightInfo userRightInfo) {
        float quantity = sellPointInfo.getFree();
        log.debug("Processing gift sell point: {} for user: {}, quantity: {}",
                sellPointInfo.getSellgroup(), userRightInfo.getUserid(), quantity);
        // 创建权益项
        RightItems.RightItem item = new RightItems.RightItem();
        item.setI(sellPointInfo.getSellgroup());
        item.setR(0);
        item.setRt(-1);
        item.setF(quantity);
        // 根据销售点类型处理权益
        switch (sellPointInfo.getSelltype()) {
            case Constant.SellType.COINS:
                processGiftRight(sellPointInfo, item, userRightInfo::getCoins, userRightInfo::setCoins, "coins");
                break;
            case Constant.SellType.NUMBERCHARGING:
                processGiftRight(sellPointInfo, item, userRightInfo::getNumbercharging, userRightInfo::setNumbercharging, "number charging");
                break;
            case Constant.SellType.TIMESCHARGING:
                processGiftRight(sellPointInfo, item, userRightInfo::getTimescharging, userRightInfo::setTimescharging, "times charging");
                break;
            default:
                log.warn("Unknown sell type: {} for gift sell point: {}",
                        sellPointInfo.getSelltype(), sellPointInfo.getSellgroup());
        }
    }

    /**
     * 处理赠送权益
     * 统一处理不同类型的权益赠送逻辑
     *
     * @param sellPointInfo 销售点信息
     * @param item          权益项
     * @param getter        获取权益字符串的方法
     * @param setter        设置权益字符串的方法
     * @param rightType     权益类型描述
     */
    private void processGiftRight(
            SellPointInfo sellPointInfo,
            RightItems.RightItem item,
            java.util.function.Supplier<String> getter,
            java.util.function.Consumer<String> setter,
            String rightType) {
        log.debug("Processing gift {} for sell point: {}", rightType, sellPointInfo.getSellgroup());
        try {
            // 获取现有权益字符串
            String rightString = getter.get();
            rightString = StringUtils.isEmpty(rightString) ? "" : rightString;
            // 创建权益项并更新
            RightItems rightItems = new RightItems(rightString);
            //是否是加油包
            boolean isOil = sellPointInfo.getResettype() == Constant.ResetType.OIL;
            String updatedRight = rightItems.addRightItem(item, isOil);
            // 设置更新后的权益
            setter.accept(updatedRight);
            log.debug("Updated gift {} for sell point: {}, new value: {}",
                    rightType, sellPointInfo.getSellgroup(), updatedRight);
        } catch (Exception e) {
            log.error("Error processing gift {} for sell point: {}, error: {}",
                    rightType, sellPointInfo.getSellgroup(), e.getMessage(), e);
            throw new RuntimeException("Failed to process gift " + rightType, e);
        }
    }
}
