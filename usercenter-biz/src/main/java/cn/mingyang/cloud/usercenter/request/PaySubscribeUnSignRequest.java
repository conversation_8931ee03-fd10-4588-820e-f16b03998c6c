package cn.mingyang.cloud.usercenter.request;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 取消支付订阅请求
 */
@Data
public class PaySubscribeUnSignRequest {

    /**
     * 支付平台, 微信：WX、阿里支付：ALI、苹果支付：APPLE、通联支付：TL、小米支付：MI
     */
    @NotBlank(message = "支付平台：platformcode不能为空，微信：WX、阿里支付：ALI、苹果支付：APPLE、通联支付：TL、小米支付：MI")
    private String platformcode;

    /**
     * 订阅列表ID
     */
    @NotEmpty(message = "订阅列表ID: ids不能为空")
    private List<Integer> ids;


}
