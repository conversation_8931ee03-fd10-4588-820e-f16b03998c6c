package cn.mingyang.cloud.usercenter.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 苹果支付验签请求
 */
@Data
public class ApplePayVerifyRequest {

    /**
     * 是否沙箱环境
     */
    @NotNull(message = "是否沙箱环境：issandbox不能为空")
    private boolean issandbox;

    /**
     * 收据
     */
    @NotBlank(message = "收据：applereceipt不能为空")
    private String applereceipt;

    /**
     * 订阅产品名称
     */
    @NotBlank(message = "订阅产品名称：alias不能为空")
    private String alias;

}
