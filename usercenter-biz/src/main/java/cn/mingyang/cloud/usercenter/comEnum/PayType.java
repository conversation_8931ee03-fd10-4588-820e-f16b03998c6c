package cn.mingyang.cloud.usercenter.comEnum;

import lombok.Getter;

/**
 * 支付类型枚举
 */
@Getter
public enum PayType {
    //微信支付
    WX("WX", 1, "微信支付"),
    //阿里支付
    ALI("ALI", 2, "阿里支付"),
    //苹果支付
    APPLE("APPLE", 3, "苹果支付"),
    //通联支付
    TL("TL", 4, "通联支付"),
    //小米支付
    MI("MI", 5, "小米支付"),
    //鸿蒙支付
    HARMONY("HAR", 6, "鸿蒙支付");


    private final String code;

    private final Integer index;

    private final String desc;


    PayType(String code, Integer index, String desc) {
        this.code = code;
        this.index = index;
        this.desc = desc;
    }

    public static PayType fromCode(String code) {
        for (PayType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

}
