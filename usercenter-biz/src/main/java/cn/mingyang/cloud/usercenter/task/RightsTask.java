package cn.mingyang.cloud.usercenter.task;


import cn.hutool.core.date.DateUtil;
import cn.mingyang.cloud.center.common.service.RedisService;
import cn.mingyang.cloud.usercenter.dao.domain.UserRightsCache;
import cn.mingyang.cloud.usercenter.service.RightsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Set;


/**
 * 权益定时任务
 */
@Component
@Slf4j
public class RightsTask {


    private final RedisService redisService;

    private final RightsService rightsService;

    public RightsTask(RedisService redisService, RightsService rightsService) {
        this.redisService = redisService;
        this.rightsService = rightsService;
    }

    /**
     * 权益信息同步至数据库（每日凌晨 1 点执行）
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void rightsSyncTask() {
        //查询需要同步的权益
        String redisKey = UserRightsCache.userRightCacheSync + "_" + DateUtil.yesterday().toDateStr();
        log.info("Sync rights task, redisKey: {}", redisKey);
        Set<Object> userRightsCache = redisService.sMembersByString(redisKey);
        userRightsCache.forEach(user -> {
            try {
                String[] rs = user.toString().split("_");
                rightsService.userRightsSync(rs[0], rs[1], rs[2]);
            } catch (Exception exception) {
                log.error("Sync rights task error, user: {}", user, exception);
            }
        });
    }
}
