package cn.mingyang.cloud.usercenter.response;


import lombok.Data;

import java.util.List;

/**
 * 功能价格
 */
@Data
public class AllFunctionPriceResponse {

    /**
     * 主键
     */
    private int id;

    /**
     * 功能id
     */
    private int functionid;

    /**
     * 卖点组
     */
    private int sellgroup;

    /**
     * 消费单元
     */
    private float expend;

    /**
     * 消费单元 1：金币 2：数量 3：次数
     */
    private int expendtype;

    /**
     * 消费提醒
     */
    private String expendhint;

    /**
     * appid
     */
    private String appid;

    /**
     * osid
     */
    private String osid;

    /**
     * 权限类型：
     * 0：非会员可用
     * 1：普通VIP可用
     * 2：仅SVIP可用
     * 3：仅加油包可用
     * 4：VIP和加油包可用
     * 5：SVIP和加油包可用
     */
    private int righttype;


    /**
     * 卖点明细
     */
    private List<SellPointDetail> pointDetaill;


    /**
     * 卖点明细
     */
    @Data
    public static class SellPointDetail {
        /**
         * 卖点组id（主键自增）
         */
        private int sellgroupid;

        /**
         * 卖点组（不唯一，同一个卖点可根据appid有不同配置）
         */
        private Integer sellgroup;

        /**
         * 卖点名称
         */
        private String sellname;
    }

}
