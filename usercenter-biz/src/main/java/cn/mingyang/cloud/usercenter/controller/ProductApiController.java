package cn.mingyang.cloud.usercenter.controller;

import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.center.common.util.ValidateUtils;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.api.response.ProductInfoResponse;
import cn.mingyang.cloud.usercenter.api.response.UserRightResponse;
import cn.mingyang.cloud.usercenter.request.BuyProductRequest;
import cn.mingyang.cloud.usercenter.request.GetProductInfoRequest;
import cn.mingyang.cloud.usercenter.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 商品接口API控制层
 */
@Slf4j
@RestController
@RequestMapping("/product")
public class ProductApiController {

    private final ProductService productService;

    private final ValidateUtils validateUtils;


    public ProductApiController(ProductService productService, ValidateUtils validateUtils) {
        this.productService = productService;
        this.validateUtils = validateUtils;
    }


    /**
     * 获取商品列表
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=10201&*")
    public CommonResult<List<ProductInfoResponse>> getProductInfo(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("获取商品列表, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        //参数校验
        GetProductInfoRequest productRequest = validateUtils.validateParam(comRequest.getParam(), GetProductInfoRequest.class);
        return productService.getProductInfo(cmdRequest, productRequest, comRequest);
    }

    /**
     * 购买商品
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=10203&*")
    public CommonResult<UserRightResponse> buyProduct(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("购买商品, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        //参数校验
        BuyProductRequest buyProductRequest = validateUtils.validateParam(comRequest.getParam(), BuyProductRequest.class);
        return productService.buyProduct(buyProductRequest, cmdRequest, comRequest);
    }

    /**
     * 新注册用户赠送权限
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=10204&*")
    public CommonResult<UserRightResponse> newUserGift(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("新注册用户赠送权限, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return productService.newUserGift(cmdRequest, comRequest);
    }
}