package cn.mingyang.cloud.usercenter.dao.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户订单
 */
@Data
public class OrgUserOrder {

    /**
     * 主键，非空
     */
    private int id;

    /**
     * 外部交易号，非空，长度30
     */
    private String outTradeNo;

    /**
     * 用户id，可为空
     */
    private Integer userid;

    /**
     * 订阅价格，非空
     */
    private Float fee;

    /**
     * 支付类型，1:微信 2:支付宝，非空
     */
    private Integer type;

    /**
     * 时间，非空
     */
    private LocalDateTime time;

    /**
     * 订单状态，1:待付款 2:已付款，非空
     */
    private Integer status;

    /**
     * 设备id，长度50，可为空
     */
    private String deviceid;

    /**
     * 是否需要通知添加，可为空（0或1）
     */
    private Integer neednotifyadd;

    /**
     * appid，长度20，可为空
     */
    private String appid;

    /**
     * 操作系统id，长度10，可为空
     */
    private String osid;


    /**
     * 渠道，长度15，可为空
     */
    private String df;

    /**
     * 原始价格，可为空
     */
    private Float originPrice;

    /**
     * 商品名称，长度255，可为空
     */
    private String productName;

    /**
     * 数量，长度255，可为空（字符串形式）
     */
    private Integer quantity;

    /**
     * 地址信息，长文本，可为空
     */
    private String addressInfo;

    /**
     * openid，长度255，可为空
     */
    private String openid;

    /**
     * 同步状态，长度255，可为空
     */
    private String syncstatus;

    /**
     * 商户id，长度100，可为空（微信、支付宝、抖音商户id）
     */
    private String mchId;

    /**
     * 老df
     */
    private String oldDf;

    /**
     * 广告状态
     */
    private Integer adState;


    /**
     * 订阅价格，可为空（与fee可能重复，按表结构保留）
     */
    private Float subscribePrice;

    /**
     * 标题
     */
    private String title;


    /**
     * 是否订阅订单 0 否 1是
     */
    private Integer isSubscribe;
}
