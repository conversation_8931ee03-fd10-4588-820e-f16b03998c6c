package cn.mingyang.cloud.usercenter.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * 统一注册 & 登录 返回
 * 用户信息返回
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ThirdPartyLoginResponse {

    /**
     * 用户 userid
     */
    private String userid;

    /**
     * 昵称
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * qq
     */
    private String qq;

    /**
     * 手机号码
     */
    private String telephone;

    /**
     * 微信
     */
    private String wexin;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 图像
     */
    private String photo;

    /**
     * 是否是首次登录，true 代表首次登录
     */
    private Boolean isfirstlogin;

    /**
     * 0 正常 1 封禁 2 注销
     */
    private String status;

    /**
     * 授权类型
     */
    private String authtype;

    /**
     * 渠道
     */
    private String df;

    /**
     * 注册时 appid
     */
    private String appid;
}
