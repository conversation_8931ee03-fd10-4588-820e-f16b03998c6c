package cn.mingyang.cloud.usercenter.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.usercenter.comEnum.AppleAutoPayStatusEnum;
import cn.mingyang.cloud.usercenter.dao.domain.AppleReceiptResult;
import cn.mingyang.cloud.usercenter.dao.entity.OrgUserOrder;
import cn.mingyang.cloud.usercenter.request.ApplePayVerifyRequest;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.SignatureVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.PublicKey;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.interfaces.ECPublicKey;
import java.util.Base64;

/**
 * 苹果支付帮助类
 */
@Service
@Slf4j
public class AppleUtilService {

    // 苹果验证URL
    public static final String URL_SANDBOX = "https://sandbox.itunes.apple.com/verifyReceipt";
    public static final String URL_PRODUCTION = "https://buy.itunes.apple.com/verifyReceipt";

    // 配置常量
    private static final int CONNECT_TIMEOUT_MS = 10_000;
    private static final int READ_TIMEOUT_MS = 30_000;
    private static final String CONTENT_TYPE_JSON = "application/json";
    private static final String USER_AGENT = "Mozilla/5.0 (compatible; ApplePay/1.0)";


    // 验证苹果收据（详细结果）
    public AppleReceiptResult verifyAppleReceiptWithPlatformDetail(ApplePayVerifyRequest verifyRequest, String password) {
        AppleReceiptResult result = new AppleReceiptResult();
        try {
            // 构建请求参数
            JSONObject requestBody = buildRequestBody(verifyRequest.getApplereceipt(), password);
            String verifyUrl = verifyRequest.isIssandbox() ? URL_SANDBOX : URL_PRODUCTION;

            // 发送请求并验证响应
            String response = sendHttpRequestToApple(verifyUrl, requestBody.toString());
            log.info("验证苹果收据，  url： {} ", verifyUrl);
            log.info("验证苹果收据，  requestBody: {} ", requestBody);
            log.info("验证苹果收据，  response: {}", response);
            if (!StringUtils.hasLength(response)) {
                return setFailureResult(result, verifyRequest.isIssandbox());
            }

            // 处理响应和状态码
            return processAppleResponse(verifyRequest.getAlias(), response, requestBody.toString(), result, verifyRequest.isIssandbox());

        } catch (Exception e) {
            log.error("[苹果支付验签] 验证异常", e);
            return setFailureResult(result, verifyRequest.isIssandbox());
        }
    }

    // 构建请求体
    private JSONObject buildRequestBody(String receipt, String password) {
        JSONObject requestBody = new JSONObject();
        requestBody.set("receipt-data", receipt);
        if (StringUtils.hasLength(password)) {
            requestBody.set("password", password);
        }
        requestBody.set("exclude-old-transactions", true);
        return requestBody;
    }

    // 设置失败结果
    private AppleReceiptResult setFailureResult(AppleReceiptResult result, boolean isSandbox) {
        result.setSuccess(false);
        result.setSandbox(isSandbox);
        return result;
    }

    // 处理苹果响应
    private AppleReceiptResult processAppleResponse(String alias, String response, String requestBody, AppleReceiptResult result, boolean isSandbox) {
        JSONObject appleResponse = JSONUtil.parseObj(response);
        populateResultFromResponse(result, appleResponse);

        Integer status = appleResponse.getInt("status");
        if (status == null) {
            return setFailureResult(result, isSandbox);
        }

        // 处理环境切换
        String retryResponse = handleEnvironmentSwitch(status, isSandbox, requestBody);
        if (retryResponse != null) {
            appleResponse = JSONUtil.parseObj(retryResponse);
            populateResultFromResponse(result, appleResponse);
            status = appleResponse.getInt("status");
            isSandbox = status == 21007;
        }

        result.setStatus(status);
        result.setSandbox(isSandbox);
        // 默认没有终身订单
        result.setHaveLifeOrder(false);
        result.setSubscribeChange(false);

        if (status != 0 || !extractReceiptFields(alias, appleResponse, result)) {
            return setFailureResult(result, isSandbox);
        }

        result.setSuccess(true);
        return result;
    }

    // 填充响应字段
    private void populateResultFromResponse(AppleReceiptResult result, JSONObject appleResponse) {
        result.setEnvironment(appleResponse.getStr("environment"));
        result.setReceipt(appleResponse.getJSONObject("receipt"));
        result.setLatestReceipt(appleResponse.getStr("latest_receipt"));
        result.setLatestReceiptInfo(appleResponse.getJSONArray("latest_receipt_info"));
        result.setPendingRenewalInfo(appleResponse.getJSONArray("pending_renewal_info"));
    }

    // 处理环境切换
    private String handleEnvironmentSwitch(Integer status, boolean isSandbox, String requestBody) {
        if (status == 21007 && !isSandbox) {
            return sendHttpRequestToApple(URL_SANDBOX, requestBody);
        } else if (status == 21008 && isSandbox) {
            return sendHttpRequestToApple(URL_PRODUCTION, requestBody);
        }
        return null;
    }

    // 提取收据字段
    private boolean extractReceiptFields(String alias, JSONObject response, AppleReceiptResult out) {
        try {
            JSONObject receipt = response.getJSONObject("receipt");
            JSONArray latestReceiptInfo = response.getJSONArray("latest_receipt_info");
            if (receipt == null || latestReceiptInfo == null || latestReceiptInfo.isEmpty()) {
                return false;
            }

            // 设置收据基本信息
            out.setBundleId(receipt.getStr("bundle_id"));
            out.setApplicationVersion(receipt.getStr("application_version"));
            out.setReceiptType(receipt.getStr("receipt_type"));

            // 设置最新交易信息
            for (Object lastReceipt : latestReceiptInfo) {
                JSONObject lr = JSONUtil.parseObj(lastReceipt);
                if (null == lr) {
                    return false;
                }
                if (alias.equals(lr.getStr("product_id"))) {
                    out.setLatestInApp(lr);
                    out.setTransactionId(lr.getStr("transaction_id"));
                    out.setProductId(lr.getStr("product_id"));
                    out.setPurchaseDate(lr.getStr("purchase_date"));
                    out.setExpiresDate(lr.getStr("expires_date"));
                    out.setExpiresDateMs(lr.getStr("expires_date_ms"));
                    out.setOriginalTransactionId(lr.getStr("original_transaction_id"));
                }
                if (null == lr.getStr("expires_date_ms")) {
                    out.setHaveLifeOrder(true);
                }
            }
            // 如果当前订单号是空的,判断是否是订阅变化
            if (StringUtils.hasLength(out.getTransactionId())) {
                // 待订阅信息
                JSONArray pendingRenewalInfo = response.getJSONArray("pending_renewal_info");
                if (null != pendingRenewalInfo && !pendingRenewalInfo.isEmpty()) {
                    Object objPending = pendingRenewalInfo.get(pendingRenewalInfo.size() - 1);
                    if (null != objPending) {
                        JSONObject opj = JSONUtil.parseObj(objPending);
                        // 待订阅产品ID
                        String autoRenewProductId = opj.getStr("auto_renew_product_id");
                        // 有效产品ID
                        String productId = opj.getStr("product_id");
                        if (StringUtils.hasLength(autoRenewProductId) && StringUtils.hasLength(productId) && !productId.equals(autoRenewProductId)) {
                            out.setTransactionId(opj.getStr("original_transaction_id"));
                            out.setProductId(productId);
                            out.setSubscribeChange(true);
                        }
                    }
                }
            }
            return StringUtils.hasLength(out.getBundleId()) && StringUtils.hasLength(out.getTransactionId()) && StringUtils.hasLength(out.getProductId());
        } catch (Exception e) {
            log.error("[苹果支付验签] 提取收据字段异常", e);
            return false;
        }
    }

    // 发送POST请求到苹果
    private String sendHttpRequestToApple(String url, String requestBody) {
        return sendHttpRequest(url, "POST", requestBody, CONTENT_TYPE_JSON);
    }


    // 通用HTTP请求方法
    private String sendHttpRequest(String url, String method, String requestBody, String contentType) {
        HttpURLConnection connection = null;
        try {
            connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setRequestMethod(method);
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("User-Agent", USER_AGENT);
            connection.setConnectTimeout(CONNECT_TIMEOUT_MS);
            connection.setReadTimeout(READ_TIMEOUT_MS);

            if ("POST".equals(method) && requestBody != null) {
                connection.setRequestProperty("Content-Type", contentType);
                connection.setDoOutput(true);
                try (OutputStream os = connection.getOutputStream()) {
                    os.write(requestBody.getBytes(StandardCharsets.UTF_8));
                }
            }

            if (connection.getResponseCode() == 200) {
                try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = br.readLine()) != null) {
                        response.append(line.trim());
                    }
                    return response.toString();
                }
            }
            return null;
        } catch (Exception e) {
            log.error("[苹果支付验签] HTTP请求异常", e);
            return null;
        } finally {
            if (connection != null) connection.disconnect();
        }
    }


    // 验证苹果收据（详细结果）
    public AppleReceiptResult verifyAppleNotice(String noticeStr) {
        AppleReceiptResult result = new AppleReceiptResult();
        try {
            // 解析苹果相关参数
            JSONObject payload = verifyAndGet(noticeStr);
            log.info(payload.toString());
            //通知的应用内购买或外部购买事件的类型
            String notificationType = payload.get("notificationType").toString();
            //通知数据
            JSONObject data = payload.getJSONObject("data");
            //应用环境
            String environment = data.get("environment").toString();
            JSONObject transactionInfo = null;
            // 签名的交易信息
            if (StringUtils.hasLength(data.getStr("signedTransactionInfo"))) {
                transactionInfo = verifyAndGet(data.getStr("signedTransactionInfo"));
                log.info("[苹果支付验签] 交易订单信息： {}", transactionInfo);
            }
            // 签名的订阅续订信息
            JSONObject renewalInfo = null;
            if (StringUtils.hasLength(data.getStr("signedRenewalInfo"))) {
                renewalInfo = verifyAndGet(data.getStr("signedRenewalInfo"));
                log.info("[苹果支付验签] 订阅续订信息： {}", renewalInfo);
            }
            // 订单ID
            String transactionId = null == transactionInfo ? null : transactionInfo.getStr("transactionId");
            // 原始订单ID
            String originalTransactionId = null == transactionInfo ? renewalInfo.getStr("originalTransactionId") : transactionInfo.getStr("originalTransactionId");
            String productId = null == transactionInfo ? renewalInfo.getStr("productId") : transactionInfo.getStr("productId");
            AppleAutoPayStatusEnum statusEnum = AppleAutoPayStatusEnum.valueOf(notificationType);
            String iosAutoPayStatusEnumStr = statusEnum.getStatusDesc();
            log.info("[苹果支付验签]：{} {} ，environment：{}，transactionId：{}，originalTransactionId：{}，notificationType：{}",
                    notificationType, iosAutoPayStatusEnumStr, environment, transactionId, originalTransactionId, notificationType);
            return switch (statusEnum) {
                case SUBSCRIBED ->
                    // 初始购买
                    // 3. 订单状态校验
                       // OrgUserOrder dbUserOrder = payMapper.getUserOrderByOutTradeNo(transactionId);
                        result;
                case DID_RENEW, NORMAL_RENEWAL, RENEWAL ->
                    // 续订成功
                        result;
                case REFUND ->
                    // 退订
                        result;
                case DID_CHANGE_RENEWAL_PREF ->
                    // 升降级
                        result;
                case EXPIRED, CANCEL ->
                    // 取消订阅
                        result;
                default -> result;
            };
        } catch (Exception e) {
            log.error("[苹果支付验签] 验证异常", e);
            return setFailureResult(result, false);
        }
    }

    /**
     * 验证签名
     *
     * @param jws
     * @return
     * @throws CertificateException
     */
    private JSONObject verifyAndGet(String jws) throws CertificateException {
        DecodedJWT decodedJWT = JWT.decode(jws);
        // 拿到 header 中 x5c 数组中第一个
        String header = new String(Base64.getDecoder().decode(decodedJWT.getHeader()));
        String x5c = JSONUtil.parseObj(header).getJSONArray("x5c").getStr(0);

        // 获取公钥
        PublicKey publicKey = getPublicKeyByX5c(x5c);

        // 验证 token
        Algorithm algorithm = Algorithm.ECDSA256((ECPublicKey) publicKey, null);

        try {
            algorithm.verify(decodedJWT);
        } catch (SignatureVerificationException e) {
            throw new RuntimeException("签名验证失败");
        }
        // 解析数据
        return JSONUtil.parseObj(new String(Base64.getDecoder().decode(decodedJWT.getPayload())));
    }


    /**
     * 获取公钥
     *
     * @param x5c
     * @return
     * @throws CertificateException
     */
    private PublicKey getPublicKeyByX5c(String x5c) throws CertificateException {
        byte[] x5c0Bytes = Base64.getDecoder().decode(x5c);
        CertificateFactory fact = CertificateFactory.getInstance("X.509");
        X509Certificate cer = (X509Certificate) fact.generateCertificate(new ByteArrayInputStream(x5c0Bytes));
        return cer.getPublicKey();
    }
}
