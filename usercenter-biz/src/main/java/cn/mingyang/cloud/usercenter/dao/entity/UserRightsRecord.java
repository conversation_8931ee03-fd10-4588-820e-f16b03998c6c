package cn.mingyang.cloud.usercenter.dao.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户权益记录表
 */
@Data
public class UserRightsRecord {

    /**
     * 记录ID，主键，自增
     */
    private Integer id;

    /**
     * 用户ID，关联用户表
     */
    private Integer userid;

    /**
     * 功能ID，关联功能表
     */
    private Integer functionid;

    /**
     * 卖点组编号，默认0
     */
    private Integer sellgroup;

    /**
     * 消耗数量，保留2位小数
     */
    private Float quantity;

    /**
     * 消耗描述
     */
    private String desc;

    /**
     * 消耗时间，自动记录当前时间
     */
    private LocalDateTime time;

    /**
     * 会员权益流水标识
     * 101: 注册会员赠送;
     * 102: 购买VIP赠送;
     * 103: 购买SVIP赠送;
     * 104: 客服调整;
     * 105: 购买加油包;
     * 201: 消耗;
     */
    private Integer recordtype;


}
