package cn.mingyang.cloud.usercenter.controller;

import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.center.common.comEnum.BaseErrorCodeEnum;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.usercenter.service.TencentService;
import cn.mingyang.cloud.center.common.util.ValidateUtils;
import com.tencentcloudapi.vod.v20180717.models.SearchMediaRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 腾讯API控制层
 */
@Slf4j
@RestController
@RequestMapping("/tencent")
public class TencentApiController {

    private final TencentService tencentService;

    private final ValidateUtils validateUtils;

    public TencentApiController(TencentService tencentService, ValidateUtils validateUtils) {
        this.tencentService = tencentService;
        this.validateUtils = validateUtils;
    }


    /**
     * 查询腾讯云媒体
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10060&*")
    public CommonResult<?> getTencentMedia(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("查询腾讯云媒体, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        //参数校验
        SearchMediaRequest searchMediaRequest = validateUtils.validateParam(comRequest.getParam(), SearchMediaRequest.class);
        return tencentService.getTencentMedia(searchMediaRequest);
    }


    /**
     * 删除腾讯云媒体
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/api/c=10061&*")
    public CommonResult<?> delTencentMedia(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("删除腾讯云媒体, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        //参数校验
        SearchMediaRequest searchMediaRequest = validateUtils.validateParam(comRequest.getParam(), SearchMediaRequest.class);
        if (null == searchMediaRequest.getSubAppId()) {
            return CommonResult.error(BaseErrorCodeEnum.DEL_MEDIA_ERROR.getCode(), "删除云媒体时，应用ID不能为空");
        }
        return tencentService.delTencentMedia(searchMediaRequest);
    }


}