package cn.mingyang.cloud.usercenter.response;


import lombok.Data;

/**
 * 支付订阅请求
 */
@Data
public class PaySubscribeResponse {

    /**
     * ID
     */
    private String id;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 签约时间
     */
    private String time;

    /**
     * 下次扣款时间
     */
    private String deducttime;

    /**
     * 费用
     */
    private String fee;

    /**
     * 标题
     */
    private String title;

    /**
     * 数量
     */
    private String quantity;

    /**
     * 1 正常签约 2取消签约
     */
    private String status;


}
