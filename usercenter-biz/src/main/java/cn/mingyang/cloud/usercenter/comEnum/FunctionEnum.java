package cn.mingyang.cloud.usercenter.comEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * 老权益接口号映射表
 */
@Slf4j
public enum FunctionEnum {
    CREATE_VIDEO_V2(13010, 1, "createVideoV2", "图文成片", 1),
    COMIC_TWEETS_V2(13011, 5, "comicTweetsV2", "漫画推文", 1),
    VIDEO_VFX_V2(13012, 7, "videoVfxV2", "特效推文", 1),
    REMOVE_SUBTITLE_TASK_V2(13001, 0, "submitRemoveSubtitleTaskV2", "去字幕", 3),
    SEPARATION_V2(13013, 0, "separationV2", "人声分离", 3),
    REMOVE_WATERMARK_V2(13014, 0, "removeWatermarkV2", "去水印", 3),
    VIDEO_TRANS_V2(13015, 0, "videoTransV2", "文案提取", 3),
    AV_TO_VIDEO_V2(13016, 4, "avToVideoV2", "视频二创", 1),
    AI_DRAW_IMG_V2(13017, 16, "pictureEditV2", "ai绘画", 2),
    MEMES_VIDEO_V2(13018, 3, "memeVideoV2", "表情包推文", 1),
    VIDEO_EDIT_V2(13019, 6, "videoEditV2", "视频编辑", 1),
    REPLACE_VIDEO_BACKGROUND_V2(13020, 9, "replaceVideoBackgroundV2", "视频更换背景", 1),
    PIC_TO_VIDEO_V2(13021, 12, "picToVideoV2", "滚动推文/聊天推文", 1),
    VIDEO_ADD_SUBTITLE(13003, 14, "addSubTitles", "视频添加字幕", 3),
    GOODSMIXCLIP(13008, 15, "goodsMixClip", "好物混剪", 1),
    HCARSDEB(13036, 17, "hcarsDeb", "豪车配音", 1);

    private final int cmd;
    private final int type;
    private final String functionName;
    private final String description;
    // 1 时长 2 张数 3 次数
    private final int expendType;


    FunctionEnum(int cmd, int type, String functionName, String description, int expendType) {
        this.cmd = cmd;
        this.type = type;
        this.functionName = functionName;
        this.description = description;
        this.expendType = expendType;
    }

    public int getCmd() {
        return cmd;
    }

    public String getFunctionName() {
        return functionName;
    }

    public String getDescription() {
        return description;
    }

    public int getType() {
        return type;
    }

    // 根据 cmd 获取枚举值
    public static FunctionEnum getFunctionEnumByCmd(int cmd) {
        if (cmd == 0) {
            return null;
        }
        try {
            for (FunctionEnum function : values()) {
                if (function.getCmd() == cmd) {
                    return function;
                }
            }
        } catch (Exception e) {
            log.error("Invalid cmd: {}", cmd);
            return null;
        }
        return null;
    }

    public int getExpendType() {
        return expendType;
    }
}
