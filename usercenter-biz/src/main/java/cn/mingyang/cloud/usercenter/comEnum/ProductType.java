package cn.mingyang.cloud.usercenter.comEnum;

import lombok.Getter;

/**
 * 产品类型
 */
@Getter
public enum ProductType {
    VIP("101", "VIP"),
    SVIP("102", "SVIP"),
    OIL("106", "加油包");

    private final String code;

    private final String desc;

    ProductType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProductType fromCode(String code) {
        for (ProductType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

}
