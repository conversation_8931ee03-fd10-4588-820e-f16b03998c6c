package cn.mingyang.cloud.usercenter.dao.entity;


import lombok.Data;

import java.io.Serial;
import java.io.Serializable;


/**
 * 功能卖点信息
 */
@Data
public class SellPointInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 123456792L;

    /**
     * 卖点组id（主键自增）
     */
    private int sellgroupid;

    /**
     * 卖点组（不唯一，同一个卖点可根据appid有不同配置）
     */
    private Integer sellgroup;

    /**
     * 卖点名称
     */
    private String sellname;

    /**
     * 免费数量
     */
    private Float free;

    /**
     * 重置数量
     */
    private Float quantity;

    /**
     * svip重置数量
     */
    private Float svipquantity;

    /**
     * 重置单元（日, 月, 年, 百年）
     */
    private Integer resetunit;

    /**
     * appid
     */
    private String appid;

    /**
     * 商品初始ID
     */
    private String startuserid;

    /**
     * 商品终止ID
     */
    private String enduserid;

    /**
     * 1：不重置加油包
     * 2：可重置加油包
     * 3：循环资源包
     */
    private Integer resettype;

    /**
     * 1：金币
     * 2：按数量计费类型
     * 3：按次计费类型
     */
    private Integer selltype;

    /**
     * 备注
     */
    private String note;

}
