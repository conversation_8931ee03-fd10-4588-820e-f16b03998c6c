package cn.mingyang.cloud.usercenter.controller;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.request.BaseRequest;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.center.common.util.XmlUtils;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.api.response.UserRightResponse;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import cn.mingyang.cloud.usercenter.comEnum.PayType;
import cn.mingyang.cloud.usercenter.dao.domain.HarmonyNoticeParam;
import cn.mingyang.cloud.usercenter.dao.domain.UserDetail;
import cn.mingyang.cloud.usercenter.dao.entity.OrgUserOrder;
import cn.mingyang.cloud.usercenter.dao.entity.PayConfig;
import cn.mingyang.cloud.usercenter.dao.entity.PaySubscribe;
import cn.mingyang.cloud.usercenter.dao.mapper.PayMapper;
import cn.mingyang.cloud.usercenter.request.BuyProductRequest;
import cn.mingyang.cloud.usercenter.service.*;
import com.alibaba.druid.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 支付回调接口
 */
@RestController
@RequestMapping("/callback")
@Slf4j
public class PayCallBackController {


    private final WxPayService wxPayService;
    private final AliPayService aliPayService;
    private final OrderService orderService;
    private final ProductService productService;
    private final UnifiedPayService unifiedPayService;
    private final UnionPayService unionPayService;
    private final HarmonyPayService harmonyPayService;
    private final PayMapper payMapper;

    public PayCallBackController(WxPayService wxPayService, AliPayService aliPayService, OrderService orderService, ProductService productService, UnifiedPayService unifiedPayService, UnionPayService unionPayService, HarmonyPayService harmonyPayService, PayMapper payMapper) {
        this.wxPayService = wxPayService;
        this.aliPayService = aliPayService;
        this.orderService = orderService;
        this.productService = productService;
        this.unifiedPayService = unifiedPayService;
        this.unionPayService = unionPayService;
        this.harmonyPayService = harmonyPayService;
        this.payMapper = payMapper;
    }


    /**
     * 微信支付回调 V2（XML+MD5验签）
     */
    @RequestMapping(value = "/wxpay/v2/{pid}", method = {RequestMethod.POST, RequestMethod.GET})
    public String wxPayCallbackV2(@PathVariable("pid") String pid, HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        String notifyData = sb.toString();
        try {
            Map<String, String> resultMap = XmlUtils.parseXmlResponse(notifyData);
            log.info("[微信支付回调V2] 业务标识： {} 内容: {}", pid, resultMap);
            // 签名校验
            boolean md5Check = wxPayService.verifyWxPayNotifyV2(pid, resultMap);
            if (md5Check) {
                // 验签通过，处理业务
                String outTradeNo = resultMap.get("out_trade_no");
                if (StringUtils.isEmpty(outTradeNo)) {
                    return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[获取订单号失败]]></return_msg></xml>";
                }
                UserDetail userDetail = orderService.handlePayNotice(pid, outTradeNo, PayType.WX.getCode());
                if (null != userDetail) {
                    initUserRights(pid, userDetail);
                }
                return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
            } else {
                return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[签名失败]]></return_msg></xml>";
            }
        } catch (Exception e) {
            log.error("[微信支付回调V2] 解析异常", e);
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[解析异常]]></return_msg></xml>";
        }
    }

    /**
     * 微信支付回调 V3（JSON+RSA验签+解密，官方SDK NotificationParser）
     */
    @RequestMapping(value = "/wxpay/v3/{pid}", method = {RequestMethod.POST, RequestMethod.GET})
    public String wxPayCallbackV3(@PathVariable("pid") String pid, HttpServletRequest request) throws IOException {
        String body = new String(request.getInputStream().readAllBytes(), request.getCharacterEncoding() != null ? request.getCharacterEncoding() : "UTF-8");
        String serial = request.getHeader("Wechatpay-Serial");
        String nonce = request.getHeader("Wechatpay-Nonce");
        String timestamp = request.getHeader("Wechatpay-Timestamp");
        String signature = request.getHeader("Wechatpay-Signature");
        log.info("[微信支付回调V3] body: {}", body);
        log.info("[微信支付回调V3] headers: serial={}, nonce={}, timestamp={}, signature={}", serial, nonce, timestamp, signature);
        try {
            // 这里需要你在WxPayService中注入NotificationParser，并配置好APIv3密钥等
            // com.wechat.pay.java.core.notification.NotificationRequest notifyRequest = new com.wechat.pay.java.core.notification.NotificationRequest.Builder()
            //         .withSerialNumber(serial)
            //         .withNonce(nonce)
            //         .withTimestamp(timestamp)
            //         .withSignature(signature)
            //         .withBody(body)
            //         .build();
            // Transaction transaction = wxPayService.parseWxPayNotifyV3(notifyRequest);
            // wxPayService.handleWxPayNotifyV3(transaction);
            // return "{\"code\":\"SUCCESS\",\"message\":\"成功\"}";
            return "{\"code\":\"SUCCESS\",\"message\":\"成功\"}";
        } catch (Exception e) {
            log.error("[微信支付回调V3] 解析异常", e);
            return "{\"code\":\"FAIL\",\"message\":\"解析异常\"}";
        }
    }

    /**
     * 支付宝支付回调
     */
    @RequestMapping(value = "/alipay/{pid}", method = {RequestMethod.POST, RequestMethod.GET})
    public String aliPayCallback(@PathVariable("pid") String pid, HttpServletRequest request) {
        try {
            // 获取请求参数
            request.setCharacterEncoding("UTF-8");
            TreeMap<String, String> params = getParams(request);
            log.info("[支付宝支付回调] pid: {} 内容: {}", pid, params);
            // 验签有问题
            boolean valid = aliPayService.verifyAliPayNotify(pid, params);
            if (valid) {
                // 验签通过，处理业务
                String outTradeNo = params.get("out_trade_no");
                if (StringUtils.isEmpty(outTradeNo)) {
                    log.error("[支付宝支付回调] 缺少商家订单号, params={}", params);
                    return "fail";
                }
                UserDetail userDetail = orderService.handlePayNotice(pid, outTradeNo, PayType.ALI.getCode());
                if (null != userDetail) {
                    initUserRights(pid, userDetail);
                }
                return "success";
            } else {
                log.error("[支付宝支付回调] 验签失败, params={}", params);
                return "fail";
            }
        } catch (Exception e) {
            log.error("[支付宝支付回调] 验签异常", e);
            return "fail";
        }

    }

    /**
     * 支付宝订阅回调
     */
    @RequestMapping(value = "/subscribe/ali/{pid}", method = {RequestMethod.POST, RequestMethod.GET})
    public String aliPaySubscribeCallback(@PathVariable("pid") String pid,
                                          HttpServletRequest request) {
        try {
            // 获取请求参数
            request.setCharacterEncoding("UTF-8");
            TreeMap<String, String> params = getParams(request);
            log.info("[支付宝订阅回调] 业务标识: {} 内容: {}", pid, JSONUtil.toJsonStr(params));
            // 首次订阅单号
            String externalAgreementNo = params.get("external_agreement_no");
            if (StrUtil.isBlank(externalAgreementNo)) {
                log.error("[支付宝订阅回调] externalAgreementNo 首次订阅单号为空 ");
                return "fail";
            }
            //遍历业务ID,查询订阅记录
            PaySubscribe dbPaySubscribe = orderService.selectPaySubscribeByFirstOutTradeNo(pid, externalAgreementNo);
            if (null == dbPaySubscribe) {
                for (String aPid : Constant.AllDs.ALL) {
                    dbPaySubscribe = orderService.selectPaySubscribeByFirstOutTradeNo(aPid, externalAgreementNo);
                    if (null != dbPaySubscribe) {
                        pid = aPid;
                        log.info("[支付宝订阅回调] 业务标识转化， 原pid：{} 转pid: {}", pid, aPid);
                    }
                }
            }
            // 验签有问题
            boolean valid = aliPayService.verifyAliPayNotify(pid, params);
            if (valid) {
                // 获取订阅协议号
                String agreementNo = params.get("agreement_no");
                // 获取订阅状态
                String status = params.get("status");
                // 支付平台AppId
                String appId = params.get("app_id");
                // 支付宝唯一用户号
                String alipayUserId = params.get("alipay_user_id");
                // 更新订阅表签约状态以及协议号
                PayConfig payConfig = orderService.getPayChannelConfig(pid, appId, PayType.ALI.getCode());
                return aliPayService.doSubscribe(alipayUserId, pid, payConfig, agreementNo, status, externalAgreementNo);
            } else {
                log.error("[支付宝订阅回调] 验签失败, params={}", params);
                return "fail";
            }
        } catch (Exception e) {
            log.error("[支付宝订阅回调] 处理异常", e);
            return "fail";
        }
    }

    /**
     * 通联支付回调
     */
    @RequestMapping(value = "/union/pay/{pid}", method = {RequestMethod.POST, RequestMethod.GET})
    public String unionPayCallback(@PathVariable("pid") String pid, HttpServletRequest request) {
        try {
            request.setCharacterEncoding("UTF-8");
            TreeMap<String, String> params = getParams(request);
            log.info("[通联支付回调] 业务标识: {} 内容: {}", pid, JSONUtil.toJsonStr(params));
            if (MapUtil.isNotEmpty(params)) {
                // 验证签名
                if (!unionPayService.verifyUnionPayNotify(pid, params)) {
                    log.error("[通联支付回调] 验签失败, params={}", params);
                    return "error";
                }
                // 取值标识
                String flag = "cusorderid";
                if (MapUtil.isNotEmpty(params) && !StringUtils.isEmpty(params.get(flag))) {
                    log.info("[通联支付回调] 业务标识: {} cusorderid: {}", pid, params.get(flag));
                    UserDetail userDetail = orderService.handlePayNotice(pid, params.get(flag), PayType.TL.getCode());
                    if (null != userDetail) {
                        initUserRights(pid, userDetail);
                    }
                }
                return "success";
            } else {
                return "error";
            }
        } catch (Exception e) {
            log.error("[通联支付回调] 处理异常", e);
            return "error";
        }
    }

    /**
     * 小米支付回调
     */
    @RequestMapping(value = "/mi/pay/{pid}", method = {RequestMethod.POST, RequestMethod.GET})
    public String miPayCallback(@PathVariable("pid") String pid, HttpServletRequest request) {
        try {
            request.setCharacterEncoding("UTF-8");
            TreeMap<String, String> params = getParams(request);
            log.info("[小米支付回调] 业务标识: {} 内容: {}", pid, JSONUtil.toJsonStr(params));
            if (MapUtil.isEmpty(params)) {
                return this.miCallBackResult(1525, "参数缺失");
            }
            boolean valid = unifiedPayService.verifyMiPayNotify(pid, params);
            if (!valid) {
                log.error("[小米支付回调] 验签失败, params={}", params);
                return this.miCallBackResult(1525, "验签失败");
            }
            String outTradeNo = unifiedPayService.getOutTradeNoFromNotify(params);
            if (StringUtils.isEmpty(outTradeNo)) {
                log.error("[小米支付回调] 缺少订单号, params={}", params);
                return this.miCallBackResult(1506, "缺少订单号");
            }
            //遍历查询订单信息
            OrgUserOrder userOrder = orderService.getUserOrder(pid, outTradeNo);
            if (null == userOrder) {
                for (String aPid : Constant.AllDs.ALL) {
                    userOrder = orderService.getUserOrder(aPid, outTradeNo);
                    if (null != userOrder) {
                        pid = aPid;
                        log.info("[小米支付回调] 业务标识转化， 原pid：{} 转pid: {}", pid, aPid);
                    }
                }
            }
            UserDetail userDetail = orderService.handlePayNotice(pid, outTradeNo, PayType.MI.getCode());
            if (null != userDetail) {
                initUserRights(pid, userDetail);
            }
            return this.miCallBackResult(200, "success");
        } catch (Exception e) {
            log.error("[小米支付回调] 处理异常", e);
            return this.miCallBackResult(1506, "处理异常");
        }
    }

    /**
     * 返回回调结果
     *
     * @param errcode 错误码
     * @param errMsg  错误信息
     * @return 结果
     */
    private String miCallBackResult(Integer errcode, String errMsg) {
        Map<String, Object> result = new HashMap<>();
        result.put("errcode", errcode);
        result.put("errMsg", errMsg);
        return JSONUtil.toJsonStr(result);
    }

    /**
     * 动态遍历获取所有收到的参数,此步非常关键,因为收银宝以后可能会加字段,动态获取可以兼容由于收银宝加字段而引起的签名异常
     *
     * @param request 请求参数
     * @return 排序Map
     */
    private TreeMap<String, String> getParams(HttpServletRequest request) {
        TreeMap<String, String> map = new TreeMap<>();
        Map reqMap = request.getParameterMap();
        for (Object key : reqMap.keySet()) {
            String value = ((String[]) reqMap.get(key))[0];
            map.put(key.toString(), value);
        }
        return map;
    }

    /**
     * 调用权益新增
     *
     * @param pid        业务ID
     * @param userDetail 用户信息
     */
    private void initUserRights(String pid, UserDetail userDetail) {
        try {
            BuyProductRequest buyRequest = new BuyProductRequest();
            buyRequest.setProductid(userDetail.getProductid());
            CmdRequest cmd = new CmdRequest("", "", userDetail.getDf(), "", pid);
            ComRequest comRequest = new ComRequest();
            BaseRequest baseRequest = new BaseRequest();
            baseRequest.setUserid(userDetail.getUserid() + "");
            baseRequest.setOsid(userDetail.getOsid());
            comRequest.setBase(baseRequest);
            CommonResult<UserRightResponse> result = productService.buyProduct(buyRequest, cmd, comRequest);
            if (result.isSuccess()) {
                log.info("Successfully processed product purchase for pid: {} user: {}, product: {}", pid, userDetail.getUserid(), userDetail.getProductid());
            } else {
                log.error("Failed to process product purchase for pid: {}, user: {}, product: {}, result: {}", pid,
                        userDetail.getUserid(), userDetail.getProductid(), result.getMsg());
            }
        } catch (Exception e) {
            log.error("Failed to process product purchase for pid: {}, Detail: {}", pid, JSONUtil.toJsonStr(userDetail));
        }
    }

    /**
     * 苹果支付回调
     */
    @RequestMapping(value = "/apple/pay/{pid}/{appId}", method = {RequestMethod.POST, RequestMethod.GET})
    public String applePayCallback(@PathVariable("pid") String pid,
                                   @PathVariable("appId") String appId,
                                   HttpServletRequest request) {
        try {
            String body = new String(request.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
            log.info("[苹果支付回调] 业务标识: {} 内容: {}", pid, body);
            JSONObject checkResult = unifiedPayService.verifyApplePayNotify(pid, appId, body);
            if (null != checkResult) {
                // 从data字段中获取交易信息
                JSONObject data = checkResult.getJSONObject("data");
                if (data == null) {
                    log.error("[苹果支付回调] 缺少交易数据");
                    return "fail";
                }
                String transactionId = data.getStr("transactionId");
                if (StringUtils.isEmpty(transactionId)) {
                    log.error("[苹果支付回调] 缺少交易ID, body={}", data);
                    return "fail";
                }
                log.info("[苹果支付回调] 业务标识: {} 内容: {}", pid, data);
                //遍历业务ID,查询订阅记录
                PaySubscribe dbPaySubscribe = orderService.selectPaySubscribeByFirstOutTradeNo(pid, transactionId);
                if (null == dbPaySubscribe) {
                    for (String aPid : Constant.AllDs.ALL) {
                        dbPaySubscribe = orderService.selectPaySubscribeByFirstOutTradeNo(aPid, transactionId);
                        if (null != dbPaySubscribe) {
                            pid = aPid;
                            log.info("[苹果支付回调] 业务标识转化， 原pid：{} 转pid: {}", pid, aPid);
                        }
                    }
                }
                if (null == dbPaySubscribe) {
                    log.error("[苹果支付回调] 交易ID不存在, transactionId={}", transactionId);
                    return "fail";
                }
                OrgUserOrder orgUserOrder = orderService.createPayOrderBySubscribe(pid, dbPaySubscribe);
                if (null == orgUserOrder) {
                    log.error("[苹果支付回调] 苹果待支付订单创建失败, transactionId={}", transactionId);
                    return "fail";
                }
                UserDetail userDetail = orderService.handlePayNotice(pid, orgUserOrder.getOutTradeNo(), PayType.APPLE.getCode());
                if (null != userDetail) {
                    // 更新下次扣款时间
                    orderService.updatePaySubscribeNextDeductTime(pid, dbPaySubscribe.getId(), dbPaySubscribe.getNextdeducttime()
                            .plusDays(dbPaySubscribe.getDuration()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    // 初始化权益
                    initUserRights(pid, userDetail);
                }
                return "success";
            } else {
                log.error("[苹果支付回调] 验签失败, body={}", body);
                return "fail";
            }
        } catch (Exception e) {
            log.error("[苹果支付回调] 处理异常", e);
            return "fail";
        }
    }

    /**
     * 鸿蒙内购统一通知回调
     * 支持：首次购买、续费、订阅取消、退款等所有事件类型
     */
    @RequestMapping(value = "/harmony/notify/{pid}", method = {RequestMethod.POST})
    public ResponseEntity<String> harmonyNotifyCallback(@PathVariable("pid") String pid, HttpServletRequest request) {
        String jwsNotification = null;
        try {
            String body = new String(request.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
            log.info("[鸿蒙内购统一通知] 业务标识: {} 报文内容: {}", pid, body);
            // 华为通知参数名：jwsNotification（JWS紧凑串）
            jwsNotification = request.getParameter("jwsNotification");
            if (StrUtil.isBlank(jwsNotification)) {
                // 尝试从JSON体中取
                if (StrUtil.isNotBlank(body) && JSONUtil.isTypeJSON(body)) {
                    JSONObject temp = JSONUtil.parseObj(body);
                    jwsNotification = temp.getStr("jwsNotification");
                }
            }
            if (StrUtil.isBlank(jwsNotification)) {
                log.error("[鸿蒙内购统一通知] 缺少jwsNotification参数");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Missing jwsNotification");
            }
            log.info("[鸿蒙内购统一通知] 业务标识: {} jws长度: {}", pid, jwsNotification.length());
            // 验签并解析JWS，得到通知载荷
            HarmonyNoticeParam noticeParam = harmonyPayService.verifyAndParseJwsNotification(jwsNotification);
            if (noticeParam == null) {
                log.error("[鸿蒙内购统一通知] 验签或解析失败");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("Verify failed");
            }
            // 查询支付配置
            PayConfig payConfig = orderService.getPayChannelConfig(
                    pid,
                    noticeParam.getNotificationMetaData().getApplicationId(),
                    PayType.HARMONY.getCode()
            );
            if (payConfig == null) {
                log.error("[鸿蒙内购统一通知] 未找到对应的支付配置");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Config not found");
            }
            log.info("[鸿蒙内购统一通知] 事件类型: {}, 消息内容: {}",
                    noticeParam.getNotificationType(), JSONUtil.toJsonStr(noticeParam));
            // 处理不同事件类型 - 添加break防止case穿透
            switch (noticeParam.getNotificationType()) {
                case "DID_NEW_TRANSACTION":
                    didNewTransaction(pid, payConfig, noticeParam);
                    break;
                case "DID_CHANGE_RENEWAL_STATUS":
                    harmonyPayService.didChangeRenewalStatus(noticeParam);
                    break;
                case "REVOKE":
                    harmonyPayService.revoke(noticeParam);
                    break;
                case "RENEWAL_TIME_MODIFIED":
                    harmonyPayService.renewalTimeModified(noticeParam);
                    break;
                case "EXPIRE":
                    harmonyPayService.expire(noticeParam);
                    break;
                case "TEST":
                    log.warn("[鸿蒙内购统一通知] TEST类型不处理: {} ", JSONUtil.toJsonStr(noticeParam));
                    break;
                default:
                    log.warn("[鸿蒙内购统一通知] 未知的事件类型: {}", noticeParam.getNotificationType());
                    break;
            }
            // 成功处理，返回200状态码和success内容
            return ResponseEntity.ok("success");
        } catch (Exception e) {
            log.error("[鸿蒙内购统一通知] 处理异常, jwsNotification: {}", jwsNotification, e);
            // 返回500状态码，让华为服务器知道需要重试
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Server error");
        }
    }

    /**
     * 订单已购买/订阅已购买/订阅续订成功
     *
     * @param pid         业务ID
     * @param payConfig   支付配置
     * @param noticeParam 参数
     */
    private void didNewTransaction(String pid, PayConfig payConfig, HarmonyNoticeParam noticeParam) {
        UserDetail userDetail = null;
        // 1、 根据子类型做区分处理
        switch (noticeParam.getNotificationSubtype()) {
            // 消耗型/非消耗型/非续期订阅商品购买成功; 自动续期订阅商品的第一次购买成功
            case "INITIAL_BUY":
                // 续期成功
            case "DID_RENEW":
                // 用户主动恢复了一个订阅型商品，续期恢复正常
            case "RESTORE":
                // 用户调整自动续期订阅商品升级或跨级且立即生效
            case "UPGRADE":
                // 订阅重试扣费成功
            case "BILLING_RECOVERY":
                userDetail = harmonyPayService.createOrderAndAddRights(pid, payConfig, noticeParam);
                // 用户调整自动续期订阅商品降级或跨级且在下个续订生效
            case "DOWNGRADE":
                log.info("[鸿蒙内购] subType: {} 不处理", noticeParam.getNotificationSubtype());
        }
        // 标记支付成功与后续权益发放
        if (userDetail != null) {
            initUserRights(userDetail.getPid(), userDetail);
            log.info("[鸿蒙内购] didNewTransaction 处理完成, pid={}, outTradeNo={}, userId={}",
                    userDetail.getPid(), userDetail.getOutTradeNo(), userDetail.getUserid());
        }
    }


}
