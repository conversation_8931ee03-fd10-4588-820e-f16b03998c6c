package cn.mingyang.cloud.usercenter.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.comEnum.BaseErrorCodeEnum;
import cn.mingyang.cloud.center.common.exception.MyBaseException;
import cn.mingyang.cloud.center.common.request.BaseRequest;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.api.response.UserRightResponse;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import cn.mingyang.cloud.usercenter.comEnum.PayType;
import cn.mingyang.cloud.usercenter.dao.domain.AppleReceiptResult;
import cn.mingyang.cloud.usercenter.dao.domain.ProductDoMain;
import cn.mingyang.cloud.usercenter.dao.domain.UserDetail;
import cn.mingyang.cloud.usercenter.dao.entity.OrgUserOrder;
import cn.mingyang.cloud.usercenter.dao.entity.PayConfig;
import cn.mingyang.cloud.usercenter.dao.entity.PaySubscribe;
import cn.mingyang.cloud.usercenter.dao.mapper.PayMapper;
import cn.mingyang.cloud.usercenter.request.*;
import cn.mingyang.cloud.usercenter.response.PayOrderResponse;
import cn.mingyang.cloud.usercenter.uitls.HmacSHA1Encryption;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.ZoneId;
import java.util.*;

/**
 * 统一下单以及回调验证Service
 */
@Slf4j
@Service
public class UnifiedPayService {


    private final PayMapper payMapper;
    private final OrderService orderService;
    private final ProductService productService;
    private final AppleUtilService appleUtilService;
    private final DbCacheService dbCacheService;
    private final AliPayService aliPayService;

    public UnifiedPayService(PayMapper payMapper, OrderService orderService, ProductService productService, AppleUtilService appleUtilService, DbCacheService dbCacheService, AliPayService aliPayService) {
        this.payMapper = payMapper;
        this.orderService = orderService;
        this.productService = productService;
        this.appleUtilService = appleUtilService;
        this.dbCacheService = dbCacheService;
        this.aliPayService = aliPayService;
    }

    /**
     * 支付统一下单
     */
    @DS("#cmd.ds")
    public CommonResult<?> unifiedOrder(CmdRequest cmd, ComRequest comRequest, PayOrderRequest payRequest) {
        try {
            String appId = cmd.getAppId();
            // 校验支付平台
            PayType payType = PayType.fromCode(payRequest.getPayplatform());
            if (null == payType) {
                return CommonResult.error(GlobalErrorCodeConstants.BAD_REQUEST.getCode(), "暂不支持支付平台： " + payRequest.getPayplatform());
            }
            // 查询支付配置
            PayConfig payConfig = payMapper.getPayChannelConfig(appId, payType.getCode());
            if (null == payConfig) {
                payConfig = payMapper.getPayChannelConfig(Constant.DEFAULT, payType.getCode());
                if (null == payConfig) {
                    return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(),
                            "没有找到当前应用: " + appId + " 的" + payType.getDesc() + "支付配置");
                }
            }
            // 创建支付订单
            OrgUserOrder orgUserOrder = orderService.createPayOrder(payRequest, cmd, comRequest, payType.getIndex(), payConfig.getMchid());
            log.info("支付统一下单, 订单信息： {}", JSONUtil.toJsonStr(orgUserOrder));

            // 构建返回给客户端的数据
            PayOrderResponse payOrderResponse = new PayOrderResponse();
            payOrderResponse.setOuttradeno(orgUserOrder.getOutTradeNo());
            payOrderResponse.setPlatformappid(payConfig.getPlatformappid());
            payOrderResponse.setPlatformmchid(payConfig.getMchid());
            payOrderResponse.setTimestamp(orgUserOrder.getTime().atZone(ZoneId.systemDefault()).toInstant().getEpochSecond() + "");
            Map<String, String> payData = buildClientPayData(payConfig, orgUserOrder, payRequest);
            payOrderResponse.setPaydata(payData);
            log.info("支付统一下单,下发客户端支付参数成功, response: {}", JSONUtil.toJsonStr(payOrderResponse));
            return CommonResult.success(payOrderResponse);
        } catch (Exception e) {
            log.error("苹果支付下单报错， Exception： ", e);
            return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "苹果支付下单失败：" + e.getMessage());
        }
    }

    /**
     * 构建客户端所需支付参数
     */
    private Map<String, String> buildClientPayData(PayConfig config,
                                                   OrgUserOrder order,
                                                   PayOrderRequest req) {
        Map<String, String> params = new HashMap<>();
        params.put("payappid", config.getPlatformappid());
        params.put("productid", String.valueOf(req.getProductid()));
        params.put("productname", order.getProductName());
        params.put("amount", String.valueOf(order.getFee()));
        params.put("currency", "CNY");
        params.put("notifyurl", config.getNotifyurl());
        return params;
    }


    /**
     * 验证苹果支付回调
     */
    @DS("#cmd.ds")
    @Transactional
    public CommonResult<?> applePayVerify(CmdRequest cmd, ComRequest com, ApplePayVerifyRequest verifyRequest) {
        try {
            log.info("[苹果支付验签] 开始验证,  isSandbox: {}", verifyRequest.isIssandbox());

            // 1. 查询支付配置 todo appId 替换成系统ID
            PayConfig payConfig = payMapper.getPayChannelConfigByPayChannel(PayType.APPLE.getCode(), PayType.APPLE.getCode());
            if (payConfig == null) {
                log.error("[苹果支付验签] 支付配置不存在");
                return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "支付配置不存在");
            }

            // 2. 验证苹果支付收据
            AppleReceiptResult receipt = verifyApplePayReceipt(verifyRequest, payConfig);
            if (null == receipt) {
                log.error("[苹果支付验签] 收据验证失败");
                return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "苹果支付收据验证失败");
            }

            // 3. 订单状态校验
            OrgUserOrder dbUserOrder = payMapper.getUserOrderByOutTradeNo(receipt.getTransactionId());
            if (null != dbUserOrder) {
                // 如果有终身订单，则不处理
                if (receipt.isHaveLifeOrder()) {
                    log.warn("[苹果支付验签] 存在终身订阅订单，此单无效； 订单信息： {}", receipt.getLatestReceiptInfo());
                    return CommonResult.success(null);
                }
                // 判断是否是订阅变化，而不是支付
                if (receipt.isSubscribeChange()) {
                    log.warn("[苹果支付验签] 订阅变化，此单无效，记录收据； 订单信息： {}", receipt.getLatestReceiptInfo());
                    payMapper.updatePaySubscribeMessage(receipt.getTransactionId(), JSONUtil.toJsonStr(receipt));
                    return CommonResult.success(null);
                }
                // 最新一条交易过期时间戳（订阅）
                String expiresDateMs = receipt.getExpiresDateMs();
                // 过期时间戳不为空，则判断是否有效订阅
                long localExpiresDateMs = DateUtil.date(Long.parseLong(expiresDateMs)).getTime();
                if (localExpiresDateMs > System.currentTimeMillis()) {
                    log.warn("[苹果支付验签] 订阅订单有效期内，无需处理， 订单信息： {}", receipt.getLatestReceiptInfo());
                    return CommonResult.success(null);
                } else {
                    log.info("[苹果支付验签] 订单已是已付款状态，无需重复处理 outTradeNo={}", receipt.getTransactionId());
                    return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "订单已是已付款状态，无需重复处理");
                }
            }
            // 4. 创建支付订单
            List<ProductDoMain> products = dbCacheService.getProductByAlias(cmd.getDs(), receipt.getProductId());
            products = products.stream().filter(pr ->
                    pr.getLg().equals(com.getBase().getLg()) &&
                            pr.getAppid().equals(cmd.getAppId()) &&
                            pr.getOsid().equals(com.getBase().getOsid()) &&
                            pr.getDf().equals(com.getBase().getDf())
            ).toList();
            if (CollectionUtil.isEmpty(products) || products.size() > 1) {
                log.error("[苹果支付验签] 存在多个别名商品， 商品名称： {}", receipt.getProductId());
                return CommonResult.error(BaseErrorCodeEnum.PAY_ERROR.getCode(), "存在多个别名商品");
            }

            // 创建支付订单
            PayOrderRequest payRequest = new PayOrderRequest();
            payRequest.setProductid(products.get(0).getProductid());
            payRequest.setOuttradeno(receipt.getTransactionId());
            OrgUserOrder userOrder = orderService.createPayOrder(payRequest, cmd, com, PayType.APPLE.getIndex(), payConfig.getMchid());
            log.info("[苹果支付验签] 支付统一下单, 订单信息： {}", JSONUtil.toJsonStr(userOrder));


            // 5. 检查订单状态
            if (userOrder.getStatus() != null && userOrder.getStatus() == 2) {
                log.warn("[苹果支付验签] 订单已是已付款状态，无需重复处理, outTradeNo: {}", userOrder.getOutTradeNo());
                return CommonResult.success("订单已处理");
            }

            // 6. 处理支付成功逻辑
            UserDetail userDetail = orderService.handlePayNotice(cmd.getDs(), userOrder.getOutTradeNo(), PayType.APPLE.getCode());
            if (userDetail != null) {
                initUserRights(cmd.getDs(), userDetail);
                log.info("[苹果支付验签] 支付处理成功, outTradeNo: {}, userDetail: {}",
                        userOrder.getOutTradeNo(), JSONUtil.toJsonStr(userDetail));
            }

            // 7. 创建订阅信息
            if (Objects.equals(userOrder.getIsSubscribe(), Constant.IsSubscribe.YES)) {
                // 查询商品信息
                ProductDoMain queryProduct = new ProductDoMain();
                queryProduct.setProductname(userOrder.getProductName());
                ProductDoMain dbProduct = orderService.getProductInfo(
                        cmd.getDs(), queryProduct, userOrder.getAppid(), userOrder.getOsid(), com.getBase().getLg(), com.getBase().getDf());
                if (dbProduct != null && dbProduct.getIssubscribe().intValue() == Constant.IsSubscribe.YES.intValue()) {
                    PaySubscribe paySubscribe = orderService.initSubscribeOrder(dbProduct, userOrder, payConfig);
                    paySubscribe.setConfigvalues(JSONUtil.toJsonStr(receipt));
                    if (StringUtils.hasLength(receipt.getOriginalTransactionId())) {
                        paySubscribe.setFirstouttradeno(receipt.getOriginalTransactionId());
                    }
                    orderService.createSubscribeOrder(cmd.getDs(), paySubscribe);
                }
            }
            log.info("[苹果支付验签] 验证成功, outTradeNo: {}", userOrder.getOutTradeNo());
            return CommonResult.success(null);
        } catch (Exception e) {
            log.error("[苹果支付验签] 验证异常", e);
            throw new MyBaseException(BaseErrorCodeEnum.PAY_ERROR.getCode(), "苹果支付验证异常");
        }
    }

    /**
     * 验证苹果支付收据
     */
    private AppleReceiptResult verifyApplePayReceipt(ApplePayVerifyRequest verifyRequest, PayConfig payConfig) {
        try {
            // 1. 调用苹果平台验证收据（获取详细对象）
            AppleReceiptResult detail = appleUtilService.verifyAppleReceiptWithPlatformDetail(verifyRequest, payConfig.getApikey());
            if (detail == null || !detail.isSuccess()) {
                log.error("[苹果支付验签] 苹果平台验证失败, status={} sandbox={}",
                        detail != null ? detail.getStatus() : null,
                        detail != null && detail.isSandbox());
                return null;
            }

            log.info("[苹果支付验签] 收据验证成功, productId={}, transactionId={}, bundleId={}, sandbox={}",
                    detail.getProductId(), detail.getTransactionId(), detail.getBundleId(), detail.isSandbox());
            return detail;
        } catch (Exception e) {
            log.error("[苹果支付验签] 收据验证异常", e);
            return null;
        }
    }


    /**
     * 调用权益新增
     *
     * @param pid        业务ID
     * @param userDetail 用户信息
     */
    private void initUserRights(String pid, UserDetail userDetail) {
        try {
            BuyProductRequest buyRequest = new BuyProductRequest();
            buyRequest.setProductid(userDetail.getProductid());
            CmdRequest cmd = new CmdRequest("", "", userDetail.getDf(), "", pid);
            ComRequest comRequest = new ComRequest();
            BaseRequest baseRequest = new BaseRequest();
            baseRequest.setUserid(userDetail.getUserid() + "");
            baseRequest.setOsid(userDetail.getOsid());
            comRequest.setBase(baseRequest);
            CommonResult<UserRightResponse> result = productService.buyProduct(buyRequest, cmd, comRequest);
            if (result.isSuccess()) {
                log.info("Successfully processed product purchase for pid: {} user: {}, product: {}", pid, userDetail.getUserid(), userDetail.getProductid());
            } else {
                log.error("Failed to process product purchase for pid: {}, user: {}, product: {}, result: {}", pid,
                        userDetail.getUserid(), userDetail.getProductid(), result.getMsg());
            }
        } catch (Exception e) {
            log.error("Failed to process product purchase for pid: {}, Detail: {}", pid, JSONUtil.toJsonStr(userDetail));
        }
    }

    /**
     * 验证苹果支付回调
     */
    @DS("#pid")
    public JSONObject verifyApplePayNotify(String pid, String appId, String requestBody) throws Exception {
        JSONObject notifyData = JSONUtil.parseObj(requestBody);
        String signedPayload = notifyData.getStr("signedPayload");
        if (!StringUtils.hasLength(signedPayload)) {
            log.error("[苹果支付回调] signedPayload为空");
            return null;
        }
        // 查询支付配置
        PayConfig payConfig = payMapper.getPayChannelConfig(appId, PayType.APPLE.getCode());
        if (payConfig == null) {
            log.error("[苹果支付验签] 支付配置不存在, appId: {} payType: {}", appId, PayType.APPLE.getCode());
            return null;
        }
        AppleReceiptResult receiptResult = appleUtilService.verifyAppleNotice(signedPayload);


        // 验证证书和签名
        return null;
    }

    /**
     * 回调验签
     * 基于相同字段与顺序规则重建签名并对比。
     */
    @DS("#pid")
    public boolean verifyMiPayNotify(String pid, Map<String, String> params) {
        try {
            if (params == null || params.isEmpty()) {
                log.warn("[MiNotify] 空参数, pid={}", pid);
                return false;
            }
            String appId = params.get("appId");
            log.info("[MiNotify] 开始验签, pid={}, appId={}, keys={}", pid, appId, params.keySet());

            PayConfig payConfig = payMapper.getPayChannelConfig(appId, PayType.MI.getCode());
            if (payConfig == null) {
                payConfig = payMapper.getPayChannelConfig(Constant.DEFAULT, PayType.MI.getCode());
            }
            if (payConfig == null) {
                log.error("[MiNotify] 未找到支付配置, appId={}", appId);
                return false;
            }
            String providedSign = params.get("signature");
            Map<String, String> signParams = new HashMap<>(params);
            signParams.remove("signature");
            String calc = signParamsHmacSha256(signParams, payConfig.getPrivatekey());
            boolean ok = Objects.equals(providedSign, calc);
            log.info("[MiNotify] 验签结果={}, providedSign(hash)={}, calc(hash)={}", ok, providedSign, calc);
            return ok;
        } catch (Exception e) {
            log.error("[MiNotify] 验签异常, pid={}", pid, e);
            return false;
        }
    }

    /**
     * 提取业务订单号，兼容多种字段命名。
     */
    public String getOutTradeNoFromNotify(Map<String, String> params) {
        if (params == null) return null;
        String v = params.get("cpOrderId");
        if (v == null || v.isEmpty()) v = params.get("outTradeNo");
        if (v == null || v.isEmpty()) v = params.get("orderId");
        log.info("[MiNotify] 解析订单号, outTradeNo={}", v);
        return v;
    }

    /**
     * 使用 HMAC-SHA256 对参数进行签名。参数需先按 key 升序拼接为 query 字符串。
     */
    private String signParamsHmacSha256(Map<String, String> params, String appSecret) throws Exception {
        SortedMap<String, String> sorted = new TreeMap<>();
        for (Map.Entry<String, String> e : params.entrySet()) {
            if (e.getValue() != null && !e.getValue().isEmpty()) {
                sorted.put(e.getKey(), e.getValue());
            }
        }
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> e : sorted.entrySet()) {
            sb.append(e.getKey()).append('=').append(e.getValue()).append('&');
        }
        if (!sb.isEmpty()) {
            sb.setLength(sb.length() - 1);
        }
        String base = sb.toString();
        log.debug("[MiSign] 待签名串(len={})", base.length());
        return HmacSHA1Encryption.HmacSHA1Encrypt(base, appSecret);
    }

    /**
     * 支付订阅列表
     */
    @DS("#cmd.ds")
    public CommonResult<?> paymentSubscribeList(CmdRequest cmd, ComRequest com, PaySubscribeRequest subscribeRequest) {
        return CommonResult.success(payMapper.paymentSubscribeList(Integer.valueOf(com.getBase().getUserid()), subscribeRequest.getPlatformcode()));
    }


    /**
     * 支付订阅取消
     */
    public CommonResult<?> paymentSubscribeUnsign(CmdRequest cmd, ComRequest com, PaySubscribeUnSignRequest subscribeRequest) {
        if (subscribeRequest.getPlatformcode().equals(PayType.ALI.getCode())) {
            return aliPayService.aliSubscribeUnSign(cmd, com, subscribeRequest);
        }
        return CommonResult.success(null);
    }
}