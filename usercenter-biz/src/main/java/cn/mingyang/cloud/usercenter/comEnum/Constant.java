package cn.mingyang.cloud.usercenter.comEnum;

import java.util.List;

public class Constant {
    public class SellType {
        /*
         * 1:coins,
         * 2:numbercharging,
         * 3:timecharging,
         */
        public static final int COINS = 1; //金币
        public static final int NUMBERCHARGING = 2;//按数量计费类型
        public static final int TIMESCHARGING = 3;//按次计费类型
    }

    public class ResetType {
        public static final int FREE = 0; //重置免费权益
        public static final int OIL = 1;//不重置加油包
        public static final int RESETOIL = 2;//可重置加油包
        public static final int VIPRESET = 3;
        public static final int SVIPRESET = 4;
    }

    public class RightType {
        public static final int NOLIMIT = 0;//非会员可用
        public static final int VIP = 1;//普通VIP可用
        public static final int SVIP = 2;//仅SVIP可用
        public static final int OIL = 3;//仅加油包可用
        public static final int VIPOIL = 4;//VIP和加油包可用
        public static final int SVIPOIL = 5;//SVIP和加油包可用
    }

    public class VIPType {
        public static final int NOTVIP = 1;
        public static final int VIP = 2;
        public static final int SVIP = 3;
    }

    public class ProductType {
        public static final int VIP = 101;
        public static final int SVIP = 102;
        public static final int OIL = 103;
    }

    /**
     * 权益消费状态码
     */
    public class ExpendRightsStatus {
        // 非会员，试用次数不足
        public static final int NO_VIP = 1;
        // 会员权益不足
        public static final int VIP_QUALITY = 2;
        // 会员加油包不足
        public static final int VIP_QUALITY_OIL = 3;
    }

    /**
     * 权益同步标识
     */
    public class SyncRightsFlag {
        // 转移
        public static final String TRANSFER = "transfer";
        // 同步
        public static final String SYNC = "sync";
    }

    /**
     * 编辑权益类型
     */
    public class EditRightsFlag {
        // 加油包
        public static final Integer OIL = 1;
        // 循环资源包
        public static final Integer QUALITY = 2;
    }

    /**
     * 会员权益流水标识
     */
    public class AddRightsFlag {
        // 注册会员赠送
        public static final Integer ADD_GIFT = 101;
        // 购买VIP赠送
        public static final Integer ADD_VIP = 102;
        // 购买SVIP赠送
        public static final Integer ADD_SVIP = 103;
        // 客服调整
        public static final Integer ADD_ERP = 104;
        // 购买加油包
        public static final Integer ADD_OIL = 105;
        // 消耗
        public static final Integer EXPEND = 201;
    }

    /**
     * 所有数据源
     */
    public class AllDs {
        // 推文
        public static final String AITW = "aitw";
        // PPT
        public static final String PPT = "ppt";
        // gol
        public static final String GOL = "gol";
        // 写作
        public static final String MIX = "mix";
        // 配音
        public static final String IFLYTTS = "iflytts";
        // ALL
        public static final List<String> ALL = List.of(AITW, PPT, GOL, MIX, IFLYTTS);
    }

    /**
     * 权益流水查询标识
     */
    public class RecordFlag {
        // 增加
        public static final String ADD = "add";
        // 消耗
        public static final String EXPEND = "expend";

    }

    /**
     * 签约状态 0签约中 1签约 2取消 3其他
     */
    public class SignStatus {
        // 0签约中
        public static final Integer SIGNING = 0;
        // 1签约
        public static final Integer SIGNED = 1;
        // 2取消
        public static final Integer CANCEL = 2;
        // 3其他
        public static final Integer OTHER = 3;
    }

    /**
     * 支付宝签约状态
     */
    public class AliSignStatus {
        // 签约
        public static final String SIGNED = "NORMAL";
        // 签约
        public static final String CANCEL = "UNSIGN";
    }

    /**
     * 是否可重试 0不可 1可
     */
    public class Retry {
        public static final Integer YES = 1;
        public static final Integer NO = 0;
    }

    /**
     * 支付状态 1未支付 2已支付 3支付失败
     */
    public class PayStatus {
        // 1未支付
        public static final Integer UNPAID = 1;
        // 2已支付
        public static final Integer PAID = 2;
        // 3支付失败
        public static final Integer FAILED = 3;
    }

    /**
     * 是否订阅 0否 1是
     */
    public class IsSubscribe {
        // 0 否
        public static final Integer NO = 0;
        // 1 是
        public static final Integer YES = 1;

    }

    /**
     * 语言类型
     */
    public class LanguageType {
        // 中文简体
        public static final String ZH_HANS = "zh_hans";
        // 英文
        public static final String EN = "en";
        // 中文繁体
        public static final String ZH_HANT = "zh_hant";
    }

    /**
     * 币种
     */
    public class CurrencyType {
        // 人民币
        public static final String CNY = "CNY";
        // 美元
        public static final String USD = "USD";
    }


    public static final String DEFAULT = "default";
}
