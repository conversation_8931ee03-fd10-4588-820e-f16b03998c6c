package cn.mingyang.cloud.usercenter.request;


import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 发送短信验证码
 */
@Data
public class ThirdPartyLoginRequest {

    /**
     * weixin：微信登录、SMS：手机验证码登录
     */
    @NotBlank(message = "登录类型：authtype不能为空")
    private String authtype;

    /**
     * 授权昵称。weixin：昵称，如：等一哈，SMS：手机号码 + 公司代号，如：18812345432boli
     */
    @NotBlank(message = "授权昵称：authname不能为空")
    private String authname;

    /**
     * 授权 unionid。weixin：微信 unionid，SMS：手机号码 + 公司代号，如：18812345432boli
     */
    @NotBlank(message = "授权ID：authunionid不能为空")
    private String authunionid;

    /**
     * 授权密钥。weixin：微信 unionid + 公司代号、SMS：手机号 + appid
     */
    @NotBlank(message = "授权密钥：authpassword不能为空")
    private String authpassword;

    /**
     * 头像
     */
    private String photo;


}
