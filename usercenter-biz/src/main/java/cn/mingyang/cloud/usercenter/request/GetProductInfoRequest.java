package cn.mingyang.cloud.usercenter.request;


import lombok.Data;

import javax.validation.constraints.NotNull;


/**
 * 查询应用商品信息列表请求
 */
@Data
public class GetProductInfoRequest {

    /**
     * 接口版本，默认 “0”，多版本控制商品显示
     */
    @NotNull(message = "接口版本：apiversion不能为空")
    private Integer apiversion;

    /**
     * 商品类型，详情见商品类型枚举
     */
    private Integer producttype;


    /**
     * 获取缓存Key
     *
     * @return 字符串
     */
    public String getCacheKey() {
        String cacheKey = "";
        if (null != apiversion) {
            cacheKey += apiversion;
        }
        if (null != producttype) {
            cacheKey += ":" + producttype;
        }
        return cacheKey;
    }

}
