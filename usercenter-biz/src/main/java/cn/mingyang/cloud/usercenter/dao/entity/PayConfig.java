package cn.mingyang.cloud.usercenter.dao.entity;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 支付渠道配置
 */
@Data
public class PayConfig {

    /**
     * ID
     */
    private Long id;

    /**
     * 渠道编码 (ali/wx)
     */
    private String platformcode;

    /**
     * 应用ID (支付宝/微信等通用)
     */
    private String platformappid;

    /**
     * 商户号 (微信等需要商户号的渠道)
     */
    private String mchid;

    /**
     * API密钥
     */
    private String apikey;

    /**
     * 卖家ID (支付宝专用字段)
     */
    private String sellerid;

    /**
     * 私有秘钥
     */
    private String privatekey;

    /**
     * 通知地址
     */
    private String notifyurl;

    /**
     * 公有秘钥
     */
    private String publickey;

    /**
     * 其他扩展配置 (如证书路径、异步通知URL等)
     */
    private String configvalues;

    /**
     * 创建时间
     */
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    private LocalDateTime updatetime;

    /**
     * 是否生效 (0否 1是)
     */
    private Integer valid;

    /**
     * 支付版本号（v2/v3）
     */
    private String version;
}
