package cn.mingyang.cloud.usercenter.dao.domain;

import lombok.Data;

/**
 * 通联支付请求参数
 */
@Data
public class UnionPayRequest {

    /**
     * 集团/代理商商户号
     */
    private String orgid;

    /**
     * 商户号
     */
    private String cusid;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 版本号
     */
    private String version = "11";

    /**
     * 交易金额（单位为分）
     */
    private String trxamt;

    /**
     * 商户交易单号
     */
    private String reqsn;

    /**
     * 唯一订单号
     */
    private String unireqsn;

    /**
     * 交易方式
     */
    private String paytype;

    /**
     * 随机字符串
     */
    private String randomstr;

    /**
     * 订单标题
     */
    private String body;

    /**
     * 备注
     */
    private String remark;

    /**
     * 有效时间（分钟）
     */
    private String validtime;

    /**
     * 绝对时间
     */
    private String expiretime;

    /**
     * 支付平台用户标识
     */
    private String acct;

    /**
     * 交易结果通知地址
     */
    private String notify_url;

    /**
     * 支付限制
     */
    private String limit_pay;

    /**
     * 微信子appid
     */
    private String sub_appid;

    /**
     * 订单优惠标识
     */
    private String goods_tag;

    /**
     * 优惠信息
     */
    private String benefitdetail;

    /**
     * 渠道门店编号
     */
    private String chnlstoreid;

    /**
     * 门店号
     */
    private String subbranch;

    /**
     * 拓展参数
     */
    private String extendparams;

    /**
     * 终端ip
     */
    private String cusip;

    /**
     * 支付完成跳转
     */
    private String front_url;

    /**
     * 证件号
     */
    private String idno;

    /**
     * 付款人真实姓名
     */
    private String truename;

    /**
     * 分账信息
     */
    private String asinfo;

    /**
     * 分期
     */
    private String fqnum;

    /**
     * 签名方式
     */
    private String signtype = "RSA";

    /**
     * 银联pid
     */
    private String unpid;

    /**
     * 金融机构号
     */
    private String finorg;

    /**
     * 收银员号
     */
    private String operatorid;

    /**
     * 签名
     */
    private String sign;

    /**
     * 授权码（条码支付使用）
     */
    private String authcode;
}
