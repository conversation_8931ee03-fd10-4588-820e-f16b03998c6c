package cn.mingyang.cloud.usercenter.comEnum;

import lombok.Getter;

/**
 * 支付类型明细枚举
 */
@Getter
public enum PayTypeDetail {
    //微信支付
    WX_BAR("WX_BAR", "微信条码"),
    WX_JSAPI("WX_JSAPI", "微信公众号"),
    WX_LITE("WX_LITE", "微信小程序"),
    WX_APP("WX_APP", "微信 APP"),
    WX_H5("WX_H5", "微信 H5"),
    WX_NATIVE("WX_NATIVE", "微信扫码"),
    WX_REFUNDS("WX_REFUNDS", "微信订单退款"),
    //阿里支付
    ALI_BAR("ALI_BAR", "支付宝条码"),
    ALI_JSAPI("ALI_JSAPI", "支付宝生活号"),
    ALI_LITE("ALI_LITE", "支付宝小程序"),
    ALI_APP("ALI_APP", "支付宝生活号"),
    ALI_WAP("ALI_WAP", "支付宝WAP"),
    ALI_PC("ALI_PC", "支付宝PC网站"),
    ALI_QR("ALI_QR", "支付宝二维码"),
    ALI_SUBSCRIBE("ALI_SUBSCRIBE", "支付宝代扣支付");

    private final String code;

    private final String desc;


    PayTypeDetail(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PayTypeDetail fromCode(String code) {
        for (PayTypeDetail type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

}
