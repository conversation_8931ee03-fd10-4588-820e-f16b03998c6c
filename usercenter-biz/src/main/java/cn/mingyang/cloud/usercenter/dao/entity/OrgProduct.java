package cn.mingyang.cloud.usercenter.dao.entity;

import lombok.Data;

/**
 * 原产品表
 */
@Data
public class OrgProduct {

    /**
     * 产品ID，主键，自增
     */
    private Integer id;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品类型（不能为空）
     */
    private String productType;

    /**
     * 产品价格
     */
    private Float price;

    /**
     * 促销价格
     */
    private Float promotionPrice;

    /**
     * 产品数量
     */
    private Integer quantity;

    /**
     * 操作系统ID
     */
    private String osid;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 产品标题
     */
    private String title;

    /**
     * 产品版本，默认为0
     */
    private String version;

    /**
     * 渠道
     */
    private String df;

    /**
     * 语言
     */
    private String lg;

    /**
     * 产品图片URL
     */
    private String productImg;

    /**
     * 是否订阅 0-未订阅 1-订阅自动续费，默认为0
     */
    private Integer isSubscribe;

    /**
     * 订阅续费价格，默认为0.00
     */
    private Float subscribePrice;

    /**
     * 副标题
     */
    private String subtitle;

    /**
     * 排序值，越小越排前，默认为0
     */
    private Integer orderNum;

    /**
     * 限制类型
     */
    private String limittype;

    /**
     * AI限制
     */
    private String ailimit;

    /**
     * 原始限制
     */
    private String originallimit;

    /**
     * 高级限制
     */
    private String seniorlimit;

    /**
     * 应用特有限制
     */
    private String applimit;

    /**
     * 币种，默认为空字符串
     */
    private String currency;

    /**
     * 视频时长，默认为0
     */
    private Integer videoDuration;

    /**
     * 备注信息
     */
    private String note;

    /**
     * 会员AI绘画次数，默认为0
     */
    private Integer aiimgNum;

    /**
     * 套餐量(加油包)，默认为0
     */
    private Integer tcQuantity;

    /**
     * 赠送量(加油包)，默认为0
     */
    private Integer giftQuantity;

    /**
     * 套餐绘画张数(加油包)，默认为0
     */
    private Integer tcAiimgNum;

    /**
     * 套餐赠送绘画张数(加油包)，默认为0
     */
    private Integer giftAiimgNum;
}
