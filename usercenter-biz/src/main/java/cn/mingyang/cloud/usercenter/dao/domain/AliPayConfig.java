package cn.mingyang.cloud.usercenter.dao.domain;

import lombok.Data;

/**
 * 阿里支付内容
 */
@Data
public class AliPayConfig {

    /**
     * 商家和支付宝签约的产品码
     */
    private String productCode;

    /**
     * 代扣签约
     */
    private Subscribe subscribe;

    /**
     * 返回地址
     */
    private String returnUrl;

    /**
     * 是否沙箱环境
     */
    private Boolean isSandbox;

    public Boolean getSandbox() {
        if (null == isSandbox) {
            return false;
        }
        return isSandbox;
    }


    /**
     * 签约对象
     */
    @Data
    public static class Subscribe {
        /**
         * 商家和支付宝签约的产品码。 商家扣款产品传入固定值：GENERAL_WITHHOLDING
         */
        private String productCode;

        /**
         * 个人签约产品码，商户和支付宝签约时确定
         */
        private String personalProductCode;

        /**
         * 签约场景
         */
        private String signScene;

        /**
         * 处理参数
         */
        private AccessParams accessParams;

        /**
         * 签约通知地址
         */
        private String signNotifyUrl;
    }

    /**
     * 签约处理参数
     */
    @Data
    public static class AccessParams {

        /**
         * 渠道
         */
        private String channel;
    }

}
