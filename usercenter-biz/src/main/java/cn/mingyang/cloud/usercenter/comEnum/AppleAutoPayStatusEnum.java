package cn.mingyang.cloud.usercenter.comEnum;

/**
 * 苹果支付状态
 */
public enum AppleAutoPayStatusEnum {

    /**
     * 苹果对自动订阅做出了改变 https://developer.apple.com/documentation/storekit/in-app_purchase/enabling_server-to-server_notifications
     * <p>
     * https://developer.apple.com/documentation/appstoreservernotifications/notification_type
     */

    // INITIAL_BUY(1,"初始购买"),
    SUBSCRIBED(1, "初始购买"),
    NORMAL_RENEWAL(2, "正常续订"),
    CANCEL(3, "取消续订"),
    RENEWAL(4, "已过期订阅的自动续订成功"),//(DEPRECATED IN SANDBOX)
    INTERACTIVE_RENEWAL(5, "交互式续订"),
    DID_CHANGE_RENEWAL_PREF(6, "升降级"),
    DID_CHANGE_RENEWAL_STATUS(7, "自动续订状态改变"),

    DID_FAIL_TO_RENEW(8, "自动续订失败"),
    DID_RECOVER(9, "自动续订恢复"),
    DID_RENEW(10, "自动续订成功"),
    PRICE_INCREASE_CONSENT(11, "涨价后，自动续订失败"),
    EXPIRED(12, "取消订阅"),
    CONSUMPTION_REQUEST(13, "消费请求"),
    REFUND(20, "退款"),
    REFUND_DECLINED(21, "退款失败"),
    REFUND_REVERSED(22, "退款返回"),
    ONE_TIME_CHARGE(23, "一次性收费"),
    ;

    Integer status;
    String statusDesc;

    public Integer getStatusCode() {
        return status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    AppleAutoPayStatusEnum(Integer status, String statusDesc) {
        this.status = status;
        this.statusDesc = statusDesc;
    }

    public static AppleAutoPayStatusEnum getStatusEnum(Integer status) {
        for (AppleAutoPayStatusEnum iosStatusCodeEnum : AppleAutoPayStatusEnum.values()) {
            if (iosStatusCodeEnum.getStatusCode().intValue() == status.intValue()) {
                return iosStatusCodeEnum;
            }
        }
        return null;
    }


}
