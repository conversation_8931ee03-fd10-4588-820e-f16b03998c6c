package cn.mingyang.cloud.usercenter;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 描述：用户中心服务
 *
 * <AUTHOR>
 * @date 2023-02-28
 */
@SpringBootApplication(scanBasePackages = "cn.mingyang.cloud")
@MapperScan("cn.mingyang.cloud.usercenter.dao.mapper")
@Slf4j
@EnableScheduling
@EnableFeignClients
@EnableAsync
public class UserCenterApplication {

    public static void main(String[] args)  throws UnknownHostException {
        //SpringApplication.run(UsercenterApplication.class, args);
        ConfigurableApplicationContext application = SpringApplication.run(UserCenterApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        log.info("\n----------------------------------------------------------\n\t" +
                "Application Chat is running! Access URLs:\n\t" +
                "Local: \t\thttp://localhost:" + port +  "/\n\t" +
                "External: \thttp://" + ip + ":" + port  + "/\n\t" +
                "Swagger文档: \thttp://" + ip + ":" + port  + "/doc.html\n" +
                "----------------------------------------------------------");
    }

}
