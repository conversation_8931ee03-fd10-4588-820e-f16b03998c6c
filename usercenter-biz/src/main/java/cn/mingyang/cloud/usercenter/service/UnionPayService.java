package cn.mingyang.cloud.usercenter.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.exception.MyBaseException;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.center.common.comEnum.BaseErrorCodeEnum;
import cn.mingyang.cloud.usercenter.comEnum.PayType;
import cn.mingyang.cloud.usercenter.comEnum.UnionPayEnum;
import cn.mingyang.cloud.usercenter.dao.domain.PayAmount;
import cn.mingyang.cloud.usercenter.dao.domain.UnionPayRequest;
import cn.mingyang.cloud.usercenter.dao.domain.UnionPayResponse;
import cn.mingyang.cloud.usercenter.dao.entity.OrgUserOrder;
import cn.mingyang.cloud.usercenter.dao.entity.PayConfig;
import cn.mingyang.cloud.usercenter.dao.mapper.PayMapper;
import cn.mingyang.cloud.usercenter.request.PayOrderRequest;
import cn.mingyang.cloud.usercenter.response.PayOrderResponse;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

/**
 * 通联支付Service
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class UnionPayService {

    // ==================== 常量定义 ====================

    /**
     * 生产环境API地址
     */
    private static final String PROD_API_URL = "https://vsp.allinpay.com/apiweb/unitorder/pay";

    /**
     * 测试环境API地址
     */
    private static final String TEST_API_URL = "https://syb-test.allinpay.com/apiweb/unitorder/pay";

    /**
     * 默认订单有效期（分钟）
     */
    private static final String DEFAULT_VALID_TIME = "30";

    /**
     * 默认签名类型
     */
    private static final String DEFAULT_SIGN_TYPE = "RSA";

    /**
     * 签名算法
     */
    private static final String RSA_SIGNATURE_ALGORITHM = "SHA1WithRSA";

    /**
     * RSA密钥算法
     */
    private static final String RSA_KEY_ALGORITHM = "RSA";

    // ==================== 依赖注入 ====================

    private final PayMapper payMapper;
    private final OrderService orderService;
    private final CloseableHttpClient httpClient;

    public UnionPayService(PayMapper payMapper, OrderService orderService, CloseableHttpClient httpClient) {
        this.payMapper = payMapper;
        this.orderService = orderService;
        this.httpClient = httpClient;
    }

    /**
     * 判断是否支持通联支付
     */
    @DS("#cmd.ds")
    public boolean checkUnionPay(CmdRequest cmd) {
        try {
            PayConfig payConfig = payMapper.getPayChannelConfig(cmd.getAppId(), PayType.TL.getCode());
            if (payConfig == null) {
                log.info("应用 {} 未配置通联支付渠道", cmd.getAppId());
                return false;
            }
            return true;
        } catch (Exception e) {
            log.error("检查通联支付配置异常, appId: {}", cmd.getAppId(), e);
            return false;
        }
    }

    /**
     * 通联支付统一下单
     */
    @DS("#cmd.ds")
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<?> unionUnifiedOrder(CmdRequest cmd, ComRequest com, PayOrderRequest payRequest) {
        String appId = cmd.getAppId();
        log.info("通联支付统一下单开始, appId: {}, paycode: {}", appId, payRequest.getPaycode());

        try {
            // 验证支付方式
            UnionPayEnum unionPayEnum = UnionPayEnum.unionPayForwardWxOrAli(payRequest.getPaycode());
            if (unionPayEnum == null) {
                return handleError("不支持的支付方式: " + payRequest.getPaycode(), null, null);
            }
            // 获取支付配置
            PayConfig payConfig = payMapper.getPayChannelConfig(appId, PayType.TL.getCode());
            if (payConfig == null) {
                return handleError("未找到应用 " + appId + " 的通联支付渠道配置", null, null);
            }
            // 创建支付订单
            OrgUserOrder orgUserOrder = orderService.createPayOrder(
                    payRequest, cmd, com, PayType.TL.getIndex(), payConfig.getMchid());
            log.info("通联支付订单创建成功, outTradeNo: {}", orgUserOrder.getOutTradeNo());
            // 构建支付金额
            PayAmount amount = new PayAmount();
            amount.setCurrency("CNY");
            amount.setTotalYuan(orgUserOrder.getFee());
            // 根据支付方式执行相应的支付逻辑
            return switch (unionPayEnum) {
                case W01, A01 -> unionBarPay(payConfig, amount, orgUserOrder, payRequest, unionPayEnum);
                case W02, W06, A02 -> unionJsapiPay(payConfig, amount, orgUserOrder, payRequest, unionPayEnum);
                case W03, A03 -> unionAppPay(payConfig, amount, orgUserOrder, unionPayEnum);
                default -> handleError("不支持的通联支付方式: " + unionPayEnum.getDesc(),
                        orgUserOrder.getOutTradeNo(), null);
            };
        } catch (Exception e) {
            log.error("通联支付统一下单异常, appId: {}", appId, e);
            return handleError("通联支付下单失败: " + e.getMessage(), null, e);
        }
    }

    /**
     * 通联条码支付
     */
    private CommonResult<?> unionBarPay(PayConfig config,
                                        PayAmount amount,
                                        OrgUserOrder order,
                                        PayOrderRequest payRequest,
                                        UnionPayEnum unionPayEnum) {
        // 验证授权码
        if (StrUtil.isBlank(payRequest.getAuthcode())) {
            return handleError("条码支付缺少授权码参数", order.getOutTradeNo(), null);
        }
        // 构建请求参数
        UnionPayRequest request = buildBaseUnionPayRequest(config, amount, order, unionPayEnum.getCode());
        request.setAuthcode(payRequest.getAuthcode());
        log.info("执行通联条码支付, outTradeNo: {}", order.getOutTradeNo());
        return executeUnionPay(request, config, order, unionPayEnum.getDesc());
    }

    /**
     * 通联JS支付（公众号/小程序）
     */
    private CommonResult<?> unionJsapiPay(PayConfig config,
                                          PayAmount amount,
                                          OrgUserOrder order,
                                          PayOrderRequest payRequest,
                                          UnionPayEnum unionPayEnum) {
        // 验证openid
        if (StrUtil.isBlank(payRequest.getOpenid())) {
            return handleError("JS支付缺少用户openid参数", order.getOutTradeNo(), null);
        }
        // 构建请求参数
        UnionPayRequest request = buildBaseUnionPayRequest(config, amount, order, unionPayEnum.getCode());
        request.setAcct(payRequest.getOpenid());
        log.info("执行通联JS支付, outTradeNo: {}", order.getOutTradeNo());
        return executeUnionPay(request, config, order, unionPayEnum.getDesc());
    }

    /**
     * 通联APP支付
     */
    private CommonResult<?> unionAppPay(PayConfig config,
                                        PayAmount amount,
                                        OrgUserOrder order,
                                        UnionPayEnum unionPayEnum) {
        // 构建请求参数
        UnionPayRequest request = buildBaseUnionPayRequest(config, amount, order, unionPayEnum.getCode());
        log.info("执行通联APP支付, outTradeNo: {}", order.getOutTradeNo());
        return executeUnionPay(request, config, order, unionPayEnum.getDesc());
    }


    /**
     * 构建通联支付基础请求参数
     */
    private UnionPayRequest buildBaseUnionPayRequest(PayConfig config,
                                                     PayAmount amount,
                                                     OrgUserOrder order,
                                                     String paytype) {
        UnionPayRequest request = new UnionPayRequest();
        // 解析扩展配置
        JSONObject unionConfig = new JSONObject();
        if (StrUtil.isNotBlank(config.getConfigvalues())) {
            unionConfig = JSONUtil.parseObj(config.getConfigvalues());
        }
        // 设置必填参数
        request.setCusid(config.getMchid());
        request.setAppid(config.getPlatformappid());
        request.setPaytype(paytype);
        request.setTrxamt(String.valueOf(amount.getTotalFen()));
        request.setReqsn(order.getOutTradeNo());
        request.setRandomstr(generateRandomStr());
        request.setBody(order.getTitle());
        request.setNotify_url(config.getNotifyurl());
        request.setValidtime(DEFAULT_VALID_TIME);
        request.setSigntype(DEFAULT_SIGN_TYPE);
        request.setRemark("通联支付， 商品名称：" + order.getTitle());
        // 设置可选参数
        if (unionConfig.containsKey("orgid")) {
            request.setOrgid(unionConfig.getStr("orgid"));
        }
        if (unionConfig.containsKey("sub_appid")) {
            request.setSub_appid(unionConfig.getStr("sub_appid"));
        }
        if (unionConfig.containsKey("front_url")) {
            request.setFront_url(unionConfig.getStr("front_url"));
        }
        return request;
    }

    /**
     * 执行通联支付请求
     */
    private CommonResult<?> executeUnionPay(UnionPayRequest request,
                                            PayConfig config,
                                            OrgUserOrder order,
                                            String payMethod) {
        String outTradeNo = order.getOutTradeNo();

        try {
            // 转换请求参数并生成签名
            Map<String, String> params = convertToMap(request);
            String sign = generateSign(params, config.getPrivatekey(), request.getSigntype());
            params.put("sign", sign);

            // 确定API地址
            JSONObject tlConfig = new JSONObject();
            if (StrUtil.isNotBlank(config.getConfigvalues())) {
                tlConfig = JSONUtil.parseObj(config.getConfigvalues());
            }
            boolean isTest = tlConfig.getBool("isTest", false);
            String apiUrl = isTest ? TEST_API_URL : PROD_API_URL;
            log.info("发送通联支付请求, outTradeNo: {}, url: {} params: {}", outTradeNo, apiUrl, JSONUtil.toJsonStr(params));
            // 发送HTTP请求 - 使用form表单方式
            HttpPost httpPost = new HttpPost(apiUrl);
            // 构建form表单参数
            List<NameValuePair> formParams = new ArrayList<>();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                formParams.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
            // 设置form表单实体
            UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(formParams, StandardCharsets.UTF_8);
            httpPost.setEntity(formEntity);
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
            String responseBody = httpClient.execute(httpPost,
                    response -> EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8));
            log.info("通联支付响应: {}", responseBody);
            // 参数非空验证
            if (StrUtil.isBlank(responseBody)) {
                log.error("通联支付响应为空, payMethod: {}", payMethod);
                return handleError("支付响应为空", null, null);
            }
            // 解析响应
            UnionPayResponse unionPayResponse;
            unionPayResponse = JSONUtil.toBean(responseBody, UnionPayResponse.class);
            // 处理响应结果
            return handleUnionPayResponse(unionPayResponse, order, payMethod);
        } catch (Exception e) {
            log.error("执行通联支付请求失败, outTradeNo: {}, payMethod: {}",
                    outTradeNo, payMethod, e);
            return handleError("支付请求执行失败: " + e.getMessage(), outTradeNo, e);
        }
    }

    /**
     * 处理通联支付响应
     */
    private CommonResult<?> handleUnionPayResponse(UnionPayResponse response,
                                                   OrgUserOrder order,
                                                   String payMethod) {
        String outTradeNo = order.getOutTradeNo();
        // 检查响应状态
        if (!response.isSuccess()) {
            String retcode = StrUtil.blankToDefault(response.getRetcode(), "UNKNOWN");
            String retmsg = StrUtil.blankToDefault(response.getRetmsg(), "未知错误");
            log.error("通联{}失败, outTradeNo: {}, retcode: {}, retmsg: {}",
                    payMethod, outTradeNo, retcode, retmsg);
            return handleError(retmsg, outTradeNo, null);
        }
        // 验证必要的响应字段
        if (StrUtil.isBlank(response.getPayinfo())) {
            log.error("通联支付响应缺少payinfo字段, outTradeNo: {}, payMethod: {}",
                    outTradeNo, payMethod);
            return handleError("支付信息缺失", outTradeNo, null);
        }
        // 解析支付信息
        JSONObject payInfo;
        payInfo = JSONUtil.parseObj(response.getPayinfo());
        if (payInfo.isEmpty()) {
            log.error("支付信息解析失败或为空, outTradeNo: {}, payinfo: {}",
                    outTradeNo, response.getPayinfo());
            return handleError("支付信息解析失败", outTradeNo, null);
        }
        // 构建成功响应
        PayOrderResponse payOrderResponse = buildPayOrderResponse(response, order, payInfo);
        log.info("通联{}成功, outTradeNo: {}, response: {}",
                payMethod, outTradeNo, JSONUtil.toJsonStr(payOrderResponse));
        return CommonResult.success(payOrderResponse);
    }

    /**
     * 构建支付订单响应对象
     */
    private PayOrderResponse buildPayOrderResponse(UnionPayResponse response,
                                                   OrgUserOrder order,
                                                   JSONObject payInfo) {
        PayOrderResponse payOrderResponse = new PayOrderResponse();
        // 设置基础信息
        payOrderResponse.setOuttradeno(order.getOutTradeNo());
        payOrderResponse.setMchId(StrUtil.blankToDefault(response.getCusid(), ""));
        payOrderResponse.setPlatformmchid(StrUtil.blankToDefault(response.getCusid(), ""));
        // 设置支付相关数据
        Map<String, String> payData = new HashMap<>();
        // 从payInfo中安全获取字段
        if (payInfo != null) {
            payOrderResponse.setPayAppId(payInfo.getStr("appId"));
            payOrderResponse.setPlatformappid(payInfo.getStr("appId"));
            payOrderResponse.setNonceStr(payInfo.getStr("nonceStr"));
            payOrderResponse.setRandomstr(payInfo.getStr("nonceStr"));
            payOrderResponse.setSign(payInfo.getStr("paySign"));
            payOrderResponse.setTimestamp(payInfo.getStr("timeStamp"));
            String signType = payInfo.getStr("signType");
            if (StrUtil.isNotBlank(signType)) {
                payData.put("signType", signType);
            }
            String prepayId = payInfo.getStr("package");
            if (StrUtil.isNotBlank(prepayId)) {
                payData.put("prepayId", prepayId);
            }
        }
        payOrderResponse.setPaydata(payData);
        return payOrderResponse;
    }

    /**
     * 通联支付回调验签
     */
    @DS("#pid")
    public boolean verifyUnionPayNotify(String pid, Map<String, String> params) {
        try {
            String appid = params.get("appid");
            if (StrUtil.isBlank(appid)) {
                log.error("通联支付回调缺少appid参数, pid: {}", pid);
                return false;
            }
            //通过appId和支付方式获取支付配置
            PayConfig payConfig = payMapper.getPayChannelConfigByPayChannel(appid, PayType.TL.getCode());
            if (payConfig == null) {
                log.error("通联支付回调未找到支付配置, pid: {}, appid: {}", pid, appid);
                return false;
            }
            String signType = params.getOrDefault("signtype", DEFAULT_SIGN_TYPE);
            boolean verifyResult = verifySign(params, payConfig.getPublickey(), signType);
            log.info("通联支付回调验签结果, pid: {}, appid: {}, result: {}", pid, appid, verifyResult);
            return verifyResult;
        } catch (Exception e) {
            log.error("通联支付回调验签异常, pid: {}", pid, e);
            return false;
        }
    }

    /**
     * 转换请求对象为Map
     */
    private Map<String, String> convertToMap(UnionPayRequest request) {
        Map<String, Object> objectMap = JSONUtil.parseObj(request);
        Map<String, String> stringMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : objectMap.entrySet()) {
            if (entry.getValue() != null) {
                stringMap.put(entry.getKey(), entry.getValue().toString());
            }
        }
        return stringMap;
    }


    /**
     * 统一错误处理
     */
    private CommonResult<?> handleError(String message, String outTradeNo, Exception e) {
        String errorMsg = StrUtil.isNotBlank(outTradeNo) ?
                String.format("通联支付错误[%s]: %s", outTradeNo, message) :
                String.format("通联支付错误: %s", message);
        if (e != null) {
            log.error(errorMsg, e);
        } else {
            log.error(errorMsg);
        }
        throw new MyBaseException(BaseErrorCodeEnum.PAY_ERROR.getCode(), errorMsg);
    }

    /**
     * 生成签名
     */
    private String generateSign(Map<String, String> params, String privateKey, String signType) {
        if (StrUtil.isBlank(privateKey)) {
            throw new IllegalArgumentException("私钥不能为空");
        }

        try {
            // 构建待签名字符串：过滤空值和sign参数，按key排序后拼接
            Map<String, String> filteredParams = new TreeMap<>();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                if (StrUtil.isNotBlank(value) && !"sign".equals(key)) {
                    filteredParams.put(key, value);
                }
            }

            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> entry : filteredParams.entrySet()) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            if (!sb.isEmpty()) {
                sb.setLength(sb.length() - 1); // 移除最后一个&
            }

            String signData = sb.toString();
            log.debug("待签名字符串: {}", signData);

            // 使用私钥签名
            return sign(signData, privateKey, signType);

        } catch (Exception e) {
            log.error("生成签名失败, signType: {}", signType, e);
            throw new RuntimeException("生成签名失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证签名
     */
    private boolean verifySign(Map<String, String> params, String publicKey, String signType) {
        if (StrUtil.isBlank(publicKey)) {
            log.error("验证签名失败: 公钥为空");
            return false;
        }
        try {
            String sign = params.get("sign");
            if (StrUtil.isBlank(sign)) {
                log.error("验证签名失败: 签名参数为空");
                return false;
            }
            // 构建待验签字符串（排除sign参数）
            TreeMap<String, String> filteredParams = new TreeMap<>();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();
                if (StrUtil.isNotBlank(value) && !"sign".equals(key)) {
                    filteredParams.put(key, value);
                }
            }
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> entry : filteredParams.entrySet()) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
            if (!sb.isEmpty()) {
                sb.setLength(sb.length() - 1);
            }
            String signData = sb.toString();
            log.debug("待验签字符串: {}", signData);
            // 执行验签
            return verify(signData, sign, publicKey, signType);
        } catch (Exception e) {
            log.error("验证签名异常, signType: {}", signType, e);
            return false;
        }
    }

    /**
     * RSA私钥签名
     */
    private String sign(String data, String privateKey, String signType) throws Exception {
        if ("SM2".equals(signType)) {
            throw new UnsupportedOperationException("SM2签名暂未实现，请使用RSA签名");
        }
        if (!DEFAULT_SIGN_TYPE.equals(signType)) {
            throw new IllegalArgumentException("不支持的签名类型: " + signType);
        }
        // 解析私钥
        byte[] keyBytes = Base64.getDecoder().decode(privateKey);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_KEY_ALGORITHM);
        PrivateKey priKey = keyFactory.generatePrivate(keySpec);
        // 执行签名
        Signature signature = Signature.getInstance(RSA_SIGNATURE_ALGORITHM);
        signature.initSign(priKey);
        signature.update(data.getBytes(StandardCharsets.UTF_8));
        byte[] signed = signature.sign();
        return Base64.getEncoder().encodeToString(signed);
    }

    /**
     * RSA公钥验签
     */
    private boolean verify(String data, String sign, String publicKey, String signType) throws Exception {
        if ("SM2".equals(signType)) {
            throw new UnsupportedOperationException("SM2验签暂未实现，请使用RSA验签");
        }
        if (!DEFAULT_SIGN_TYPE.equals(signType)) {
            throw new IllegalArgumentException("不支持的签名类型: " + signType);
        }
        // 解析公钥
        byte[] keyBytes = Base64.getDecoder().decode(publicKey);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_KEY_ALGORITHM);
        PublicKey pubKey = keyFactory.generatePublic(keySpec);
        // 执行验签
        Signature signature = Signature.getInstance(RSA_SIGNATURE_ALGORITHM);
        signature.initVerify(pubKey);
        signature.update(data.getBytes(StandardCharsets.UTF_8));
        log.info("通联支付回调验签结果, sign: {}, data: {}", sign, data);
        return signature.verify(Base64.getDecoder().decode(sign));
    }


    /**
     * 生成随机字符串
     */
    private String generateRandomStr() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 32);
    }
}
