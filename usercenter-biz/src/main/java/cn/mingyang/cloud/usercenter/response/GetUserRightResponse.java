package cn.mingyang.cloud.usercenter.response;


import cn.hutool.core.collection.CollectionUtil;
import cn.mingyang.cloud.center.common.util.DateTimeUtils;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import cn.mingyang.cloud.usercenter.dao.domain.RightItems;
import cn.mingyang.cloud.usercenter.dao.domain.UserRightsCache;
import cn.mingyang.cloud.usercenter.dao.entity.SellPointInfo;
import cn.mingyang.cloud.usercenter.dao.entity.UserRightInfo;
import com.alibaba.druid.util.StringUtils;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 用户权益信息返回
 */
@Data
public class GetUserRightResponse {

    /**
     * 用户id
     */
    private String userid;

    /**
     * 金币数
     */
    private float coins;

    /**
     * 权益金币明细
     */
    private List<RightDetail> coinsdetail;

    /**
     * 数量
     */
    private float number;

    /**
     * 权益数量明细
     */
    private List<RightDetail> numberdetail;

    /**
     * 次数
     */
    private float times;

    /**
     * 权益数量明细
     */
    private List<RightDetail> timesdetail;

    /**
     * vip 到期时间字符串
     */
    private String vipendtime;

    /**
     * vip 到期时间时间戳（秒）
     */
    private Long vipendtimeStamp;

    /**
     * 超级会员到期时间字符串
     */
    private String svipendtime;


    /**
     * 超级会员到期时间时间戳（秒）
     */
    private Long svipendtimeStamp;


    /**
     * 权益明细
     */
    @Data
    public static class RightDetail {

        /**
         * 卖点ID
         */
        private Integer sellgroup;

        /**
         * 卖点名称
         */
        private String sellname;

        /**
         * 免费数量
         */
        private Float free;

        /**
         * 剩余免费数量
         */
        private Float remainingfree;

        /**
         * VIP数量
         */
        private Float quantity;

        /**
         * 剩余会员权益
         */
        private Float remainingquantity;

        /**
         * SVIP数量
         */
        private Float svipquantity;

        /**
         * 重置单位（天）
         */
        private Integer resetunit;

        /**
         * 备注
         */
        private String note;

        /**
         * 重置日期
         */
        private String resettime;

        /**
         * 加油包
         */
        private Float oil;

    }

    /**
     * 统计金币数量
     *
     * @param coins 金币字符串
     * @return 金币数量
     */
    public float getCoins(String coins) {
        float coinSum = 0f;
        if (StringUtils.isEmpty(coins)) {
            return coinSum;
        }
        RightItems rightItems = new RightItems(coins);
        List<RightItems.RightItem> rightItemList = rightItems.getRightItems();
        if (CollectionUtil.isEmpty(rightItemList)) {
            return coinSum;
        }
        for (RightItems.RightItem item : rightItemList) {
            coinSum = coinSum + item.getF() + item.getQ() + item.getO();
        }
        return coinSum;
    }

    /**
     * 统计数量
     *
     * @param numbers 数量字符串
     * @return 数量
     */
    public float getNumber(String numbers) {
        float numberSum = 0f;
        if (StringUtils.isEmpty(numbers)) {
            return numberSum;
        }
        RightItems rightItems = new RightItems(numbers);
        List<RightItems.RightItem> rightItemList = rightItems.getRightItems();
        if (CollectionUtil.isEmpty(rightItemList)) {
            return numberSum;
        }
        for (RightItems.RightItem item : rightItemList) {
            numberSum = numberSum + item.getF() + item.getQ() + item.getO();
        }
        return numberSum;
    }

    /**
     * 统计次数
     *
     * @param timeStr 次数字符串
     * @return 次数
     */
    public float getTimes(String timeStr) {
        float numberSum = 0f;
        if (StringUtils.isEmpty(timeStr)) {
            return numberSum;
        }
        RightItems rightItems = new RightItems(timeStr);
        List<RightItems.RightItem> rightItemList = rightItems.getRightItems();
        if (CollectionUtil.isEmpty(rightItemList)) {
            return numberSum;
        }
        for (RightItems.RightItem item : rightItemList) {
            numberSum = numberSum + item.getF() + item.getQ() + item.getO();
        }
        return numberSum;
    }

    /**
     * 构建权益返回
     *
     * @param userRightInfo 用户权益
     * @return 权益信息
     */
    public static GetUserRightResponse buildUserRightResponse(UserRightInfo userRightInfo) {
        if (null == userRightInfo) {
            return null;
        }
        GetUserRightResponse rightResponse = new GetUserRightResponse();
        rightResponse.setCoins(rightResponse.getCoins(userRightInfo.getCoins()));
        rightResponse.setNumber(rightResponse.getNumber(userRightInfo.getNumbercharging()));
        rightResponse.setTimes(rightResponse.getTimes(userRightInfo.getTimescharging()));
        rightResponse.setVipendtime(userRightInfo.getVipendtime());
        rightResponse.setVipendtimeStamp(DateTimeUtils.dateStringToStamp(userRightInfo.getVipendtime()));
        rightResponse.setSvipendtime(userRightInfo.getSvipendtime());
        rightResponse.setSvipendtimeStamp(DateTimeUtils.dateStringToStamp(userRightInfo.getSvipendtime()));
        return rightResponse;
    }

    /**
     * 从缓存中构建权益缓存
     *
     * @param existCache 用户权益缓存
     * @return 权益信息
     */
    public static GetUserRightResponse buildUserRightResponseByCache(UserRightsCache existCache) {
        if (null == existCache) {
            return null;
        }
        GetUserRightResponse rightResponse = new GetUserRightResponse();
        rightResponse.setCoins(rightResponse.getCoins(existCache.getCoins()));
        rightResponse.setNumber(rightResponse.getNumber(existCache.getNumbercharging()));
        rightResponse.setTimes(rightResponse.getTimes(existCache.getTimescharging()));
        rightResponse.setVipendtime(existCache.getVipendtime());
        rightResponse.setSvipendtime(existCache.getSvipendtime());
        rightResponse.setVipendtimeStamp(DateTimeUtils.dateStringToStamp(existCache.getVipendtime()));
        rightResponse.setSvipendtimeStamp(DateTimeUtils.dateStringToStamp(existCache.getSvipendtime()));
        return rightResponse;
    }


    /**
     * 构建权益明细
     *
     * @param rightStr       权益字符串
     * @param sellPointInfos 卖点信息
     * @return 权益列表
     */
    public List<RightDetail> initRightsDetail(String rightStr, List<SellPointInfo> sellPointInfos) {
        List<RightDetail> result = new ArrayList<>();
        if (StringUtils.isEmpty(rightStr)) {
            return null;
        }
        RightItems rightItems = new RightItems(rightStr);
        List<RightItems.RightItem> rightItemList = rightItems.getRightItems();
        if (CollectionUtil.isEmpty(rightItemList)) {
            return null;
        }
        for (RightItems.RightItem item : rightItemList) {
            // 筛选出正常的资源
            Optional<SellPointInfo> sellPointInfo = sellPointInfos.stream()
                    .filter(x -> x.getSellgroup() == item.getI() && x.getResettype() == Constant.ResetType.VIPRESET).findFirst();
            RightDetail rtd = new RightDetail();
            rtd.setSellgroup(item.getI());
            if (sellPointInfo.isPresent()) {
                rtd.setSellname(sellPointInfo.get().getSellname());
                rtd.setFree(sellPointInfo.get().getFree());
                rtd.setQuantity(sellPointInfo.get().getQuantity());
                rtd.setSvipquantity(sellPointInfo.get().getSvipquantity());
                rtd.setResetunit(sellPointInfo.get().getResetunit());
                rtd.setNote(sellPointInfo.get().getNote());
            }
            rtd.setRemainingfree(item.getF());
            rtd.setRemainingquantity(item.getQ());
            rtd.setResettime(item.getT());
            rtd.setOil(item.getO());
            result.add(rtd);
        }
        return result;
    }


    /**
     * 从缓存中构建权益明细
     *
     * @param existCache     用户权益缓存
     * @param sellPointInfos 卖点信息
     * @return 权益信息
     */
    public static GetUserRightResponse buildUserRightDetailResponseByCache(UserRightsCache existCache, List<SellPointInfo> sellPointInfos) {
        if (null == existCache) {
            return null;
        }
        GetUserRightResponse rightResponse = new GetUserRightResponse();
        rightResponse.setCoins(rightResponse.getCoins(existCache.getCoins()));
        rightResponse.setCoinsdetail(rightResponse.initRightsDetail(existCache.getCoins(), sellPointInfos));
        rightResponse.setNumber(rightResponse.getNumber(existCache.getNumbercharging()));
        rightResponse.setNumberdetail(rightResponse.initRightsDetail(existCache.getNumbercharging(), sellPointInfos));
        rightResponse.setTimes(rightResponse.getTimes(existCache.getTimescharging()));
        rightResponse.setTimesdetail(rightResponse.initRightsDetail(existCache.getTimescharging(), sellPointInfos));
        rightResponse.setVipendtime(existCache.getVipendtime());
        rightResponse.setSvipendtime(existCache.getSvipendtime());
        rightResponse.setVipendtimeStamp(DateTimeUtils.dateStringToStamp(existCache.getVipendtime()));
        rightResponse.setSvipendtimeStamp(DateTimeUtils.dateStringToStamp(existCache.getSvipendtime()));
        return rightResponse;
    }


}
