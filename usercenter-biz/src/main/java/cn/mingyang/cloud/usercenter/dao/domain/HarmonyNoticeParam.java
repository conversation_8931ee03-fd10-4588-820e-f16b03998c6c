package cn.mingyang.cloud.usercenter.dao.domain;


import lombok.Data;


/**
 * 鸿蒙支付通知返回参数
 */
@Data
public class HarmonyNoticeParam {

    /**
     * 通知主类型
     * DID_NEW_TRANSACTION   订单已购买/订阅已购买/订阅续订成功
     * DID_CHANGE_RENEWAL_STATUS   订阅状态发生改变
     * REVOKE   订单退款/撤销订阅
     * RENEWAL_TIME_MODIFIED   订阅过期时间调整
     * EXPIRE   订阅已过期
     * TEST   测试服务端通知，仅开发者调用测试服务端通知接口才会发送此类型通知。此场景下无notificationSubtype
     */
    private String notificationType;

    /**
     * 通知子类型
     * INITIAL_BUY 消耗型/非消耗型/非续期订阅商品购买成功。自动续期订阅商品的第一次购买成功。使用主类型：DID_NEW_TRANSACTION
     * DID_RENEW 续期成功。使用主类型：DID_NEW_TRANSACTION
     * RESTORE 用户主动恢复了一个订阅型商品，续期恢复正常。使用主类型：DID_NEW_TRANSACTION
     * AUTO_RENEW_ENABLED 自动续期功能开启。使用主类型：DID_CHANGE_RENEWAL_STATUS
     * AUTO_RENEW_DISABLED 自动续期功能关闭。使用主类型：DID_CHANGE_RENEWAL_STATUS
     * DOWNGRADE 用户调整自动续期订阅商品降级或跨级且在下个续订生效。使用主类型：DID_CHANGE_RENEWAL_STATUS或DID_NEW_TRANSACTION
     * UPGRADE 用户调整自动续期订阅商品升级或跨级且立即生效。使用主类型：DID_NEW_TRANSACTION
     * REFUND_TRANSACTION 消耗型/非消耗型/非续期订阅商品订单退款成功。自动续期订阅商品订单退款成功。使用主类型：REVOKE
     * BILLING_RETRY 一个到期的自动续期订阅商品进入账号保留期。使用主类型：EXPIRE
     * PRICE_INCREASE 用户同意了涨价。使用主类型：DID_CHANGE_RENEWAL_STATUS
     * BILLING_RECOVERY  订阅重试扣费成功。使用主类型：DID_NEW_TRANSACTION
     * PRODUCT_NOT_FOR_SALE 商品不存在。使用主类型：EXPIRE
     * APPLICATION_DELETE_SUBSCRIPTION_HOSTING 撤销订阅成功，订阅权益会立即取消。使用主类型：REVOKE
     * RENEWAL_EXTENDED 延迟订阅续订日期成功，订阅的下一个续订日期将推迟。使用主类型：RENEWAL_TIME_MODIFIED
     */
    private String notificationSubtype;

    /**
     * 通知唯一请求ID
     */
    private String notificationRequestId;

    /**
     * 通知元数据
     */
    private NotificationMetaData notificationMetaData;

    /**
     * 通知版本：v3
     */
    private String notificationVersion;

    /**
     * 通知签名时间，UTC时间戳，以毫秒为单位
     */
    private Long signedTime;


    /**
     * 通知元数据
     */
    @Data
    public static class NotificationMetaData {
        /**
         * 环境类型（必选）。<br>
         * 可选值：<br>
         * NORMAL: 正式环境<br>
         * SANDBOX: 沙盒环境
         */
        private String environment;

        /**
         * 应用Id（必选）
         */
        private String applicationId;

        /**
         * 应用包名（必选）
         */
        private String packageName;

        /**
         * 商品类型（必选）。<br>
         * 可选值：<br>
         * 0: 消耗型商品<br>
         * 1: 非消耗型商品<br>
         * 2: 自动续期订阅商品<br>
         * 3: 非续期订阅商品
         */
        private Integer type;

        /**
         * 最近一个有效订阅的商品ID（可选）。仅自动续期订阅商品场景下存在值
         */
        private String currentProductId;

        /**
         * 订阅组ID（可选）。仅自动续期订阅商品场景下存在值
         */
        private String subGroupId;

        /**
         * 订阅组的代ID（可选）。<br>
         * 说明：<br>
         * - 用户切换订阅商品时，此ID不会改变。<br>
         * - 订阅失效且超出保留期后，用户重新购买商品时，此ID会改变。
         */
        private String subGroupGenerationId;

        /**
         * 商品的订阅ID（可选）。以下场景此ID会发生改变：<br>
         * - 用户切换订阅商品时。<br>
         * - 订阅失效且超出保留期后，用户重新购买商品时。
         */
        private String subscriptionId;

        /**
         * 商品的购买Token（必选），发起购买和查询订阅信息均会返回。最大长度256
         */
        private String purchaseToken;

        /**
         * 具体一笔订单中对应的购买订单号（可选）。当NotificationType为DID_CHANGE_RENEWAL_STATUS且NotificationSubtype为DOWNGRADE时不返回该字段。最大长度256
         */
        private String purchaseOrderId;
    }


}
