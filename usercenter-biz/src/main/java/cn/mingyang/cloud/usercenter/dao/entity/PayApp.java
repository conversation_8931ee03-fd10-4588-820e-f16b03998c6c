package cn.mingyang.cloud.usercenter.dao.entity;


import lombok.Data;

import java.time.LocalDateTime;


/**
 * 支付应用表
 */
@Data
public class PayApp {

    /**
     * 假设自增主键
     */
    private Long id;

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 应用名称
     */
    private String appname;

    /**
     * 配置元数据
     */
    private String configmetadata;

    /**
     * 创建时间
     */
    private LocalDateTime createtime;

    /**
     * 更新时间
     */
    private LocalDateTime updatetime;

    /**
     * 0: 否，1: 是（对应 tinyint 类型）
     */
    private Integer valid;
}
