package cn.mingyang.cloud.usercenter.dao.domain;


import lombok.Data;

/**
 * 用户信息
 */
@Data
public class UserDetail {

    /**
     * 应用ID
     */
    private String appid;

    /**
     * 用户id，可为空
     */
    private Integer userid;

    /**
     * 系统ID
     */
    private String osid;

    /**
     * 渠道
     */
    private String df;

    /**
     * 商品类型 101： vip 102：svip 103：加油包
     */
    private Integer producttype;

    /**
     * 商品id
     */
    private Integer productid;

    /**
     * 业务ID
     */
    private String pid;

    /**
     * 外部交易号，非空，长度30
     */
    private String outTradeNo;


}
