package cn.mingyang.cloud.usercenter.task;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.mingyang.cloud.center.common.request.BaseRequest;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.center.common.service.RedisService;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.comEnum.Constant;
import cn.mingyang.cloud.usercenter.comEnum.PayType;
import cn.mingyang.cloud.usercenter.comEnum.PayTypeDetail;
import cn.mingyang.cloud.usercenter.dao.entity.PaySubscribe;
import cn.mingyang.cloud.usercenter.dao.mapper.PayMapper;
import cn.mingyang.cloud.usercenter.request.PayOrderRequest;
import cn.mingyang.cloud.usercenter.service.AliPayService;
import cn.mingyang.cloud.usercenter.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;


/**
 * 签约支付定时发起支付
 */
@Component
@Slf4j
public class SignPayTask {

    /**
     * 订阅支付Redis锁
     */
    public final String SUBSCRIBE_PAY_KEY_LOCK = "SUBSCRIBE_PAY_KEY_LOCK";
    public final String SUBSCRIBE_PAY_KEY_LOCK_VALUE = "1";


    private final RedisService redisService;
    private final OrderService orderService;
    private final AliPayService aliPayService;
    private final PayMapper payMapper;

    public SignPayTask(RedisService redisService, OrderService orderService, AliPayService aliPayService, PayMapper payMapper) {
        this.redisService = redisService;
        this.orderService = orderService;
        this.aliPayService = aliPayService;
        this.payMapper = payMapper;
    }

    /**
     * 签约支付每日7:00～22:00整点执行支付
     * cron表达式说明：0 0 7-22 * * ?
     * - 秒：0（第0秒）
     * - 分：0（第0分钟）
     * - 时：7-22（7点到22点）
     * - 日：*（每天）
     * - 月：*（每月）
     * - 周：?（不指定）
     */
    @Scheduled(cron = "0 0 7-22 * * ?")
    public void signPay() {
        log.info("签约支付每日7:00～22:00整点执行支付");
        boolean isGetLock = false;
        try {
            //订阅支付锁
            if (!redisService.tryLock(this.SUBSCRIBE_PAY_KEY_LOCK, this.SUBSCRIBE_PAY_KEY_LOCK_VALUE, 60 * 30)) {
                log.warn("获取签约支付锁失败");
            } else {
                isGetLock = true;
                //查询今日需要支付的签约订单
                Constant.AllDs.ALL.forEach(pid -> {
                    try {
                        List<PaySubscribe> subscribes = orderService.selectNeedPaySubscribe(pid, DateUtil.today());
                        if (CollectionUtil.isEmpty(subscribes)) {
                            log.info("{} No need to pay today", pid);
                            return;
                        }
                        log.info("Pay subscribe, pid: {}, count: {}", pid, subscribes.size());
                        subscribes.forEach(subscribe -> {
                            try {
                                // 如果是支付失败，且不可重试或者非阿里支付，则跳过
                                if (Objects.equals(subscribe.getRetry(), Constant.Retry.NO)
                                        || !subscribe.getPlatformcode().equals(PayType.ALI.getCode())) {
                                    log.info("Pay subscribe, subscribe: {} is not retry", subscribe);
                                    return;
                                }
                                // 支付类型
                                PayType payType = PayType.fromCode(subscribe.getPlatformcode());
                                // 命令参数
                                CmdRequest cmd = new CmdRequest("", "", subscribe.getAppid(), "", pid);
                                // 通用参数
                                ComRequest com = new ComRequest();
                                BaseRequest base = new BaseRequest();
                                base.setUserid(subscribe.getUserid() + "");
                                base.setOsid(subscribe.getOsid());
                                base.setDf(subscribe.getDf());
                                base.setPid(pid);
                                com.setBase(base);
                                // 支付请求
                                PayOrderRequest payRequest = new PayOrderRequest();
                                payRequest.setProductid(subscribe.getProductid());
                                payRequest.setIssubscribe(Constant.IsSubscribe.YES);
                                payRequest.setPaycode(PayTypeDetail.ALI_SUBSCRIBE.getCode());
                                payRequest.setAgreementno(subscribe.getAgreementno());
                                // 支付结果
                                CommonResult<?> result = null;
                                if (payType != null && Objects.equals(payType.getCode(), PayType.ALI.getCode())) {
                                    result = aliPayService.aliUnifiedOrder(cmd, com, payRequest);
                                }
                                if (result != null && result.isSuccess()) {
                                    log.info("Pay subscribe success, subscribe: {}", subscribe);
                                    // 更新下次扣款时间
                                    orderService.updatePaySubscribeNextDeductTime(pid, subscribe.getId(), subscribe.getNextdeducttime()
                                            .plusDays(subscribe.getDuration()).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                                } else {
                                    log.error("Pay subscribe failed, subscribe: {}", subscribe);
                                }
                            } catch (Exception exception) {
                                log.error("Pay subscribe error, subscribe: {}", subscribe, exception);
                            }
                        });
                    } catch (Exception e) {
                        log.error("Pay subscribe error, pid: {}", pid, e);
                    }
                });
            }
        } catch (Exception e) {
            log.error("签约支付异常，异常信息：", e);
        } finally {
            // 获取到锁，则最终释放锁
            if (isGetLock) {
                // 确保释放锁
                if (!redisService.unlock(this.SUBSCRIBE_PAY_KEY_LOCK, this.SUBSCRIBE_PAY_KEY_LOCK_VALUE)) {
                    log.warn("签约支付锁释放失败（可能已超时自动释放），key: {} ", this.SUBSCRIBE_PAY_KEY_LOCK);
                }
            }
        }
    }
}
