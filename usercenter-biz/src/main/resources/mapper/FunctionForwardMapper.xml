<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mingyang.cloud.usercenter.dao.mapper.FunctionForwardMapper">

    <!-- 基础字段映射 -->
    <resultMap id="BaseResultMap" type="cn.mingyang.cloud.usercenter.dao.mapper.FunctionForwardMapper">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="main_function" property="mainFunction" jdbcType="VARCHAR"/>
        <result column="new_c_no" property="newCNo" jdbcType="INTEGER"/>
        <result column="forward_url" property="forwardUrl" jdbcType="VARCHAR"/>
        <result column="old_c_no" property="oldCNo" jdbcType="INTEGER"/>
        <result column="os_id" property="osId" jdbcType="VARCHAR"/>
        <result column="df" property="df" jdbcType="VARCHAR"/>
        <result column="old_param" property="oldParam" jdbcType="VARCHAR"/>
        <result column="mock_param" property="mockParam" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 新增 -->
    <insert id="insert" parameterType="cn.mingyang.cloud.usercenter.dao.mapper.FunctionForwardMapper">
        INSERT INTO db_java_to_php
        <trim prefix="(" suffix=")" suffixOverrides=",">
            main_function,
            new_c_no,
            forward_url,
            old_c_no,
            os_id,
            df,
            old_param,
            create_time,
            update_time
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            #{mainFunction},
            #{newCNo},
            #{forwardUrl},
            #{oldCNo},
            #{osId},
            #{df},
            #{oldParam, jdbcType=OTHER, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
            #{createTime},
            #{updateTime}
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        DELETE FROM db_java_to_php WHERE id = #{id}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteBatchIds">
        DELETE FROM db_java_to_php
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 更新 -->
    <update id="update" parameterType="cn.mingyang.cloud.usercenter.dao.mapper.FunctionForwardMapper">
        UPDATE db_java_to_php
        <set>
            <if test="mainFunction != null">main_function = #{mainFunction},</if>
            <if test="newCNo != null">new_c_no = #{newCNo},</if>
            <if test="forwardUrl != null">forward_url = #{forwardUrl},</if>
            <if test="oldCNo != null">old_c_no = #{oldCNo},</if>
            <if test="osId != null">os_id = #{osId},</if>
            <if test="df != null">df = #{df},</if>
            <if test="oldParam != null">old_param = #{oldParam, jdbcType=OTHER, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </set>
        WHERE id = #{id}
    </update>

    <!-- 查询 -->
    <select id="selectById" resultType="cn.mingyang.cloud.center.common.dao.entity.FunctionForward" parameterType="java.lang.Integer">
        SELECT * FROM db_java_to_php
        WHERE id = #{id}
    </select>

    <select id="selectAll" resultType="cn.mingyang.cloud.center.common.dao.entity.FunctionForward">
        SELECT * FROM db_java_to_php ORDER BY create_time DESC
    </select>

    <select id="selectByCondition" resultType="cn.mingyang.cloud.center.common.dao.entity.FunctionForward">
        SELECT * FROM db_java_to_php
        <where>
            <if test="mainFunction != null and mainFunction != ''">
                AND main_function = #{mainFunction}
            </if>
            <if test="newCNo != null">
                AND new_c_no = #{newCNo}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper>