<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mingyang.cloud.usercenter.dao.mapper.ProductMapper">

    <!--根据应用ID查询产品信息-->
    <select id="getProductInfoByAppid" resultType="cn.mingyang.cloud.usercenter.dao.entity.ProductInfo">
        SELECT
                a.id,
                a.productid,
                a.productname,
                a.productimg,
                a.quantity,
                a.duration,
                a.title,
                a.subtitle,
                a.producttype,
                a.order AS sort,
                a.note,
                b.*
        FROM db_product_info a INNER JOIN db_product_price_info b ON a.productid = b.productid
        WHERE b.appid = #{appid} and b.osid = #{osid}
        <if test="null != product.producttype">
            and a.producttype = #{product.producttype}
        </if>
        <if test="null != product.apiversion">
            and b.version <![CDATA[ <= ]]>  #{product.apiversion}
        </if>
    </select>


    <select id="getSellPointInfoByAppid" resultType="cn.mingyang.cloud.usercenter.dao.entity.SellPointInfo">
        SELECT
                    u.productid,
                    p.sellgroupid,
                    p.sellgroup,
                    p.sellname,
                    p.free,
                    p.quantity,
                    p.svipquantity,
                    p.resetunit,
                    p.appid,
                    p.startuserid,
                    p.enduserid,
                    p.selltype,
                    p.resettype
        FROM db_product_price_info u
        INNER JOIN db_product_sell_info o ON u.productid = o.productid
        INNER JOIN db_sell_point_info p ON o.sellgroupid = p.sellgroupid
        WHERE u.appid = #{appid} and u.osid = #{osid};
    </select>

    <!--获取免费权益-->
    <select id="getGiftSellPoint" resultType="cn.mingyang.cloud.usercenter.dao.entity.SellPointInfo">
        SELECT * FROM db_sell_point_info
        WHERE free > 0
        <if test="null != appIds and appIds.size > 0 ">
            AND appid IN
            <foreach collection="appIds" item="sg" open="(" separator="," close=")">
                #{sg}
            </foreach>
        </if>
    </select>


    <select id="getSellPointByProductId" resultType="cn.mingyang.cloud.usercenter.dao.entity.SellPointInfo">
        SELECT a.productid,
        b.sellgroupid,
        b.sellgroup,
        b.sellname,
        b.free,
        b.quantity,
        b.svipquantity,
        b.resetunit,
        b.appid,
        b.startuserid,
        b.enduserid,
        b.selltype,
        b.resettype
        FROM db_product_sell_info a
        INNER JOIN
        db_sell_point_info b
        ON a.sellgroupid = b.sellgroupid
        where a.productid = #{productid}
    </select>


    <select id="getProductType" resultType="cn.mingyang.cloud.usercenter.dao.domain.ProductDoMain">
        SELECT
                productid,
                producttype,
                duration,
                quantity,
                title,
                productname
        FROM    db_product_info
        WHERE   productid = #{productid};
    </select>

    <!--根据别名查询到对应的商品-->
    <select id="getProductByAlias" resultType="cn.mingyang.cloud.usercenter.dao.domain.ProductDoMain">
        SELECT
                *
        FROM    db_product_price_info
        WHERE   alias = #{alias};
    </select>

    <!--查询新的产品信息-->
    <select id="queryNewProduct" resultType="cn.mingyang.cloud.usercenter.dao.domain.ProductDoMain">
        SELECT
                pi.productid,
                pi.productname,
                pi.title,
                pi.quantity,
                pi.duration,
                pi.producttype,
                pri.orginalprice,
                pri.realprice,
                pri.*
        FROM    db_product_info pi LEFT JOIN db_product_price_info pri ON pi.productid = pri.productid
        WHERE   1 = 1
        <if test="null != product.productid">
            and pi.productid = #{product.productid}
        </if>
        <if test="null != product.productname">
            and pi.productname = #{product.productname}
        </if>
    </select>


    <!--根据查询老商品表-->
    <select id="queryOldProductByName" resultType="cn.mingyang.cloud.usercenter.dao.domain.ProductDoMain">
        SELECT
                id AS productid,
                product_name AS productname
        FROM
                aitw_productinfo2
        WHERE
        product_name = #{productName}
        limit 1;
    </select>


</mapper>