<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mingyang.cloud.usercenter.dao.mapper.PayMapper">

    <!--通过应用ID和支付渠道查询支付配置-->
    <select id="getPayChannelConfig" resultType="cn.mingyang.cloud.usercenter.dao.entity.PayConfig">
        SELECT cc.*
        FROM db_pay_app pc
                 LEFT JOIN db_pay_config_relation cr ON pc.id = cr.payappid
                 LEFT JOIN db_pay_config cc ON cr.payconfigid = cc.id
        WHERE cc.platformcode = #{payChannel}
          AND pc.appid = #{appId}
          AND pc.valid = 1
          AND cc.valid = 1 LIMIT 1;
    </select>

    <!--通过商家AppID获取支付配置-->
    <select id="getPayChannelConfigByPayChannel" resultType="cn.mingyang.cloud.usercenter.dao.entity.PayConfig">
        SELECT pcc.*
        FROM db_pay_config pcc
        WHERE pcc.platformappid = #{payAppId}
          AND pcc.platformcode = #{payChannel}
          AND pcc.valid = 1 LIMIT 1;
    </select>


    <!--根据订单编号查询订单信息-->
    <select id="getUserOrder" resultType="cn.mingyang.cloud.usercenter.dao.entity.OrgUserOrder">
        SELECT *
        FROM org_userorder
        WHERE out_trade_no = #{outtradeno}
    </select>

    <!--查询商品信息-->
    <select id="getProductInfoById" resultType="cn.mingyang.cloud.usercenter.dao.entity.OrgProduct">
        SELECT *
        FROM org_productinfo
        WHERE id = #{id}
    </select>

    <!--根据商品名称查询商品信息-->
    <select id="getProductInfoByName" resultType="cn.mingyang.cloud.usercenter.dao.entity.OrgProduct">
        SELECT *
        FROM org_productinfo
        WHERE product_name = #{productName}
    </select>

    <!--查询优惠卷信息-->
    <select id="getCouponById" resultType="cn.mingyang.cloud.usercenter.dao.entity.OrgCoupon">
        SELECT *
        FROM org_coupon_info
        WHERE id = #{id}
    </select>

    <!--插入订单-->
    <insert id="insertUserOrder" parameterType="cn.mingyang.cloud.usercenter.dao.entity.OrgUserOrder">
        INSERT INTO org_userorder (out_trade_no, userid, origin_price, fee, type, time, status, deviceid, neednotifyadd,
                                   appid, osid, df,
                                   product_name, quantity, openid, syncstatus, address_info, mch_id, old_df, ad_state,
                                   subscribe_price, is_subscribe)
        VALUES (#{outTradeNo}, #{userid}, #{originPrice}, #{fee}, #{type}, #{time}, #{status}, #{deviceid},
                #{neednotifyadd},
                #{appid}, #{osid}, #{df}, #{productName}, #{quantity}, #{openid}, #{syncstatus}, #{addressInfo},
                #{mchId},
                #{oldDf}, #{adState}, #{subscribePrice}, #{isSubscribe})
    </insert>

    <!--通过主键id查询订单-->
    <select id="getUserOrderById" resultType="cn.mingyang.cloud.usercenter.dao.entity.OrgUserOrder">
        SELECT *
        FROM org_userorder
        WHERE id = #{id}
    </select>

    <!--根据订单号查询订单-->
    <select id="getUserOrderByOutTradeNo" resultType="cn.mingyang.cloud.usercenter.dao.entity.OrgUserOrder">
        SELECT *
        FROM org_userorder
        WHERE out_trade_no = #{outTradeNo}
    </select>

    <!--插入单据流水-->
    <insert id="insertOrgBills" parameterType="cn.mingyang.cloud.usercenter.dao.entity.OrgBills">
        INSERT INTO org_bills (userid, time, fee, tradeno, description, type, anchorname, beforecoins, aftercoins, df,
                               appid, mch_id)
        VALUES (#{userid}, #{time}, #{fee}, #{tradeno}, #{description}, #{type}, #{anchorname}, #{beforecoins},
                #{aftercoins},
                #{df}, #{appid}, #{mchId})
    </insert>

    <!--插入订单订阅信息-->
    <insert id="insertOrgSubscription" parameterType="cn.mingyang.cloud.usercenter.dao.entity.OrgSubscription">
        INSERT INTO org_subscribeusers (userid, out_trade_no, subscribe_status, fee, subscribe_cycle, createtime,
                                        endtime, status, payment, valid, svipendtime)
        VALUES (#{userid}, #{outTradeNo}, #{subscribeStatus}, #{fee}, #{subscribeCycle}, #{createtime}, #{endtime},
                #{status}, #{payment}, #{valid}, #{svipendtime})
    </insert>

    <!--根据订单号更新订单状态-->
    <update id="updateOrderStatusByOutTradeNo">
        UPDATE org_userorder
        SET status = #{status}
        WHERE out_trade_no = #{outTradeNo}
    </update>

    <!--根据用户ID更新订阅表-->
    <update id="updateSubscribeByUserId">
        UPDATE org_subscribeusers
        SET valid = #{valid}
        WHERE userid = #{userid}
    </update>

    <!--根据用户ID查询订阅表-->
    <select id="selectSubscribeByUserId" resultType="cn.mingyang.cloud.usercenter.dao.entity.OrgSubscription">
        SELECT *
        FROM org_subscribeusers
        WHERE userid = #{userid}
          AND valid = 1 LIMIT 1
    </select>

    <!--插入支付订阅记录-->
    <insert id="insertPaySubscribe" parameterType="cn.mingyang.cloud.usercenter.dao.entity.PaySubscribe">
        INSERT INTO db_pay_subscribe (firstouttradeno, platformcode, mchid, appid, df, userid, osid, productid,
                                      nextdeducttime, status, fee, duration, retry, agreementno, currency, configvalues)
        VALUES (#{firstouttradeno}, #{platformcode}, #{mchid}, #{appid}, #{df}, #{userid}, #{osid}, #{productid},
                #{nextdeducttime}, #{status}, #{fee}, #{duration}, #{retry}, #{agreementno}, #{currency},
                #{configvalues})
    </insert>

    <!--根据用户ID和商品ID查询支付订阅记录-->
    <select id="selectPaySubscribeByUserIdAndProductId"
            resultType="cn.mingyang.cloud.usercenter.dao.entity.PaySubscribe">
        SELECT *
        FROM db_pay_subscribe
        WHERE userid = #{userid}
          AND productid = #{productid}
        ORDER BY createtime DESC LIMIT 1
    </select>

    <!--根据首次订单号查询支付订阅记录-->
    <select id="selectPaySubscribeByFirstOutTradeNo" resultType="cn.mingyang.cloud.usercenter.dao.entity.PaySubscribe">
        SELECT *
        FROM db_pay_subscribe
        WHERE firstouttradeno = #{firstouttradeno}
    </select>

    <!--更新支付订阅状态-->
    <update id="updatePaySubscribeStatus">
        UPDATE db_pay_subscribe
        SET status      = #{status},
            agreementno = #{agreementno}
        WHERE id = #{id}
    </update>

    <!--更新支付订阅配置-->
    <update id="updatePaySubscribeMessage">
        UPDATE db_pay_subscribe
        SET configvalues = #{configvalues}
        WHERE firstouttradeno = #{firstouttradeno}
    </update>

    <!--更新支付订阅下次扣款时间-->
    <update id="updatePaySubscribeNextDeductTime">
        UPDATE db_pay_subscribe
        SET nextdeducttime = #{nextdeducttime},
            updatetime     = NOW()
        WHERE id = #{id}
    </update>

    <!--查询需要支付的订阅记录-->
    <select id="selectNeedPaySubscribe" resultType="cn.mingyang.cloud.usercenter.dao.entity.PaySubscribe">
        SELECT *
        FROM db_pay_subscribe
        WHERE DATE_SUB(nextdeducttime, INTERVAL 2 DAY) <![CDATA[ <= ]]> #{deducttime}
          AND status = 1
    </select>

    <!--查询是否有待支付的订阅订单-->
    <select id="selectNoPaySubscribeOrder" resultType="cn.mingyang.cloud.usercenter.dao.entity.OrgUserOrder">
        SELECT *
        FROM org_userorder
        WHERE mch_id = #{mchid}
          AND is_subscribe = 1
          AND userid = #{userid}
          AND product_name = #{productname}
          AND `status` = 1
          AND `time` >= DATE_SUB(#{deducttime}, INTERVAL 2 DAY )
    </select>

    <!--支付订阅列表-->
    <select id="paymentSubscribeList" resultType="cn.mingyang.cloud.usercenter.response.PaySubscribeResponse">
        SELECT ps.id,
               ps.appid,
               IF(pi.clienttitle, bp.title, pi.clienttitle) AS title,
               ps.createtime                                AS `time`,
               ps.nextdeducttime                            AS deducttime,
               ps.fee,
               ps.`status`,
               1                                            AS quantity
        FROM db_pay_subscribe ps
                 LEFT JOIN db_product_price_info pi ON ps.productid = pi.productid
            AND ps.appid = pi.appid
            AND ps.osid = pi.osid
                 LEFT JOIN db_product_info bp ON pi.productid = bp.productid
        WHERE userid = #{userid}
          AND platformcode = #{platformcode}
    </select>

    <!--根据订阅ID集合查询订阅记录-->
    <select id="selectPaySubscribeByIds" resultType="cn.mingyang.cloud.usercenter.dao.entity.PaySubscribe">
        SELECT *
        FROM db_pay_subscribe
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper>


