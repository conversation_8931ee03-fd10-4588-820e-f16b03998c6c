<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.mingyang.cloud.usercenter.dao.mapper.RightsMapper">

    <select id="getUserRightInfo" resultType="cn.mingyang.cloud.usercenter.dao.entity.UserRightInfo">
        SELECT id,
        userid,
        coins,
        numbercharging,
        timescharging,
        vipendtime,
        svipendtime
        FROM db_user_right_info
        WHERE userid = #{userid};
    </select>


    <insert id="addUserRight" parameterType="cn.mingyang.cloud.usercenter.dao.entity.UserRightInfo">
        INSERT INTO db_user_right_info (userid,
        coins,
        numbercharging,
        timecharging,
        vipendtime,
        svipendtime)
        VALUES (#{userid}, #{coins}, #{numbercharging}, #{timecharging}, #{vipendtime}, #{svipendtime})
    </insert>

    <!--写入权益或更新权益-->
    <select id="insertUserRightsInfo" parameterType="cn.mingyang.cloud.usercenter.dao.entity.UserRightInfo">
        INSERT INTO db_user_right_info
        (userid, coins, numbercharging, timescharging, vipendtime, svipendtime)
        VALUES
        (#{param.userid}, #{param.coins}, #{param.numbercharging}, #{param.timescharging}, #{param.vipendtime}, #{param.svipendtime})
        ON DUPLICATE KEY UPDATE
        coins = VALUES(coins),
        numbercharging = VALUES(numbercharging),
        timescharging = VALUES(timescharging),
        vipendtime = VALUES(vipendtime),
        svipendtime = VALUES(svipendtime);
    </select>

    <update id="updateUserRightInfo" parameterType="cn.mingyang.cloud.usercenter.dao.entity.UserRightInfo">
        UPDATE db_user_right_info
        <set>
            <if test="param.userid != null">userid = #{param.userid},</if>
            <if test="param.coins != null">coins = #{param.coins},</if>
            <if test="param.numbercharging != null">numbercharging = #{param.numbercharging},</if>
            <if test="param.timescharging != null">timescharging = #{param.timescharging},</if>
            <if test="param.vipendtime != null">vipendtime = #{param.vipendtime},</if>
            <if test="param.svipendtime != null">svipendtime = #{param.svipendtime},</if>
        </set>
        WHERE id = #{param.id}
    </update>


    <select id="getFunctionPriceByAppid" resultType="cn.mingyang.cloud.center.common.dao.entity.FunctionPriceItem">
        SELECT id,
        functionid,
        sellgroup,
        expend,
        expendtype,
        expendhint,
        appid,
        startuserid,
        enduserid,
        righttype,
        osid,
        errmessage
        FROM db_function_price_item
        WHERE appid = #{appid}
        and osid = #{osid};
    </select>

    <select id="getFunctionPriceByFunctionId" resultType="cn.mingyang.cloud.center.common.dao.entity.FunctionPriceItem">
        SELECT  * FROM db_function_price_item WHERE functionid = #{functionid};
    </select>

    <!--根据功能ID获取功能价格-->
    <select id="getSellPointBySellGroup" resultType="cn.mingyang.cloud.usercenter.dao.entity.SellPointInfo">
        SELECT
        *
        FROM
        db_sell_point_info
        WHERE 1 = 1
        <if test="null != sellGroups and sellGroups.size > 0 ">
            AND sellgroup IN
            <foreach collection="sellGroups" item="sg" open="(" separator="," close=")">
                #{sg}
            </foreach>
        </if>
    </select>

    <!--权益缓存同步到数据库-->
    <update id="syncDatabaseFromRedis" parameterType="cn.mingyang.cloud.usercenter.dao.domain.UserRightsCache">
        UPDATE db_user_right_info
        SET
            coins = #{param.coins},
            numbercharging = #{param.numbercharging},
            timescharging = #{param.timescharging},
            vipendtime = #{param.vipendtime},
            svipendtime = #{param.svipendtime}
        WHERE userid = #{param.userid}
    </update>


    <!--查询用户列表-->
    <select id="selectNeedSyncUserList" resultType="cn.mingyang.cloud.usercenter.dao.domain.RightsTemp">
        SELECT * FROM db_rights_temp WHERE syncstatus = 0 ORDER BY createtime DESC LIMIT #{offset}, #{limit};
    </select>

    <!--查询用户总数-->
    <select id="selectNeedSyncUserCount" resultType="java.lang.Integer">
        SELECT count(1) FROM db_rights_temp WHERE syncstatus = 0;
    </select>

    <!--更新用户同步状态-->
    <update id="updateSyncUserRight">
        UPDATE db_rights_temp
        SET syncstatus = 1
        WHERE syncstatus = 0 AND userid IN
        <foreach collection="userIds" item="ud" open="(" separator="," close=")">
            #{ud}
        </foreach>
    </update>

    <!--批量插入用户权益-->
    <insert id="batchInsertUserRights" parameterType="java.util.List">
        INSERT INTO db_user_right_info (
            userid,
            coins,
            numbercharging,
            timescharging,
            vipendtime,
            svipendtime
        ) VALUES 
        <foreach collection="list" item="item" separator=",">
            (
            #{item.userid},
            #{item.coins},
            #{item.numbercharging},
            #{item.timescharging},
            #{item.vipendtime},
            #{item.svipendtime}
            )
        </foreach>
    </insert>

    <!--PPT查询老权益-->
    <select id="selectPptOldRights" resultType="cn.mingyang.cloud.usercenter.dao.domain.RightsTemp">
        SELECT
                pc.userid,
                ps.endtime,
                ps.svipendtime,
                if(pc.coins <![CDATA[ <= ]]> 0,0,pc.coins) AS coins
        FROM org_coinsuser pc
        LEFT JOIN org_subscribeusers ps ON pc.userid = ps.userid AND ps.valid = 1
        WHERE pc.userid = #{userid}
        ORDER BY endtime DESC LIMIT 1
    </select>


    <!--AITW查询老权益-->
    <select id="selectAiTwOldRights" resultType="cn.mingyang.cloud.usercenter.dao.domain.RightsTemp">
        SELECT
                mu.userid,
                msb.endtime,
                msb.svipendtime AS sviptime,
                mup.video_duration AS videoDuration,
                mup.aiimg_num AS aiImgNum
        FROM
        org_user mu
        LEFT JOIN org_subscribeusers msb ON mu.userid = msb.userid
        AND msb.valid = 1
        LEFT JOIN org_user_policy mup ON mu.userid = mup.userid
        WHERE mu.userid = #{userid}
        ORDER BY msb.createtime DESC
        LIMIT 1;
    </select>

    <!--查询古戈尔老权益-->
    <select id="selectGolOldRights" resultType="cn.mingyang.cloud.usercenter.dao.domain.RightsTemp">
        SELECT
                mu.userid,
                msb.endtime,
                msb.svipendtime AS sviptime,
                0 AS videoDuration,
                0 AS aiImgNum
        FROM
        org_user mu
        LEFT JOIN org_subscribeusers msb ON mu.userid = msb.userid AND msb.valid = 1
        WHERE mu.userid = #{userid} LIMIT 1;
    </select>

    <!--查询配音老权益-->
    <select id="selectIflyttsOldRights" resultType="cn.mingyang.cloud.usercenter.dao.domain.RightsTemp">
        SELECT
            u.userid,
            s.endtime AS endtime,
            s.svipendtime AS sviptime
        FROM
            iflytts_user u
                LEFT JOIN iflytts_subscribeusers s ON u.userid = s.userid AND s.valid = 1
        WHERE
            u.userid = #{userid}
        LIMIT 1;
    </select>

    <!--删除用户权益-->
    <delete id="deleteUserRights">
        DELETE FROM db_user_right_info WHERE userid = #{userid};
    </delete>

    <!--批量插入用户权益记录-->
    <insert id="batchInsertUserRightsRecord" parameterType="java.util.List">
        INSERT INTO db_rights_record (
            userid,
            functionid,
            sellgroup,
            quantity,
            `desc`,
            recordtype
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.userid},
                #{item.functionid},
                #{item.sellgroup},
                #{item.quantity},
                #{item.desc},
                #{item.recordtype}
            )
        </foreach>
    </insert>

    <!--查询用户权益记录-->
    <select id="selectRightRecord" resultType="cn.mingyang.cloud.usercenter.dao.entity.UserRightsRecord">
        SELECT *
        FROM db_rights_record
        WHERE  1 = 1
        <if test="null != record.userid">
            and userid = #{record.userid}
        </if>
        <if test="null != record.recordtype">
            and recordtype = #{record.recordtype}
        </if>
        <if test="null != record.quantity">
            and quantity = #{record.quantity}
        </if>
    </select>


    <!--查询用户权益记录-->
    <select id="selectUserRightsRecord" resultType="cn.mingyang.cloud.usercenter.dao.entity.UserRightsRecord">
        SELECT
            id,
            userid,
            functionid,
            sellgroup,
            quantity,
            `desc`,
            `time`,
            recordtype
        FROM db_rights_record
        WHERE  1 = 1
        <if test="null != record.userid">
            and userid = #{record.userid}
        </if>
        <if test="null != record.flag and 'add' == record.flag">
            and recordtype != 201
        </if>
        <if test="null != record.flag and 'expend' == record.flag">
            and recordtype = 201
        </if>
        <if test="null != record.filterSellGroup and record.filterSellGroup.size > 0 ">
            AND sellgroup IN
            <foreach collection="record.filterSellGroup" item="sg" open="(" separator="," close=")">
                #{sg}
            </foreach>
        </if>
        ORDER BY `time` DESC
        <if test="null != record.pageSize">
            LIMIT #{record.offset},#{record.pageSize}
        </if>
    </select>

    <!--查询用户权益记录总数-->
    <select id="selectUserRightsRecordCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM db_rights_record
        WHERE  1 = 1
        <if test="null != record.userid">
            and userid = #{record.userid}
        </if>
        <if test="null != record.flag and 'add' == record.flag">
            and recordtype != 201
        </if>
        <if test="null != record.flag and 'expend' == record.flag">
            and recordtype = 201
        </if>
        <if test="null != record.filterSellGroup and record.filterSellGroup.size > 0 ">
            AND sellgroup IN
            <foreach collection="record.filterSellGroup" item="sg" open="(" separator="," close=")">
                #{sg}
            </foreach>
        </if>
    </select>

</mapper>