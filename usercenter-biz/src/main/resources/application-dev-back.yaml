spring:
  application:
    name: biz-server  # 统一服务名
  config:
    import: "nacos:${spring.cloud.nacos.server-addr}"
  cloud:
    nacos:
      server-addr: mse-1f39b670-p.nacos-ans.mse.aliyuncs.com:8848
      config:
        namespace: c4b706d3-b1eb-4a04-8075-44b2b0cfff79
        group: biz-server
        file-extension: yaml
        refresh-enabled: true
        extension-configs:
          - data_id: biz-server-dev.yaml
            group: DEFAULT_GROUP
            refresh: true
      discovery:
        namespace: c4b706d3-b1eb-4a04-8075-44b2b0cfff79
        group: DEFAULT_GROUP