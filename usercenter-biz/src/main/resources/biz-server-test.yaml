spring:
  #数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为 mysql
      primary: chat
      # 严格匹配数据源，默认false。 true：未匹配到指定数据源时抛异常；false：使用默认数据源。
      strict: false
      #      type: com.alibaba.druid.pool.DruidDataSource
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
      datasource:
        chat:
          url: **************************************************************************************************************************************************************************************
          username: bizmix
          password: My0527@o0il
          driver-class-name: com.mysql.cj.jdbc.Driver
        ppt:
          url: *************************************************************************************************************************************************************************************
          username: bizmix
          password: My0527@o0il
          driver-class-name: com.mysql.cj.jdbc.Driver

#################### 芋道相关配置 ####################
yudao:
  info:
    version: 1.0.0
    base-package: com.my.usercenter
  web:
    admin-ui:
      url: http://dashboard.yudao.iocoder.cn # Admin 管理后台 UI 的地址

################## 基础资源转发 #####################
forward:
  #转发服务
  server:
    - chat:
        url: https://biz.aixiezuo888.com/chat/api.php
    - ppt:
        url: https://ser.mingyangtek.com/ppt/api.php
  #ios_id
  ios_id:
    - and: android
    - ios: ios
    - mac: mac
    - win: win
    - web: website
    - wx: weixin
    - wxios: wxios
    - dy: douyin
    - dyios: dyios
    - qu: quickapp
    - ks: kuaishou
    - ksios: ksios
    - har: harmony
    - wgzh: wxios
    - wgzhios: wxios
  #渠道
  df:
    - xiaomi: xiaomi

logging:
  file:
    name: ./logs/${spring.application.name}.log  # 日志路径配置保持不变
  level:
    com.my.base.dao.mapper: DEBUG  # 显示 Mapper 接口的加载过程
    org.mybatis: DEBUG              # 显示 MyBatis 核心日志