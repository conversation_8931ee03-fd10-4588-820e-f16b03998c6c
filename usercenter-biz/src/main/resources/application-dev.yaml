spring:
  config:
    import: "nacos:${spring.cloud.nacos.server-addr}"
  cloud:
    nacos:
      server-addr: mse-1f39b670-p.nacos-ans.mse.aliyuncs.com:8848
      config:
        namespace: c4b706d3-b1eb-4a04-8075-44b2b0cfff79
        group: DEFAULT_GROUP
        file-extension: yaml
        refresh-enabled: true
        extension-configs:
          - data_id: usercenter-biz-dev.yaml
            group: DEFAULT_GROUP
            refresh: true
      discovery:
        namespace: c4b706d3-b1eb-4a04-8075-44b2b0cfff79
        group: DEFAULT_GROUP
  #数据库配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
        allow:
      web-stat-filter:
        enabled: true
    dynamic:
      # 设置默认的数据源或者数据源组,默认值即为 mysql
      primary: dev
      # 严格匹配数据源，默认false。 true：未匹配到指定数据源时抛异常；false：使用默认数据源。
      strict: false
      #      type: com.alibaba.druid.pool.DruidDataSource
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
      datasource:
        dev:
          url: *************************************************************************************************************************************************************************************
          username: root
          password: 123456
          driver-class-name: com.mysql.cj.jdbc.Driver
        ppt:
          url: *************************************************************************************************************************************************************************************
          username: bizmix
          password: My0527@o0il
          driver-class-name: com.mysql.cj.jdbc.Driver
        aitw:
          url: ***********************************************************************************************************************************************************************************
          username: bizmix
          password: My0527@o0il
          driver-class-name: com.mysql.cj.jdbc.Driver
        mix:
          url: **********************************************************************************************************************************************************************************
          username: bizmix
          password: My0527@o0il
          driver-class-name: com.mysql.cj.jdbc.Driver
        gol:
          url: *************************************************************************************************************************************************************************************
          username: bizmix
          password: My0527@o0il
          driver-class-name: com.mysql.cj.jdbc.Driver
        iflytts:
          url: **************************************************************************************************************************************************************************************
          username: biziflyttsadmin
          password: iFlytts2018
          driver-class-name: com.mysql.cj.jdbc.Driver


  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 1800000 # 默认缓存过期时间30分钟(毫秒)
      cache-null-values: false # 不缓存null值


#################### 芋道相关配置 ####################
yudao:
  info:
    version: 1.0.0
    base-package: com.my.usercenter
  web:
    admin-ui:
      url: http://dashboard.yudao.iocoder.cn # Admin 管理后台 UI 的地址

################### 腾讯云配置信息 ###################
tencent:
  secretId: AKID9oSvELOI1e5OXTCi9SZTQUPAyc5NVUHt
  secretKey: Ws6D9uT1RjrlSzMXVGo9IEyZ0IdqtkU9
  region: ap-chengdu

################### 跳转配置 ##########################
forward:
  server:
    chat:
      url: https://biz.aixiezuo888.com/chat/api.php
    ppt:
      url: https://ser.mingyangtek.com/ppt/api.php
    gol:
      url: http://wymtest.peiyintek.com/project/biz/googol/chat/api.php

################### 鸿蒙支付证书 ##########################
har:
  jwt:
    root:
      path: C:\duan\harmony_pay\RootG2Ca.cer
  active:
    time:
      second: 3600

logging:
  file:
    name: /home/<USER>/usercenter-biz/logs/${HOSTNAME}/info.log  # 日志路径配置保持不变
  level:
    cn.mingyang.cloud: DEBUG