spring:
  application:
    name: usercenter-biz  # 统一服务名
  config:
    import: "nacos:${spring.cloud.nacos.server-addr}"
  cloud:
    nacos:
      server-addr: mse-1f39b670-p.nacos-ans.mse.aliyuncs.com:8848
      config:
        namespace: a85f0a00-2cbf-4c12-8b06-45b1111c9d51
        group: usercenter-server
        file-extension: yaml
        refresh-enabled: true
        extension-configs:
          - data_id: usercenter-biz-test.yaml
            group: DEFAULT_GROUP
            refresh: true
      discovery:
        service: usercenter-biz
        namespace: a85f0a00-2cbf-4c12-8b06-45b1111c9d51
        group: DEFAULT_GROUP