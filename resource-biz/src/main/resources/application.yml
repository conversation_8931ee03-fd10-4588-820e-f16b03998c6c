server:
  port: 10002
  servlet:
    context-path: /resource

spring:
  application:
    name: resource-biz
  main:
    allow-bean-definition-overriding: true

  profiles:
    active: dev

  config:
    import:
      - optional:classpath:application-${spring.profiles.active}.yaml # 加载【本地】配置
      - optional:nacos:${spring.application.name}-${spring.profiles.active}.yaml # 加载【Nacos】的配置


# application.yml（如果存在，确保路径正确）
mybatis:
  mapper-locations: classpath:mapper/*.xml  # 对应 XML 映射文件位置
  type-aliases-package: cn.mingyang.cloud.resource.dao.mapper  # 对应实体类包