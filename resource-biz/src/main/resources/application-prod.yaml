--- #################### 注册中心 + 配置中心相关配置 ####################
spring:
  application:
    name: resource-biz  # 统一服务名
  cloud:
    inetutils:
      # 可用ip地址段表示
      preferred-networks: [ '47.100','139.' ]
    nacos:
      server-addr: mse-1f39b670-p.nacos-ans.mse.aliyuncs.com:8848 # Nacos 服务器地址
      config: # 【注册中心】配置项
        server-addr: mse-1f39b670-p.nacos-ans.mse.aliyuncs.com:8848 # Nacos 服务器地址
        namespace: 390b629f-eb1e-4df7-9a08-37c96cc97634 # 命名空间
        group: resource-server # 使用的 Nacos 配置分组，默认为 DEFAULT_GROUP
        file-extension: yaml
        refresh-enabled: true
        extension-configs:
          - data_id: resource-biz-prod.yaml
            group: resource-server
            refresh: true
          - data_id: redis.yaml
            group: DEFAULT_GROUP
            refresh: true
        shared-configs:
          - data_id: redis.yaml
            group: DEFAULT_GROUP
            refresh: true

      #refresh: true # 是否自动刷新配置，默认为 false
      discovery: # 【配置中心】配置项
        service: resource-biz
        namespace: 390b629f-eb1e-4df7-9a08-37c96cc97634 # 命名空间
        group: DEFAULT_GROUP
