spring:
  application:
    name: resource-biz  # 统一服务名
  config:
    import: "nacos:${spring.cloud.nacos.server-addr}"
  cloud:
    nacos:
      server-addr: mse-1f39b670-p.nacos-ans.mse.aliyuncs.com:8848
      config:
        namespace: a85f0a00-2cbf-4c12-8b06-45b1111c9d51
        group: resource-biz
        file-extension: yaml
        refresh-enabled: true
        extension-configs:
          - data_id: resource-biz-test.yaml
            group: DEFAULT_GROUP
            refresh: true
      discovery:
        service: resource-biz
        namespace: a85f0a00-2cbf-4c12-8b06-45b1111c9d51
        group: DEFAULT_GROUP