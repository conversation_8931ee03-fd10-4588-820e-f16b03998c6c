package cn.mingyang.cloud.resource.controller;

import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.center.common.util.MyUtils;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.resource.service.ResourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;


/**
 * AI资源接口API控制层
 */
@Slf4j
@RestController
@RequestMapping("/ai")
public class ResourceApiController {

    private final ResourceService resourceService;

    public ResourceApiController(ResourceService resourceService) {
        this.resourceService = resourceService;
    }


    /**
     * AI证件照
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=11001&*")
    public CommonResult<?> aiIdPhoto(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("AI证件照, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return resourceService.aiIdPhoto(cmdRequest, comRequest);
    }

    /**
     * AI写真
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=11002&*")
    public CommonResult<?> aiPortrait(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("AI写真, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return resourceService.aiPortrait(cmdRequest, comRequest);
    }


    /**
     * AI发型师
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=11003&*")
    public CommonResult<?> aiHairStylis(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("AI发型师, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return resourceService.aiHairStylis(cmdRequest, comRequest);
    }


    /**
     * 图生舞蹈
     *
     * @param comRequest 统一请求
     * @return 返回信息
     */
    @PostMapping("/c=11004&*")
    public CommonResult<?> imgDanceVideoTemplate(
            @RequestAttribute("cmdParam") CmdRequest cmdRequest,
            @RequestBody @Valid ComRequest comRequest) {
        log.info("图生舞蹈, 请求信息： cmd: {} com: {}", JSONUtil.toJsonStr(cmdRequest), JSONUtil.toJsonStr(comRequest));
        return resourceService.imgDanceVideoTemplate(cmdRequest, comRequest);
    }

}
