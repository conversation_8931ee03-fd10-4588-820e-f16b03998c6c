package cn.mingyang.cloud.resource.controller;

import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.api.ProductApi;
import cn.mingyang.cloud.usercenter.api.RightsApi;
import cn.mingyang.cloud.usercenter.api.request.FeignRequest;
import cn.mingyang.cloud.usercenter.api.response.ProductInfoResponse;
import cn.mingyang.cloud.usercenter.api.response.UserRightResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 权益接口API控制层
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class FeignTestController {

    private final RightsApi rightsApi;

    private final ProductApi productApi;

    public FeignTestController(RightsApi rightsApi, ProductApi productApi) {
        this.rightsApi = rightsApi;
        this.productApi = productApi;
    }

    /**
     * 获取商品列表
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/10201")
    public CommonResult<List<ProductInfoResponse>> getProductInfo(@RequestBody FeignRequest feignRequest) {
        return productApi.getProductInfo(feignRequest);
    }

    /**
     * 购买商品
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/10203")
    public CommonResult<UserRightResponse> buyProduct(@RequestBody FeignRequest feignRequest) {
        return productApi.buyProduct(feignRequest);
    }

    /**
     * 新注册用户赠送权限
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/10204")
    public CommonResult<UserRightResponse> newUserGift(@RequestBody FeignRequest feignRequest) {
        return productApi.newUserGift(feignRequest);
    }

    /**
     * 获取用户权益
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/10006")
    public CommonResult<UserRightResponse> getUserRight(@RequestBody FeignRequest feignRequest) {
        return rightsApi.getUserRight(feignRequest);
    }

    /**
     * 扣除权益
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/10083")
    public CommonResult<?> expendUserRights(@RequestBody FeignRequest feignRequest) {
        return rightsApi.expendUserRights(feignRequest);
    }

    /**
     * 校验用户权益
     *
     * @param feignRequest 命令请求
     * @return 返回信息
     */
    @PostMapping("/10086")
    public CommonResult<?> checkUserRights(@RequestBody FeignRequest feignRequest) {
        return rightsApi.checkUserRights(feignRequest);
    }

    /**
     * 获取功能价格列表
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/10087")
    public CommonResult<?> getAllFunctionRights(@RequestBody FeignRequest feignRequest) {
        return rightsApi.getAllFunctionRights(feignRequest);
    }

    /**
     * 重置用户权益
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/10088")
    public CommonResult<UserRightResponse> resetUserRights(@RequestBody FeignRequest feignRequest) {
        return rightsApi.resetUserRights(feignRequest);
    }

    /**
     * 用户权益记录
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/10093")
    public CommonResult<?> userRightRecord(@RequestBody FeignRequest feignRequest) {
        return rightsApi.userRightRecord(feignRequest);
    }


}
