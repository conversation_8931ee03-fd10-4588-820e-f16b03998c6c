package cn.mingyang.cloud.resource.service;

import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.dao.entity.FunctionForward;
import cn.mingyang.cloud.center.common.exception.MyBaseException;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import com.alibaba.nacos.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


/**
 * 资源Service
 */
@Slf4j
@Service
public class ResourceService {

    @Value("${forward.server.gol.url}")
    public String forwardUrl;

    private final CloseableHttpClient httpClient;

    public ResourceService(CloseableHttpClient httpClient) {
        this.httpClient = httpClient;
    }


    /**
     * AI证件照
     */
    public CommonResult<?> aiIdPhoto(CmdRequest cmd, ComRequest comRequest) {
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * AI写真
     */
    public CommonResult<?> aiPortrait(CmdRequest cmd, ComRequest comRequest) {
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * AI发型师
     */
    public CommonResult<?> aiHairStylis(CmdRequest cmd, ComRequest comRequest) {
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }


    /**
     * 图生舞蹈
     */
    public CommonResult<?> imgDanceVideoTemplate(CmdRequest cmd, ComRequest comRequest) {
        //获取转发配置
        FunctionForward forward = this.getForwardConfig(cmd, comRequest);
        return forwardRequest(forward, cmd, comRequest);
    }

    /**
     * 转发请求到目标URL
     */
    private CommonResult<?> forwardRequest(FunctionForward forward, CmdRequest cmd, ComRequest request) {
        try {
            // 1. 初始化转发URL
            //String forwardUrl = initForwardUrl(forward, cmd, request);
            String forwardUrl = forward.getForwardUrl();
            forwardUrl = forwardUrl + "?" + "c=" + cmd.getCno() + "&v=" + cmd.getVersion() + "&a=" + cmd.getAppId() + "&t=" + cmd.getTimestamp();
            log.info("[Forward] 转发请求到URL: {}", forwardUrl);

            // 2. 发送HTTP请求
            HttpPost httpPost = new HttpPost(forwardUrl);
            httpPost.setEntity(new StringEntity(JSONUtil.toJsonStr(request), "UTF-8"));
            httpPost.setHeader("Content-Type", "application/json");

            String response = httpClient.execute(httpPost, response1 -> EntityUtils.toString(response1.getEntity(), "UTF-8"));
            if (StringUtils.isEmpty(response)) {
                return CommonResult.error(GlobalErrorCodeConstants.LOCKED.getCode(), "转发请求失败,返回信息为空！");
            }
            //log.info("[Forward] 收到响应: {}", response);
            // 3. 解析响应
            return JSONUtil.toBean(response, CommonResult.class);
        } catch (Exception e) {
            log.error("[Forward] 转发请求异常", e);
            return CommonResult.error(GlobalErrorCodeConstants.LOCKED.getCode(), "转发请求失败：" + e.getMessage());
        }
    }

    /**
     * 初始化转发URL
     */
    private String initForwardUrl(FunctionForward forward, CmdRequest cmdRequest, ComRequest request) {
        try {
            return forward.getForwardUrl();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new MyBaseException(GlobalErrorCodeConstants.LOCKED.getCode(), "初始化转发URL异常");
        }
    }

    /**
     * 获取转发配置
     */
    private FunctionForward getForwardConfig(CmdRequest cmdRequest, ComRequest comRequest) {
        //查询配置信息
//        List<FunctionForward> forwards = forwardMapper.selectAll();
//        if (CollectionUtils.isEmpty(forwards)) {
//            throw new MyBaseException(BaseErrorCodeEnum.FORWARD_NULL.getCode(), BaseErrorCodeEnum.FORWARD_NULL.getMessage());
//        }
//        //查询对应的转发信息
//        Optional<FunctionForward> forwardConfig = forwards.stream()
//                .filter(x -> x.getNewCNo().equals(cmdRequest.getCno())).findFirst();
//        if (!forwardConfig.isPresent()) {
//            throw new MyBaseException(BaseErrorCodeEnum.FORWARD_NULL.getCode(), BaseErrorCodeEnum.FORWARD_NULL.getMessage());
//        }
        FunctionForward forward = new FunctionForward();
        forward.setForwardUrl(this.forwardUrl);
        return forward;
    }
}
