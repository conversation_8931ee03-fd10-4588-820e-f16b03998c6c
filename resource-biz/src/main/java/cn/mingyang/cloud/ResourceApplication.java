package cn.mingyang.cloud;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication(scanBasePackages = "cn.mingyang.cloud")
@EnableScheduling
@EnableFeignClients
public class ResourceApplication {
    public static void main(String[] args) {
        //SpringApplication.run(UsercenterApplication.class, args);
        ConfigurableApplicationContext application = SpringApplication.run(ResourceApplication.class, args);
        Environment env = application.getEnvironment();
    }
}