<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.mingyang.cloud</groupId>
        <artifactId>usercenter</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>resource-biz</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!--添加公用类-->
        <dependency>
            <groupId>cn.mingyang.cloud</groupId>
            <artifactId>center-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!--用户中心FeignAPI-->
        <dependency>
            <groupId>cn.mingyang.cloud</groupId>
            <artifactId>usercenter-api</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        <!--common基础类-->
        <dependency>
            <groupId>cn.mingyang.cloud</groupId>
            <artifactId>cloud-framework-common</artifactId>
        </dependency>
        <!--微信支付SDK-->
        <dependency>
            <groupId>com.github.wechatpay-apiv3</groupId>
            <artifactId>wechatpay-java</artifactId>
            <version>0.2.17</version>  <!-- 使用最新版本 -->
        </dependency>
        <!--支付宝SDK-->
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.40.237.ALL</version>
        </dependency>
        <!--腾讯云SDk-->
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-vod</artifactId>
            <version>3.1.1156</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>


    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


    <!-- 发布到Maven私有仓库 -->
    <distributionManagement>
        <snapshotRepository>
            <id>my-snapshots</id>
            <name>My snapshots</name>
            <url>http://106.15.225.60:18005/nexus/repository/maven-snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>my-releases</id>
            <name>My releases</name>
            <url>http://106.15.225.60:18005/nexus/repository/maven-releases/</url>
        </repository>
    </distributionManagement>


</project>