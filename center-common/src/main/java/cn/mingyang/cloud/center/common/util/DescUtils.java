package cn.mingyang.cloud.center.common.util;

import com.alibaba.cloud.commons.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.Security;
import java.security.spec.AlgorithmParameterSpec;

@Slf4j
public class DescUtils {

    private static final String CHARSET_NAME = "UTF-8";
    private static final String AES_NAME = "AES";
    // 加密模式
    // 加密模式
    public static final String ALGORITHM9 = "AES/CBC/PKCS5Padding";
    // 偏移量


    private final static boolean isEncrypt = true;

    static {
        try {
            log.info("初始化BouncyCastleProvider");
            Security.addProvider(new BouncyCastleProvider());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 9.0版本加密
     *
     * @param content
     * @return
     */
    public static String encrypt9(String key, String content, String c, String a, String t) {
        byte[] result = null;
        try {
            //接取前16个字符，CBC模式需要128bit的倍数
            //String secKey = md5(key).substring(0,16);
            Cipher cipher = Cipher.getInstance(ALGORITHM9);
            SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(CHARSET_NAME), AES_NAME);
            //long  timestamp = Long.parseLong(t);
            String iv = generateIV(c, a, t);
            AlgorithmParameterSpec paramSpec = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, paramSpec);
            result = cipher.doFinal(content.getBytes(CHARSET_NAME));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Base64.encodeBase64String(result);
    }

    /**
     * 9.0版本解密
     *
     * @param content
     * @return
     */
    public static String decrypt9(String key, String content, String c, String a, String t) {
        if (!isEncrypt) {
            return content;
        }
        try {
            //接取前16个字符，CBC模式需要128bit的倍数
            //String secKey = md5(key).substring(0,16);
            Cipher cipher = Cipher.getInstance(ALGORITHM9);
            SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(CHARSET_NAME), AES_NAME);
            //long  timestamp = Long.parseLong(t);
            String iv = generateIV(c, a, t);
            log.info("iv:{}", iv);
            AlgorithmParameterSpec paramSpec = new IvParameterSpec(iv.getBytes());
            cipher.init(Cipher.DECRYPT_MODE, keySpec, paramSpec);
            return new String(cipher.doFinal(Base64.decodeBase64(content)), CHARSET_NAME);
        } catch (Exception e) {
            log.error("解密失败:{}", e.getMessage());
            e.printStackTrace();
        }
        return StringUtils.EMPTY;
    }


    public static String generateKey(String c, String a, String t) {
        String cat = c + a + t;
        if (cat.length() >= 32) {
            return cat.substring(0, 32);
        } else {
            return String.format("%-32s", cat).replace(' ', '0');
        }
    }


    //最新密钥生成逻辑
    public static String generateIV(String c, String a, String t) {
        try {
            String salt = t + c + a; // 时间戳加盐(CA)
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(salt.getBytes(StandardCharsets.UTF_8));
            String hexHash = bytesToHex(hash);
            return hexHash.substring(0, 16);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate IV", e);
        }
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }


    public static void main(String[] args) {
//        String contents = "{\"base\":{\"aiType\":10,\"ap\":\"wifi\",\"appid\":\"aiznms\",\"deductGold\":true,\"deviceid\":\"4522aad3-3b11-4b5d-9969-bbe5aa54e9ad\",\"df\":\"baidu\",\"ipAddress\":\"127.0.0.1\",\"lg\":\"zh-CN\",\"newFormat\":false,\"osid\":\"android\",\"port\":\"1053\",\"sha1\":\"97382e2f710364891d7d837bb9b3eaeacde9e62f\",\"sid\":\"\",\"sign\":\"b7a5ad35e93c5bbf7cebe74d10095154\",\"token\":\"lxYjuSqKHZCYsMp8F3X4bhnPq5F1EUe4TkbKyFSoM37gbXCO0cVZpxEB4A3tYWgti8Zg157Ydw9cg++TEqIL+YMJ18ryQr14auq0MPU3nBpfo0AHLLYtueoPElcUQehoRaK/eIGgHY0zIgdsdF1Q2pacSBICb6w45hlMemzgR84WG7aqvjjaj5FndMMIefOVQmyCBQLRSdpVsH+oJtDajeSviVuTnzlOkptt8Ic5SCVRUdHDGImir9hCw5taCRgMQpzt9vc6TEtvOjTCyS9iOPIHqyHadBHkJ7P5ubr7YMhbp9ESxJyBB7bRVVFRNkhh\",\"ua\":\"HONOR|MAA-AN00|MAA-AN00|14|1200*2543\",\"userid\":\"12582812\",\"version\":\"1.0\",\"vipType\":0},\"cmd\":\"pptTaskStream\",\"param\":{\"isDemo\":false,\"isLongText\":false,\"outlineId\":\"fe0030b6eeb4442ab90ab1a8166d5dca\",\"templateId\":66}}";
//        String encrypt = encrypt("sqx1083aiznms", contents);
//        System.out.println("加密后:" + encrypt);
//        String decrypt = decrypt( "sqx1083aiznms", "8wHTGudDFRD84pwpurHiAZCCwiaw4TCBHZgbKZeMxDD0Ss94dgDAOzaFleemwWovAgzHCJUwykPxRWQfrJIEmNfWNbOhTYaoU4PlihazEh+1E2MPN95NkaRH71mHBExeYWoepmHdU8x7o2VZ9JnYdkukJ+1p5raDrBZn4yfJehk8TcWi708Aals4Ctt0aFx7r3M/loTz9/BxWV8RKBjg/LdbfX27norilgimvoVCY1Fk5Eoz5hfi9vAOBQo21ZhoBGOtDEojIv0atV6DBvbB5j95TJFDH5aa/aeuUATwfk7RF8CPekHEra4A8iPv7tGQYGvEIfOvrAfcChoiSnb5a+fnj5pFsZrmq4S2nJFrSwOobNJr4ujx9q3HP9e75IfWLu5kPoOAEXNABy2FdVusLQYUf+HjUAdepAgIo858IHpu2KYJRxjTACK4y1cRg+/XSMh7ltoUzLTqKoGyd8kcJMjO48yavW1a7AZJe9yoUwz4mkJ2uUxKlgBKskYAXhyWK/rryxZMTyXojiklFPbWutFR1vbHrb0vyR+ePZwvrNlBXyxPuMVb6NU6VErnweZM+TzFPG1LxqbMUOvLmwtKaZxd3Pqzw3DhxX3hXVAS8B/WcgkVG/68wF1h7n3T6KcQFboOCAfxi2wIvuXGNB2DzvzQtHvB6UGEb1s/laQtYr9kfspafPdQ33yhzYB2OZ+iMn/Xhio0/ENSp8XV4f6vw54udWdXoSTJZw6kcuqS00nGQiVfYGPtyZh0Gag4WCXTqu6uBZo1Mnpau8Sd6FDPYjCfdVSlB4xCeqsSf5QZDu6QurhGXjbd2sJC9dXu8Kzy85xW0UeXV1KH7l4Cn8rjWsonmO5LdL1cjzGAl4kUzktdpVUVeTjN5GN7I0a3Pf9xV5rQMCdLPrT0G1ikxm+cClG9eSIFjJexhxarH80vZEOZcSPvvTpXY9QJVXpWsvf82dBMZ0iLN1Xq0EBMO4/rzXC+7D3/GvHNgmaOmMkMcnlzTysrSWkJso8H+Dio9RQR3mqJaGFOb/VKhl01tVlCWpE2SI+LqiwaYoFe98+WR1T8KKD/YC85C0xbn8tUYlrVHtPsU0TYSBPyU2dBpKnEhDgIwY/4TrDgk2gcUPvNCpEJM3Mu7RcyGuzCprE44+7JgNI/v2d6GJLet7S/QsD7ySgTdSkIv24t78HOYVtCujNJSHzOJ4eKrrli9WYFdYga");
//        System.out.println("解密后:" + decrypt);

        String content = "{\"cmd\":\"AiIdPhoto\",\"base\":{\"df\":\"jlad\",\"userid\":\"12535940\",\"model\":\"HUAWEI|ALN-AL00|ALN-AL00|12|1260*2590\",\"unionid\":\"1\",\"oaid\":\"92f05c6e-9b5d-4fc8-827d-062fa3110977\",\"lg\":\"zh_hans\",\"deviceid\":\"92f05c6e-9b5d-4fc8-827d-062fa3110977\",\"version\":\"1.0.8\",\"adtrackerid\":\"1\",\"osid\":\"and\"},\"param\":{}}";
        String key = generateKey("11001", "st", "1747031240");
        System.out.println("加密key:" + key);
        String encrypt = encrypt9(key, content, "11001", "st", "1747031240");
        System.out.println("加密后:" + encrypt);


        //String encString = " +Fsg8m2jy2DaPxdii7KNpVKKm9qP9RUxs3Mi9YVrgtt97Zd5rWwaXh10q0MHYGMDvVWzSYjzRwcvbJ0Q4Ot5Iizm9AwEFEKhlBkV1JSQC3GkYoZUpOMdB+gVMJfVXsMTvBuy/Bso6Xyh/xdjyGV+N97lArLnql5A783+ItYrP4gfNk9Tt6duoZ6piscK7LvS0uvTL0yJh6Hd0sVl1tKWbeV7yiacDjH8HtXK/qE3w8cwEu62dTUC3LX8EFZpT6w6DzVOHf8eF5N3HUcIUqKhVV0ztCLxfbiI/+smXl3FwurNyx6CVo3wBLFtOCWoA0EkNymhWPoq73/P+Z8FXRHRuCOqPjzwyDRcQJIg0kGhxEkoWcIIevBDgTZAPljjoTkwJaj7p0UkyGwOwwDbERh7alAV7xlNSyAWOzmnP5EMpQnkZ62FQ+nHJ5i9tS3UXaIY2LUFSa+Ghzc1dy5MPUdvZdPfVYNxCwOzkFle36q+px5ld/yzNlj18+VdKARayU0Xub1EmjG26U4/2jpbZEiEE/RsHOdpU8gO0IJlCeCKYZeqJVothmsTT52gCOF4OGZPcav86EbQCwd7tgc6DlRx7Q==";
        //String encString = "GCqWeV/PMItGPd8FVuzLd7OhUU7fgifyl9lm1WJWW2pRohHzJu6EJoH8sIFisUbljAGILTxq841g7n8q6mg3l153HKeJ52hIOJl5q62jF+v/VW+Bh9/LpOEkMxWK4aDVa96uZoF4nrUvcyxHxWjTKMJtlhi8F0c+NgJM78EC9tBgXr9DeY1EqrnSN7Ukh4CHceDquwK1MoL+uoNnIEIa+eTLC0eIXUCyRVbOSZUjTJcsZrpF0XdqHLg6IKGr7GIlqTxIv/WXCKrVfUtCrlonYhm9ht3ZlJVkmQagBedGIWWWJpJoWtiAo/KdoB4XoC9mUfNM48+aLLQLrnsBHrA03jIC8bw2aRj4/nC0moOJPUhlhZZ0IUQUL/oZehPrwZD+Uk1+SNVPUoYw+8Be1jYRWhexA8vS3OdzjpSqWqTdNjUhdi0nHFtHIgrA72Rsx5l3lwJt28TvjvMPk8svoNw3glHCzvdEEJhBj0qkdlWsQrMSyvSj+5gnSPJuwyn169m5KQQGip/HseBamUVV5D80twLoEtVK2Z1GMv6akNotsc+ZWgLOuv0ONo6NmpJJWbIT7tQ7uIBGjED1Kw5uqCGbpfNd+dkmrtS+AXxAF+gWRvW7zpBKF3Q6pK7aVO264AHFIuRDDw8dkPBDedkxNHnRuDH7FTbOM9HZh62m8OK0PIofzKr7l+JUwaOVECfg04Y/UnjSVP84DaobsYx4zlvW8S9CEBc7PbntgwWUq6ttvvTCOAaGdeik+G+/kV2+twSmBEt18JsAJg8/1zIN/L4p2Q==";
        //String encString = "KeWV36uoX458u8Md28hLxSD4IDX8ttYZmENxBToTVK0PmB22s50hzjU2oxufmKhUqLsQ+gUPXAQ/QoQ3qkdvqZVCyyCiPGZ/qSaD8TVoNK0Yvqp2Bke6NSkYysx2pFleZwNp5VPvB8zS+Wo4FilQmRjEf1EjggshQ6O4V6yXwF6ZnGqN+cN/eqsM9C6MTj5iuNF5UmlQcT36icCtHP+8AWX4xAP7sCp0SSTjTPp+lCBly63pMoUqFeXANhy//5ZrOmBMzeqLAVWrka9ebq+i+ye+D5zv6tBeXkfPe8fnM0TTlQW+1d4ufXPH6LUwXfkABPzZSas1IAV6E4x7kFkd45gK61YyCRpyAB0J1D88dX8YRo7cnXYHdym0b4SNayMlRRa3dmZi6A9oFXHgH9gFaVoxAl56ZXZIiua6q8FuGyA=";
        //String key1 = generateKey("13002", "wxczy", "1749088567");
        //System.out.println("解密key:" + key1);


        //String encString = "/GWKw+cxAbopfIq7NzQINZpwqSQBoRGAAYnARQvjSIcXOYE48/zbhMHSigTPp9OUaebMRjUgvtu6gUhTSm7W+sydm1NKR9VaEKQkVrOIvFmZbK44aZt5K/k/LUKJOOdsJlXO1MZMv9PnL2bVIiS4bgfk4Yx82d63vej8HuaT4qaJxGBOcie4pCtbEYdGFcWcrcPYHP1JLw+hEWvMaCQG3wIflw+nOis8kWlzdvROEm8CTULpwDuqGK+o6/5OabKasLP/8ZCik0tEdndJruSZRVgVEVl7OgtCT2hBJ5v0lB/mjrWwn3cE+UExaTSXk2eFj4Vvtuqz4HLfEzsPBzGZ2xSHp6WlBdy/PqHuzBjKN5wEx2fAIQfFPJNy+zZ9U36KEQku+pnT11iW4S+I4UXY3kcHAsoLvinzgM+O9ElAeT+ze/grfAOGP1wC9yNvAPFg8XlmJqq3F/KVAg9lj1RTeRLecwjMI56sEC8DsmGH5YeywiSkKJbMsk05UiAdfaQ/MFUcyskBClAsyzGK5xhCD43e0EPzKqFONtPb1Dz+4lqImL4trIa261xcJ5OsW+bbIjB9HlwrQNiVi0ZV3kJIjett0+Mn8cGfI4szbYYZwVuLZwETAtMhzGNKFZ5PpTDM";
        //String encrypt1 = decrypt9(key1, encrypt, "13002", "wxczy", "1749088567");
        //System.out.println("解密后:" + encrypt1);
    }

}
