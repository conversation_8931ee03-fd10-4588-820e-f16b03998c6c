package cn.mingyang.cloud.center.common.request;


import cn.hutool.json.JSONObject;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 通用请求体
 */
@Data
public class ComRequest {

    /**
     * 命令校验码
     */
    @NotBlank(message = "命令校验码：cmd不能为空")
    private String cmd;

    /**
     * 基础节点
     */
    @NotNull(message = "基础节点参数：base不能为空")
    @Valid
    private BaseRequest base;

    /**
     * 业务特有参数
     */
    private JSONObject param;
}
