package cn.mingyang.cloud.center.common.request;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;


/**
 * Base请求体
 */
@Data
public class BaseRequest {

    /**
     * 渠道号，用于区分发布软件平台
     */
    @NotBlank(message = "渠道号：df不能为空")
    private String df;

    /**
     * 用户id，在没有登陆的情况下传0
     */
    @NotBlank(message = "用户id：userid不能为空")
    private String userid;

    /**
     * 认证备用值（32位，雪花算法生成，跟用户绑定，暂时不做。）
     */
    private String sid;

    /**
     * 安卓（and/ios/wx）
     */
    @NotBlank(message = "操作系统ID：osid不能为空")
    @Pattern(regexp = "and|ios|mac|win|web|wx|wxios|dy|dyios|qu|ks|ksios|har|wgzh|wghzios",
            message = "osid必须是and、ios、mac、win、web、wx、wxios、dy、dyios、qu、ks、ksios、har、wgzh、wghzios中的一个")
    private String osid;

    /**
     * 语言，“zh_hans”简体中文，“zh_hant”繁体中文，
     * en 英文
     */
    @NotBlank(message = "操作语言：lg不能为空")
    private String lg;

    /**
     * App软件版本如：1.0.8
     */
    @NotBlank(message = "App软件版本：version不能为空")
    private String version;

    /**
     * 硬件信息 品牌 手机型号，如：Xiaomi|star|M2102K1C|14|1080*2297
     */
    @NotBlank(message = "硬件信息：model不能为空")
    private String model;

    /**
     * 安卓ID/IOS随机值/小程序用户 openId
     */
    @NotBlank(message = "设备ID：deviceid不能为空")
    private String deviceid;

    /**
     * 手机系统软件版本信息
     */
    //@NotBlank(message = "浏览器名称/版本 | 渲染引擎 | 操作系统")
    private String ua;

    /**
     * 安卓广告ID / 苹果广告回传
     */
    //@NotBlank(message = "广告ID: oaid不能为空")
    private String oaid;

    /**
     * 微信，抖音，快应用unionid
     */
    //@NotBlank(message = "unionid不能为空")
    private String unionid;

    /**
     * android： 华为智能分包的trackerid或者其他平台的智能分包追踪 ID；
     * ios ： idfa
     */
    //@NotBlank(message = "adtrackerid不能为空")
    private String adtrackerid;

    /**
     * pid：数据库前缀
     */
    @NotBlank(message = "业务数据库：pid不能为空")
    private String pid;
}
