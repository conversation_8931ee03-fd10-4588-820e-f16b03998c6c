package cn.mingyang.cloud.center.common.config;

import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * HttpClient配置
 */
@Configuration
public class HttpClientConfig {
    // 连接池配置
    @Bean
    public PoolingHttpClientConnectionManager poolingConnectionManager() {
        PoolingHttpClientConnectionManager manager = new PoolingHttpClientConnectionManager();
        manager.setMaxTotal(20);              // 最大连接数
        manager.setDefaultMaxPerRoute(5);     // 每个路由的最大并发数
        return manager;
    }

    // 请求配置（超时时间）
    @Bean
    public RequestConfig requestConfig() {
        return RequestConfig.custom()
                .setConnectTimeout(5000)       // 连接超时（毫秒）
                .setSocketTimeout(10000)       // 数据传输超时
                .setConnectionRequestTimeout(2000) // 从连接池获取连接的超时
                .build();
    }

    // HttpClient Bean
    @Bean
    public CloseableHttpClient httpClient(
            PoolingHttpClientConnectionManager poolingConnectionManager,
            RequestConfig requestConfig) {
        return HttpClientBuilder.create()
                .setConnectionManager(poolingConnectionManager)
                .setDefaultRequestConfig(requestConfig)
                .build();
    }
}
