package cn.mingyang.cloud.center.common.dao.entity;

import lombok.Data;

/**
 * 功能价格表
 */
@Data
public class FunctionPriceItem {

    /**
     * 主键
     */
    private int id;

    /**
     * 功能id
     */
    private int functionid;

    /**
     * 卖点组
     */
    private int sellgroup;

    /**
     * 消费单元
     */
    private float expend;

    /**
     * 消费单元 1：金币 2：数量 3：次数
     */
    private int expendtype;

    /**
     * 消费提醒
     */
    private String expendhint;

    /**
     * appid
     */
    private String appid;

    /**
     * osid
     */
    private String osid;

    /**
     * 开始userid
     */
    private String startuserid;

    /**
     * 结束userid
     */
    private String enduserid;

    /**
     * 权限类型：
     * 0：非会员可用
     * 1：普通VIP可用
     * 2：仅SVIP可用
     * 3：仅加油包可用
     * 4：VIP和加油包可用
     * 5：SVIP和加油包可用
     */
    private int righttype;

    /**
     * 资源不足错误提醒
     */
    private String errmessage;

    /**
     * 备注
     */
    private String note;

    /**
     * 扣减顺序
     */
    private int order;

    /**
     * 共享卖点组
     */
    private Integer sharesellgroup;
}
