package cn.mingyang.cloud.center.common.util;

import cn.hutool.core.date.DateUtil;
import com.alibaba.druid.util.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

public class DateTimeUtils {
    /**
     * 获取当前时间字符串（精确到秒）
     */
    public static String currentDateTime() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    public static String afterCurrentDateTime(int date) {
        return LocalDateTime.now().plus(Period.ofDays(date)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }


    /**
     * 新增跨步时间到0点
     *
     * @param date 时间
     * @return 时间字符串
     */
    public static String afterCurrentDateTimeZero(int date) {
        return LocalDateTime.now().plus(Period.ofDays(date)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd 00:00:00"));
    }

    public static String afterDateTime(String date, int day) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return LocalDateTime.parse(date, formatter).plus(Period.ofDays(day)).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    /**
     * 获取当前时间字符串（精确到毫秒）
     */
    public static String currentDateTimeStringWithMillis() {
        return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"));
    }

    /**
     * 解析时间字符串（精确到秒）
     */
    public static LocalDateTime parseFromString(String dateTimeStr) {
        if (StringUtils.isEmpty(dateTimeStr)) {
            return null;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 解析为 LocalDateTime
        LocalDateTime dateTime = LocalDateTime.parse(dateTimeStr, formatter);
        return dateTime;
    }

    /**
     * 解析时间字符串（精确到秒）
     */
    public static long dateStringToStamp(String dateTimeStr) {
        if (StringUtils.isEmpty(dateTimeStr)) {
            return 0;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime ldt = LocalDateTime.parse(dateTimeStr, formatter);
            // 转换为系统默认时区的时刻
            return ldt.atZone(ZoneId.systemDefault()).toInstant().getEpochSecond();
        } catch (DateTimeParseException e) {
            return 0;
        }
    }


    /**
     * 比较两个世界，哪个最大取哪个
     *
     * @param exitsDate 已存在
     * @param addDate   新增
     * @return 时间
     */
    public static String compareDate(String exitsDate, String addDate) {
        if (StringUtils.isEmpty(addDate)) {
            return exitsDate;
        }
        if (DateUtil.compare(DateUtil.parseDate(exitsDate), DateUtil.parseDate(addDate)) <= 0) {
            return addDate;
        } else {
            return exitsDate;
        }
    }

    /**
     * 获取今日结束时间的时间戳（毫秒）
     *
     * @return 今日结束时间的时间戳
     */
    public static long getEndOfTodayTimestamp() {
        LocalDateTime endOfDay = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        return endOfDay.toInstant(ZoneOffset.of("+8")).toEpochMilli();
    }

    /**
     * 判断两个日期是否相等
     *
     * @param ordDate 老的时间
     * @param newDate 新的时间
     * @return 结果
     */
    public static boolean compareDateIsSample(String ordDate, String newDate) {
        return DateUtil.compare(DateUtil.parseDate(ordDate), DateUtil.parseDate(newDate)) == 0;
    }


}
