package cn.mingyang.cloud.center.common.util;

import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.input.SAXBuilder;
import org.jdom2.output.XMLOutputter;

import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * Xml工具类
 */
public class XmlUtils {

    /**
     * Map转Xml
     *
     * @param params 参数Map
     * @return XML字符串
     */
    public static String mapToXml(Map<String, String> params) {
        try {
            Element root = new Element("xml");
            Document document = new Document(root);
            
            for (Map.Entry<String, String> entry : params.entrySet()) {
                Element element = new Element(entry.getKey());
                element.setText(entry.getValue());
                root.addContent(element);
            }
            
            XMLOutputter outputter = new XMLOutputter();
            return outputter.outputString(document);
        } catch (Exception e) {
            throw new RuntimeException("生成XML失败", e);
        }
    }

    /**
     * Xml转Map
     *
     * @param xml XML字符串
     * @return Map
     * @throws Exception 解析异常
     */
    public static Map<String, String> parseXmlResponse(String xml) throws Exception {
        SAXBuilder saxBuilder = new SAXBuilder();
        Document document = saxBuilder.build(new ByteArrayInputStream(xml.getBytes("UTF-8")));
        Element root = document.getRootElement();
        Map<String, String> result = new HashMap<>();
        for (Element child : root.getChildren()) {
            result.put(child.getName(), child.getText());
        }
        return result;
    }
}
