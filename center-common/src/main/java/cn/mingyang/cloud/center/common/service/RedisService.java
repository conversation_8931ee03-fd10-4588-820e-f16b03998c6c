package cn.mingyang.cloud.center.common.service;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * Redis 操作类
 */
@Service
public class RedisService {

    private final RedisTemplate<String, Object> redisTemplate;

    public RedisService(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    // 释放锁的 Lua 脚本
    private static final String UNLOCK_SCRIPT =
            "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                    "   return redis.call('del', KEYS[1]) " +
                    "else " +
                    "   return 0 " +
                    "end";

    // ============================ 基础操作 =============================

    /**
     * 设置过期时间
     */
    public boolean expire(String key, long timeout, TimeUnit unit) {
        return redisTemplate.expire(key, timeout, unit);
    }

    /**
     * 获取剩余过期时间（秒）
     */
    public Long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 判断key是否存在
     */
    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    /**
     * 删除单个key
     */
    public Boolean delete(String key) {
        return redisTemplate.delete(key);
    }

    /**
     * 批量删除key
     */
    public Long delete(Collection<String> keys) {
        return redisTemplate.delete(keys);
    }

    // ============================ String 类型 =============================

    /**
     * 设置字符串值（无过期时间）
     */
    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 设置字符串值（带过期时间）
     */
    public void set(String key, Object value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    /**
     * 获取字符串值
     */
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 获取字符串值
     */
    public String getString(String key) {
        redisTemplate.setValueSerializer(new StringRedisSerializer());
        Object value = redisTemplate.opsForValue().get(key);
        if (value == null) {
            return "";
        }
        if (value instanceof String) {
            return (String) value;
        }
        if (value instanceof byte[]) {
            return new String((byte[]) value);
        }
        return value.toString();
    }


    /**
     * 递增（针对数值类型）
     */
    public Long incr(String key, long delta) {
        return redisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * 递减（针对数值类型）
     */
    public Long decr(String key, long delta) {
        return redisTemplate.opsForValue().increment(key, -delta);
    }

    // ============================ Hash 类型 =============================

    /**
     * 设置哈希字段值（无过期时间）
     */
    public void hSet(String key, String field, Object value) {
        redisTemplate.opsForHash().put(key, field, value);
    }

    /**
     * 设置哈希字段值（带过期时间）
     */
    public void hSet(String key, String field, Object value, long timeout, TimeUnit unit) {
        redisTemplate.opsForHash().put(key, field, value);
        expire(key, timeout, unit);
    }

    /**
     * 获取哈希字段值
     */
    public Object hGet(String key, String field) {
        return redisTemplate.opsForHash().get(key, field);
    }

    /**
     * 获取哈希所有字段和值
     */
    public Map<Object, Object> hGetAll(String key) {
        return redisTemplate.opsForHash().entries(key);
    }

    /**
     * 删除哈希字段
     */
    public Long hDelete(String key, Object... fields) {
        return redisTemplate.opsForHash().delete(key, fields);
    }

    // ============================ List 类型 =============================

    /**
     * 左插入元素（无过期时间）
     */
    public Long lLeftPush(String key, Object value) {
        return redisTemplate.opsForList().leftPush(key, value);
    }

    /**
     * 左插入元素（带过期时间）
     */
    public Long lLeftPush(String key, Object value, long timeout, TimeUnit unit) {
        Long count = redisTemplate.opsForList().leftPush(key, value);
        expire(key, timeout, unit);
        return count;
    }

    /**
     * 右插入元素（无过期时间）
     */
    public Long lRightPush(String key, Object value) {
        return redisTemplate.opsForList().rightPush(key, value);
    }

    /**
     * 右插入元素（带过期时间）
     */
    public Long lRightPush(String key, Object value, long timeout, TimeUnit unit) {
        Long count = redisTemplate.opsForList().rightPush(key, value);
        expire(key, timeout, unit);
        return count;
    }

    /**
     * 获取列表指定范围元素（start=0, end=-1表示获取全部）
     */
    public List<Object> lRange(String key, long start, long end) {
        return redisTemplate.opsForList().range(key, start, end);
    }

    /**
     * 获取列表长度
     */
    public Long lSize(String key) {
        return redisTemplate.opsForList().size(key);
    }

    /**
     * 弹出左侧元素
     */
    public Object lLeftPop(String key) {
        return redisTemplate.opsForList().leftPop(key);
    }

    /**
     * 弹出右侧元素
     */
    public Object lRightPop(String key) {
        return redisTemplate.opsForList().rightPop(key);
    }

    // ============================ Set 类型 =============================

    /**
     * 添加集合元素（无过期时间）
     */
    public Long sAdd(String key, Object... values) {
        return redisTemplate.opsForSet().add(key, values);
    }

    /**
     * 添加集合元素（带过期时间）
     */
    public Long sAddWithExpire(String key, long timeout, TimeUnit unit, Object... values) {
        Long count = redisTemplate.opsForSet().add(key, values);
        expire(key, timeout, unit);
        return count;
    }

    /**
     * 获取集合所有元素
     */
    public Set<Object> sMembers(String key) {
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 获取集合所有元素
     */
    public Set<Object> sMembersByString(String key) {
        redisTemplate.setValueSerializer(new StringRedisSerializer());
        return redisTemplate.opsForSet().members(key);
    }

    /**
     * 判断元素是否在集合中
     */
    public Boolean sIsMember(String key, Object value) {
        return redisTemplate.opsForSet().isMember(key, value);
    }

    /**
     * 删除集合元素
     */
    public Long sRemove(String key, Object... values) {
        return redisTemplate.opsForSet().remove(key, values);
    }

    // ============================ ZSet 类型 =============================

    /**
     * 添加有序集合元素（无过期时间）
     */
    public Boolean zAdd(String key, Object value, double score) {
        return redisTemplate.opsForZSet().add(key, value, score);
    }

    /**
     * 添加有序集合元素（带过期时间）
     */
    public Boolean zAdd(String key, Object value, double score, long timeout, TimeUnit unit) {
        Boolean result = redisTemplate.opsForZSet().add(key, value, score);
        expire(key, timeout, unit);
        return result;
    }

    /**
     * 获取有序集合指定分数范围的元素（升序）
     */
    public Set<Object> zRangeByScore(String key, double min, double max) {
        return redisTemplate.opsForZSet().rangeByScore(key, min, max);
    }

    /**
     * 获取有序集合元素的排名（升序，0表示第一名）
     */
    public Long zRank(String key, Object value) {
        return redisTemplate.opsForZSet().rank(key, value);
    }

    /**
     * 获取有序集合元素的分数
     */
    public Double zScore(String key, Object value) {
        return redisTemplate.opsForZSet().score(key, value);
    }

    /**
     * 删除有序集合元素
     */
    public Long zRemove(String key, Object... values) {
        return redisTemplate.opsForZSet().remove(key, values);
    }

    // ============================ Redis Lock 类型 =============================

    /**
     * 尝试获取锁
     *
     * @return 是否获取成功
     */
    public boolean tryLock(String lockKey, String lockValue, long seconds) {
        Boolean result = redisTemplate.opsForValue().setIfAbsent(
                lockKey,
                lockValue,
                seconds,
                TimeUnit.SECONDS
        );
        return Boolean.TRUE.equals(result);
    }

    /**
     * 释放锁
     *
     * @return 是否释放成功
     */
    public boolean unlock(String lockKey, String lockValue) {
        DefaultRedisScript<Long> script = new DefaultRedisScript<>(UNLOCK_SCRIPT, Long.class);
        Long result = redisTemplate.execute(script, Collections.singletonList(lockKey), lockValue);
        return result == 1;
    }


}