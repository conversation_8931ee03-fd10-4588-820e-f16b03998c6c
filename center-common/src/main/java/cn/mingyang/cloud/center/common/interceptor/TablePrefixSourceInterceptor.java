package cn.mingyang.cloud.center.common.interceptor;

import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.core.toolkit.PluginUtils;
import com.baomidou.mybatisplus.extension.parser.JsqlParserSupport;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.springframework.stereotype.Component;

import java.sql.Connection;


/**
 * 表名前缀替换
 */
@Setter
@Getter
@Component
@Slf4j
public class TablePrefixSourceInterceptor extends JsqlParserSupport implements InnerInterceptor {

    /**
     * 默认数据源
     */
    private String defaultDataSource;

    /**
     * 修改表名前缀（拦截所有SQL，包括增删改查）
     */
    @Override
    public void beforePrepare(StatementHandler sh, Connection connection, Integer transactionTimeout) {
        // 获取当前数据源前缀
        String dataSourceKey = DynamicDataSourceContextHolder.peek();
        // 如果当前数据源为空，则获取配置的默认数据源
        if (null == dataSourceKey || dataSourceKey.isEmpty()) {
            dataSourceKey = this.getDefaultDataSource();
        }
        PluginUtils.MPBoundSql mpBs = PluginUtils.mpBoundSql(sh.getBoundSql());
        String originSql = mpBs.sql();
        // 替换 db_ 为当前数据源前缀
        String newSql = originSql;
        if (dataSourceKey != null && !dataSourceKey.isEmpty() && originSql.toLowerCase().contains("db_")) {
            newSql = originSql.replaceAll("(?i)db_", dataSourceKey + "_biz_");
        }
        if (dataSourceKey != null && !dataSourceKey.isEmpty() && originSql.toLowerCase().contains("org_")) {
            newSql = originSql.replaceAll("(?i)org_", dataSourceKey + "_");
        }
        mpBs.sql(newSql);
    }

}
