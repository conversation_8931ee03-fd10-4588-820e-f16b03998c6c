package cn.mingyang.cloud.center.common.util;

import cn.mingyang.cloud.center.common.comEnum.BaseErrorCodeEnum;
import cn.mingyang.cloud.center.common.comEnum.CommandType;
import cn.mingyang.cloud.center.common.exception.MyBaseException;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import com.alibaba.druid.util.StringUtils;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 工具类
 */
public class MyUtils {


    /**
     * 校验CVAT
     *
     * @param cmd        命令请求
     * @param comRequest 请求消息
     */
    public static void checkCommand(CmdRequest cmd, ComRequest comRequest) {
        CommandType commandType = CommandType.fromCode(cmd.getCno());
        if (commandType == null) {
            throw new MyBaseException(BaseErrorCodeEnum.INVALID_ARGUMENT.getCode(),
                    "没有找到相匹配的接口号，当前请求接口号： " + cmd.getCno());
        }
        //对接口号和cmd进行校验
        CommandType expectedType;
        try {
            expectedType = CommandType.valueOf(comRequest.getCmd());
            if (expectedType != commandType) {
                throw new MyBaseException(BaseErrorCodeEnum.INVALID_ARGUMENT.getCode(),
                        String.format("接口号与cmd不匹配，路径：%s(%s)，参数：%s(%s)",
                                commandType.name(), commandType.getCode(),
                                expectedType.name(), expectedType.getCode()));
            }
        } catch (IllegalArgumentException e) {
            throw new MyBaseException(BaseErrorCodeEnum.INVALID_ARGUMENT.getCode(), "无效的接口类型：" + comRequest.getCmd());
        }
        // 写入数据源
        if (!StringUtils.isEmpty(comRequest.getBase().getPid())) {
            cmd.setDs(comRequest.getBase().getPid());
        }
    }


    /**
     * 解析URL参数
     */
    public static Map<String, String> parseParams(String url) throws URISyntaxException {
        URI uri = new URI(url);
        String query = uri.getQuery();
        Map<String, String> params = new HashMap<>();
        if (query != null) {
            for (String param : query.split("&")) {
                String[] parts = param.split("=");
                if (parts.length == 2) {
                    params.put(parts[0], parts[1]);
                }
            }
        }
        return params;
    }

    /**
     * 替换URL参数并生成新URL
     */
    public static String replaceParams(String baseUrl, Map<String, String> newParams) throws URISyntaxException {
        URI uri = new URI(baseUrl);
        Map<String, String> originalParams = parseParams(baseUrl);
        // 覆盖原有参数
        originalParams.putAll(newParams);
        // 定义参数的排序顺序
        List<String> paramOrder = Arrays.asList("c", "v", "a", "t");
        // 构建新的查询字符串，按照指定顺序排序
        String query = originalParams.entrySet().stream()
                .sorted((e1, e2) -> {
                    int idx1 = paramOrder.indexOf(e1.getKey());
                    int idx2 = paramOrder.indexOf(e2.getKey());
                    if (idx1 != -1 && idx2 != -1) {
                        return Integer.compare(idx1, idx2);
                    } else if (idx1 != -1) {
                        return -1;
                    } else if (idx2 != -1) {
                        return 1;
                    } else {
                        return e1.getKey().compareTo(e2.getKey());
                    }
                })
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&"));
        // 重构URL
        return uri.getScheme() + "://" + uri.getAuthority() + uri.getPath() + "?" + query;
    }


    /**
     * 合并属性值
     *
     * @param newObj 新版本参数
     * @param oldObj 旧版本参数
     * @return 比较结果
     */
    public Map<String, Object> mergeObjects(Map<String, Object> newObj, Map<String, Object> oldObj) {
        // 先添加旧对象的所有属性
        Map<String, Object> merged = new HashMap<>(oldObj);
        // 遍历新对象，更新已有的属性或添加新属性
        for (Map.Entry<String, Object> entry : newObj.entrySet()) {
            String key = entry.getKey();
            Object newValue = entry.getValue();
            // 如果新值非空，则使用新值覆盖旧值
            if (newValue != null && !"".equals(newValue)) {
                merged.put(key, newValue);
            }
        }
        return merged;
    }

}
