package cn.mingyang.cloud.center.common.request;

import lombok.Data;

import javax.validation.constraints.Min;

/**
 * 分页基础请求体
 */
@Data
public class PageRequest {
    /**
     * 当前页码，默认1
     */
    @Min(value = 1, message = "pageNum必须大于等于1")
    private Integer pageNum = 1;

    /**
     * 每页大小，默认10
     */
    @Min(value = 1, message = "pageSize必须大于等于1")
    private Integer pageSize = 10;

    /**
     * 排序字段，可选
     */
    private String sortField;

    /**
     * 排序方向，asc/desc，默认desc
     */
    private String sortOrder = "desc";

    /**
     * 总条数
     */
    private Integer total;

    /**
     * 总页数
     */
    private Integer totalPages;

    /**
     * 起始值
     */
    private Integer offset;

    /**
     * 获取分页查询的起始行（offset）
     */
    public int getOffset() {
        return (pageNum - 1) * pageSize;
    }

    /**
     * 计算总页数
     *
     * @param total 总记录数
     * @return 总页数
     */
    public int getTotalPages(int total) {
        if (pageSize == null || pageSize <= 0) return 0;
        return (total + pageSize - 1) / pageSize;
    }
} 