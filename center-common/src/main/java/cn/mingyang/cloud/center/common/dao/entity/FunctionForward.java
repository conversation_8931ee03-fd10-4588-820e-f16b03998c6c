package cn.mingyang.cloud.center.common.dao.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * 转发实体类
 */
@Data
public class FunctionForward implements Serializable {

    /**
     * ID
     */
    private Integer id;

    /**
     * 主功能（chat、ppt等）
     */
    private String mainFunction;

    /**
     * 新版功能编号
     */
    private String newCNo;

    /**
     * 转发URL
     */
    private String forwardUrl;

    /**
     * 老板功能编号
     */
    private String oldCNo;

    /**
     * 操作系统
     */
    private String osId;

    /**
     * 渠道
     */
    private String df;

    /**
     * 老版本参数
     */
    private String oldParam;

    /**
     * 模拟值
     */
    private String mockParam;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
