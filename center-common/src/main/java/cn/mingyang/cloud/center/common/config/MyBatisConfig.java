package cn.mingyang.cloud.center.common.config;


import cn.mingyang.cloud.center.common.interceptor.TablePrefixSourceInterceptor;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MyBatis配置
 */
@Getter
@Setter
@Configuration
@Slf4j
@ConfigurationProperties(prefix = "spring.datasource.dynamic")
public class MyBatisConfig {

    private String primary;

    /**
     * 表前缀变更拦截器
     *
     * @param mybatisPlusInterceptor 默认拦截器
     * @return 自定义拦截器
     */
    @Bean
    public TablePrefixSourceInterceptor myInterceptor(MybatisPlusInterceptor mybatisPlusInterceptor) {
        log.info("注册拦截器: TablePrefixSourceInterceptor");
        TablePrefixSourceInterceptor sourceInterceptor = new TablePrefixSourceInterceptor();
        mybatisPlusInterceptor.addInnerInterceptor(sourceInterceptor);
        sourceInterceptor.setDefaultDataSource(this.primary);
        return sourceInterceptor;
    }

}
