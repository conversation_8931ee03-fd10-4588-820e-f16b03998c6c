package cn.mingyang.cloud.center.common.util;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.comEnum.BaseErrorCodeEnum;
import cn.mingyang.cloud.center.common.exception.MyBaseException;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import javax.validation.Validator;
import java.util.Set;

/**
 * 校验工具类
 */
@Component
public class ValidateUtils {

    private final Validator validator;

    public ValidateUtils(Validator validator) {
        this.validator = validator;
    }

    /**
     * 通用参数校验方法
     *
     * @param param 请求参数JSON字符串
     * @param clazz 参数类型
     * @param <T>   参数类型泛型
     * @return 校验后的参数对象
     */
    public <T> T validateParam(JSONObject param, Class<T> clazz) {
        if (null == param) {
            throw new MyBaseException(BaseErrorCodeEnum.INVALID_ARGUMENT.getCode(), "请求参数不能为空");
        }
        T request = JSONUtil.toBean(param, clazz);
        Set<ConstraintViolation<T>> violations = validator.validate(request);
        if (!violations.isEmpty()) {
            ConstraintViolationException violationException = new ConstraintViolationException(violations);
            throw new MyBaseException(BaseErrorCodeEnum.INVALID_ARGUMENT.getCode(), violationException.getMessage());
        }
        return request;
    }

}
