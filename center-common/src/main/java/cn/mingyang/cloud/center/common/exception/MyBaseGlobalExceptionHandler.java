package cn.mingyang.cloud.center.common.exception;

import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.framework.web.core.handler.GlobalExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 基础服务全局异常
 */
@RestControllerAdvice
public class MyBaseGlobalExceptionHandler extends GlobalExceptionHandler {

    public MyBaseGlobalExceptionHandler(@Value("${spring.application.name}") String applicationName) {
        super(applicationName);
    }

    /**
     * 捕获异常，并返回
     *
     * @param e 异常信息
     * @return 通用返回体
     */

    @ExceptionHandler(MyBaseException.class)
    public CommonResult handleCustomException(MyBaseException e) {
        return CommonResult.error(e.getCode(), e.getMessage());
    }

}
