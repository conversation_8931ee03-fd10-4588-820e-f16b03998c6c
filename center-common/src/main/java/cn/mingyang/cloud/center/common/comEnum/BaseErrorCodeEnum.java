package cn.mingyang.cloud.center.common.comEnum;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 系统级错误码
 */
@Getter
@AllArgsConstructor
public enum BaseErrorCodeEnum {
    // 系统级错误 (200001-200200)
    UNKNOWN_ERROR("200001", "system", "未知异常"),

    // 限流相关错误 (200201-200203)
    USER_RATE_LIMIT_EXCEEDED("200002", "system", "用户配额超限"),
    RPM_RATE_LIMIT_EXCEEDED("200003", "system", "RPM超限额"),
    TPM_RATE_LIMIT_EXCEEDED("200004", "system", "TPM超限额"),

    // 系统内部错误 (200204-200210)
    INTERNAL_ERROR("200005", "system", "服务内部错误，请稍后重试"),
    METHOD_NOT_SUPPORTED("200006", "system", "此接口只支持POST请求"),
    CHARACTERS_TOO_LONG("200007", "system", "请求长度不能超过最大字符限制"),
    INVALID_ARGUMENT("200008", "system", "入参格式有误"),

    // 用户中心类错误
    NEED_MOBILE_PHONE("200101", "user", "注册需要用户提供手机号"),
    INVAILD_MOBILE_PHONE("200102", "user", "无效的手机号"),
    INVAILD_DYNAMIC_CODE("200103", "user", "动态验证码错误"),
    USERNAME_OR_PASSWORD_ERROR("200104", "user", "用户名或密码错误"),
    UNREGISTER_ERROR("200105", "user", "注销失败"),
    USER_UPDATE_ERROR("200106", "user", "用户更新失败"),
    USER_EMPTY("200107", "user", "未查询到用户信息"),
    RIGHTS_EXIST("200109", "user", "新会员赠送权益，不能重复新增"),
    NULL_FUNCTION_EXIST("200111", "user", "没有查询到相应的功能价格信息"),
    QUERY_MEDIA_ERROR("200112", "media", "查询云媒体异常"),
    DEL_MEDIA_ERROR("200113", "media", "删除云媒体异常"),
    NULL_RIGHT_ERROR("200114", "user", "没有查询到可用卖点信息"),


    // 商品类错误
    GET_PRODUCT_ERROR("200201", "product", "查询商品信息异常"),
    NULL_PRODUCT_ERROR("200202", "product", "未查询到商品信息"),
    NULL_PRODUCT_COUPON("200203", "product", "未查询到商品优惠券"),
    GET_PRODUCT_COUPON_ERROR("200204", "product", "查询商品优惠券异常"),


    // 应用控制类错误
    NULL_CONTROLLER("200301", "app", "未查询到用户控制信息"),
    CONFIG_LOAD_FAILED("200302", "app", "应用配置加载失败"),

    // 支付下单类错误
    PAY_ERROR("200401", "pay", "支付失败"),
    PAY_USER_ERROR("200402", "pay", "支付账户异常"),
    DUPLICATE_PAYMENT("200403", "pay", "重复支付（订单已支付）"),
    ORDER_NOT_FOUND("200404", "pay", "订单不存在"),
    PAYMENT_TIMEOUT("200405", "pay", "支付超时"),

    // 转发异常错误码
    FORWARD_NULL("200902", "forward", "未查询到转发配置信息！"),
    FORWARD_ERROR("200903", "forward", "转发请求失败！");


    private final String code;
    private final String type;
    private final String message;

}
