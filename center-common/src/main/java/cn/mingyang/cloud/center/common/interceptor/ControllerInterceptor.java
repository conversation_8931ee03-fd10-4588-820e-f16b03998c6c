package cn.mingyang.cloud.center.common.interceptor;

import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import cn.mingyang.cloud.center.common.config.MyBatisConfig;
import cn.mingyang.cloud.center.common.exception.MyBaseException;
import cn.mingyang.cloud.center.common.request.CmdRequest;
import cn.mingyang.cloud.center.common.request.ComRequest;
import cn.mingyang.cloud.center.common.util.MyUtils;
import cn.mingyang.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * 自定义拦截器
 */
@Slf4j
@Component
public class ControllerInterceptor implements HandlerInterceptor {

    private final MyBatisConfig batisConfig;

    public ControllerInterceptor(MyBatisConfig batisConfig) {
        this.batisConfig = batisConfig;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        //解析CVAT
        CmdRequest cmdRequest = this.parseParamVersion(request.getRequestURI().substring(request.getRequestURI().lastIndexOf('/') + 1), request);
        //校验接口号和CMD
        this.checkUrlParam(cmdRequest, request);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                                Object handler, Exception ex) {
    }


    /**
     * 打印回调参数
     *
     * @param urlPath 回调路径
     * @param request 请求
     */
    private void printCallBackParams(String urlPath, HttpServletRequest request) {
        if (!StringUtil.isBlank(urlPath) && urlPath.contains("/callback")) {
            try {
                // 读取请求体内容
                StringBuilder sb = new StringBuilder();
                BufferedReader reader = request.getReader();
                String line;
                while ((line = reader.readLine()) != null) {
                    sb.append(line);
                }
                if (StringUtils.isEmpty(sb)) {
                    TreeMap<String, String> map = new TreeMap<String, String>();
                    Map reqMap = request.getParameterMap();
                    for (Object key : reqMap.keySet()) {
                        String value = ((String[]) reqMap.get(key))[0];
                        map.put(key.toString(), value);
                    }
                    log.info("回调参数打印，路径：{} 参数： {}", urlPath, map);
                } else {
                    log.info("回调参数打印，路径：{} 参数： {}", urlPath, sb);
                }
            } catch (Exception e) {
                log.warn("获取回调参数失败", e);
            }
        }
    }

    /**
     * 解析CVAT
     *
     * @param urlPath 请求路径
     * @return
     */
    private CmdRequest parseParamVersion(String urlPath, HttpServletRequest request) {
        CmdRequest cmdRequest = null;
        Map<String, String> params = new HashMap<>();
        if (!StringUtil.isBlank(urlPath)) {
            String[] paramPairs = urlPath.split("&");
            for (String pair : paramPairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    params.put(keyValue[0], keyValue[1]);
                }
            }
            if (MapUtil.isNotEmpty(params)) {
                // 校验CVAT参数是否都存在
                String c = params.get("c");
                String v = params.get("v");
                String a = params.get("a");
                String t = params.get("t");
                String ds = params.get("db");

                // 判断网关是否给出数据源参数,如果没有则使用默认数据源
                if (StringUtils.isEmpty(ds)) {
                    ds = batisConfig.getPrimary();
                }
                if (StringUtils.isEmpty(c) || StringUtils.isEmpty(v) ||
                        StringUtils.isEmpty(a) || StringUtils.isEmpty(t)) {
                    //throw new MyBaseException(String.valueOf(SysErrorCodeEnum.INVALID_ARGUMENT.getCode()),"缺少必要的CVAT参数，当前参数：c=" + c + ", v=" + v + ", a=" + a + ", t=" + t);
                    return null;
                }
                request.setAttribute("cmdParam", new CmdRequest(c, v, a, t, ds));
                cmdRequest = new CmdRequest(c, v, a, t, ds);
            }
        }
        return cmdRequest;
    }

    /**
     * 路径参数校验
     *
     * @param cmdRequest 路径参数
     * @param request    请求参数
     */
    private void checkUrlParam(CmdRequest cmdRequest, HttpServletRequest request) {
        if ("POST".equalsIgnoreCase(request.getMethod())
                && request.getContentType() != null
                && request.getContentType().contains("application/json")
                && null != cmdRequest) {
            try {
                // 读取请求体内容
                StringBuilder sb = new StringBuilder();
                BufferedReader reader = request.getReader();
                String line;
                while ((line = reader.readLine()) != null) {
                    sb.append(line);
                }
                ComRequest comRequest = JSONUtil.toBean(sb.toString(), ComRequest.class);
                MyUtils.checkCommand(cmdRequest, comRequest);
                request.setAttribute("cmdParam", cmdRequest);
            } catch (MyBaseException e) {
                throw e;
            } catch (Exception e) {
                log.warn("Check url param error ", e);
                throw new MyBaseException(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), GlobalErrorCodeConstants.INTERNAL_ERROR.getMsg());
            }
        }
    }
}
