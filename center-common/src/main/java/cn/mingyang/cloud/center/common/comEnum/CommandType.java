package cn.mingyang.cloud.center.common.comEnum;

import lombok.Getter;

/**
 * 接口号枚举
 */
@Getter
public enum CommandType {
    //用户
    SendAuthCode("10001", "发送短信验证码"),
    ThirdPartyLogin("10002", "统一注册 & 登录"),
    UserUpdate("10003", "用户信息更新"),
    GetUserInfo("10004", "用户信息"),
    UserUnregister("10005", "用户注销"),
    AliGetPhoneNum("10007", "手机号码一键登录"),
    VisitorLogin("10008", "游客登录"),
    //权益
    GetUserRight("10006", "用户权益信息"),
    AddUserRights("10082", "新增用户权益"),
    ExpendUserRights("10083", "扣减用户权益"),
    CheckUserRights("10086", "校验用户权益"),
    GetAllFunctionRights("10087", "获取功能权益消费表"),
    ResetUserRights("10088", "重置用户权益"),
    SyncUserRights("10089", "用户权益同步和转移"),
    UserPolicyInfo("10090", "老权益-用户权益"),
    UserPolicyConsumeRecord("10091", "老权益-权益消耗分页列表"),
    DeleteUserRights("10092", "删除用户权益"),
    UserRightRecord("10093", "用户权益记录"),
    //商品
    GetProductInfo("10201", "商品列表"),
    GetCouponInfo("10202", "商品优惠券"),
    BuyProduct("10203", "购买商品"),
    NewUserGift("10204", "新注册用户赠送权限"),
    //应用控制
    GrayControl("10301", "应用自定义参数"),
    //支付
    WxUnifiedOrder("10401", "统一下单 (微信)"),
    AliUnifiedOrder("10402", "统一下单 (支付宝)"),
    GetBills("10403", "订单记录"),
    UnifiedOrder("10404", "统一下单"),
    UnionUnifiedOrder("10405", "统一下单（通联）"),
    ApplePayVerify("10406", "苹果支付验签"),
    PaymentSubscribeList("10407", "支付订阅列表"),
    PaymentSubscribeUnsign("10408", "支付订阅取消"),
    //腾讯云媒体
    GetTencentMedia("10060", "查询腾讯云媒体"),
    DelTencentMedia("10061", "删除腾讯云媒体"),
    //资源
    AiIdPhoto("11001", "AI证件照"),
    AiPortrait("11002", "AI写真"),
    AiHairStylis("11003", "AI发型师"),
    ImgDanceVideoTemplate("11004", "图生舞蹈"),
    //其他
    SendAdvice("10501", "意见反馈"),
    CreateQyQrCode("10502", "企微活码 (客服)"),
    ;

    private final String code;

    private final String desc;

    CommandType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CommandType fromCode(String code) {
        for (CommandType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }

}
