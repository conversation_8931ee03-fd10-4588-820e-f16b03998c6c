package cn.mingyang.cloud.center.common.util;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Map;

/**
 * Map工具类
 */
public class MapUtils {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 对象转Map
     *
     * @param obj 对象
     * @return Map
     */
    public static Map convertToMap(Object obj) {
        return objectMapper.convertValue(obj, Map.class);
    }

}
