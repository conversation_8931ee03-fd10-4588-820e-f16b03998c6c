package cn.mingyang.cloud.center.common.util;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static cn.hutool.crypto.SecureUtil.md5;

/**
 * 签名辅助类
 */
@Slf4j
public class AesUtils {


    /**
     * 生成微信支付V2签名（MD5）
     */
    public static String generateSign(Map<String, String> params, String apiKey) {
        // 1. 参数按ASCII排序
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        // 2. 拼接键值对
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            String value = params.get(key);
            if (value != null && !value.isEmpty() && !"sign".equals(key)) {
                sb.append(key).append("=").append(value).append("&");
            }
        }
        // 3. 附加API密钥并MD5加密
        sb.append("key=").append(apiKey);
        return md5(sb.toString()).toUpperCase();
    }

}

