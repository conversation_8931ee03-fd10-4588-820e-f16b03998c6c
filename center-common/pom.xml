<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.mingyang.cloud</groupId>
        <artifactId>usercenter</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>center-common</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>



    <dependencies>
        <!--common基础类-->
        <dependency>
            <groupId>cn.mingyang.cloud</groupId>
            <artifactId>cloud-framework-common</artifactId>
        </dependency>
        <!-- Spring Cloud 基础 -->
        <dependency>
            <groupId>cn.mingyang.cloud</groupId>
            <artifactId>cloud-framework-env</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.mingyang.cloud</groupId>
            <artifactId>cloud-framework-web</artifactId>
        </dependency>
        <!-- DB 相关 -->
        <dependency>
            <groupId>cn.mingyang.cloud</groupId>
            <artifactId>cloud-framework-mybatis</artifactId>
        </dependency>
        <!-- RPC 远程调用相关 -->
        <dependency>
            <groupId>cn.mingyang.cloud</groupId>
            <artifactId>cloud-framework-rpc</artifactId>
        </dependency>
        <!-- redis -->
        <dependency>
            <groupId>cn.mingyang.cloud</groupId>
            <artifactId>cloud-framework-redis</artifactId>
        </dependency>
        <!-- Registry 注册中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>
        <!-- Config 配置中心相关 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <!-- 监控相关 -->
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
        </dependency>
        <!-- java8 不需要添加，高版本需要添加 -->
        <dependency>
            <groupId>javax.xml.bind</groupId>
            <artifactId>jaxb-api</artifactId>
            <version>2.3.0</version>
        </dependency>
        <!--阿里rocketmq-->
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>ons-client</artifactId>
            <!--建议替换为Java SDK的最新版本号-->
            <version>1.9.0.Final</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- XML解析（JDOM） -->
        <dependency>
            <groupId>org.jdom</groupId>
            <artifactId>jdom2</artifactId>
            <version>2.0.6.1</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>