feign:
  hystrix:
    enabled: true  # 明确启用Hystrix集成
  client:
    config:
      default:
        connectTimeout: 5000
        readTimeout: 5000

hystrix:
  command:
    default:
      execution:
        isolation:
          strategy: THREAD  # 显式声明线程隔离策略
          thread:
            timeoutInMilliseconds: 6000  # 必须 > Feign的readTimeout(5000)
      circuitBreaker:
        requestVolumeThreshold: 20
        errorThresholdPercentage: 50
        sleepWindowInMilliseconds: 5000
      fallback:
        enabled: true
      metrics:
        rollingStats:
          timeInMilliseconds: 10000

    # 合并同类超时配置
    ProductApi#getProductInfo,ProductApi#buyProduct:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 5000  # 特殊短超时方法1

    RightsApi#getUserRight,RightsApi#expendUserRights:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 8000  # 特殊长超时方法2