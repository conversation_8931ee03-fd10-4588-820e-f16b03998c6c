package cn.mingyang.cloud.usercenter.api.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * 命令请求
 */
@Data
public class FeignCmdRequest {

    /**
     * 应用ID
     */
    @NotBlank(message = "应用ID不能为空, appId")
    private String appId;

    /**
     * 版本号
     */
    private String version;

    /**
     * 时间戳
     */
    private String timestamp;

    /**
     * 数据库前缀
     */
    @NotBlank(message = "数据库前缀不能为空, ds")
    private String ds;

    public FeignCmdRequest() {
    }

    public FeignCmdRequest(String appId, String timestamp, String ds) {
        this.appId = appId;
        this.timestamp = timestamp;
        this.ds = ds;
    }
}
