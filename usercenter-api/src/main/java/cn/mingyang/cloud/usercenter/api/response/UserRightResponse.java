package cn.mingyang.cloud.usercenter.api.response;


import cn.hutool.json.JSONArray;
import lombok.Data;

/**
 * 用户权益
 */
@Data
public class UserRightResponse {

    /**
     * 用户ID
     */
    private String userid;

    /**
     * 金币数
     */
    private float coins;

    /**
     * 数量
     */
    private float number;

    /**
     * 次数
     */
    private float times;

    /**
     * vip 到期时间字符串
     */
    private String vipendtime;

    /**
     * vip 到期时间时间戳（秒）
     */
    private Long vipendtimeStamp;

    /**
     * 超级会员到期时间字符串
     */
    private String svipendtime;


    /**
     * 超级会员到期时间时间戳（秒）
     */
    private Long svipendtimeStamp;

    /**
     * VIP 状态
     */
    private Integer vipstatus;

    /**
     * 权益金币明细
     */
    private JSONArray coinsdetail;

    /**
     * 权益数量明细
     */
    private JSONArray numberdetail;

    /**
     * 权益数量明细
     */
    private JSONArray timesdetail;


    /**
     * 获取VIP状态 -1 非会员 0 之前是会员 1 会员
     *
     * @return 状态
     */
    public Integer getVipstatus() {
        if (null == this.vipendtimeStamp || this.vipendtimeStamp == 0) {
            return -1;
        }
        if ((System.currentTimeMillis() / 1000) > this.vipendtimeStamp) {
            return 0;
        } else {
            return 1;
        }
    }
}
