package cn.mingyang.cloud.usercenter.api;

import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.api.fallback.ProductApiFallback;
import cn.mingyang.cloud.usercenter.api.request.FeignRequest;
import cn.mingyang.cloud.usercenter.api.response.ProductInfoResponse;
import cn.mingyang.cloud.usercenter.api.response.UserRightResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 商品接口API
 */
@FeignClient(name = "usercenter-biz", path = "/biz/inner/product", fallbackFactory = ProductApiFallback.class)
public interface ProductApi {

    /**
     * 获取商品列表
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10201&*")
    CommonResult<List<ProductInfoResponse>> getProductInfo(@RequestBody @Valid FeignRequest feignRequest);

    /**
     * 购买商品
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10203&*")
    CommonResult<UserRightResponse> buyProduct(@RequestBody @Valid FeignRequest feignRequest);

    /**
     * 新注册用户赠送权限
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10204&*")
    CommonResult<UserRightResponse> newUserGift(@RequestBody @Valid FeignRequest feignRequest);
} 