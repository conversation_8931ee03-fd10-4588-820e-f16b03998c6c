package cn.mingyang.cloud.usercenter.api.fallback;

import cn.mingyang.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.api.ProductApi;
import cn.mingyang.cloud.usercenter.api.request.FeignRequest;
import cn.mingyang.cloud.usercenter.api.response.ProductInfoResponse;
import cn.mingyang.cloud.usercenter.api.response.UserRightResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 商品接口熔断器
 */
@Slf4j
@Component
public class ProductApiFallback implements FallbackFactory<ProductApi> {

    @Override
    public ProductApi create(Throwable throwable) {
        return new ProductApi() {
            @Override
            public CommonResult<List<ProductInfoResponse>> getProductInfo(FeignRequest feignRequest) {
                log.error("获取商品列表失败，触发熔断", throwable);
                return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "获取商品列表失败，服务暂时不可用");
            }

            @Override
            public CommonResult<UserRightResponse> buyProduct(FeignRequest feignRequest) {
                log.error("购买商品失败，触发熔断", throwable);
                return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "购买商品失败，服务暂时不可用");
            }

            @Override
            public CommonResult<UserRightResponse> newUserGift(FeignRequest feignRequest) {
                log.error("新用户赠送权限失败，触发熔断", throwable);
                return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "新用户赠送权限失败，服务暂时不可用");
            }
        };
    }
} 