package cn.mingyang.cloud.usercenter.api.fallback;

import cn.mingyang.cloud.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.api.RightsApi;
import cn.mingyang.cloud.usercenter.api.request.FeignRequest;
import cn.mingyang.cloud.usercenter.api.response.UserRightResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 权益接口熔断器
 */
@Slf4j
@Component
public class RightsApiFallback implements FallbackFactory<RightsApi> {

    @Override
    public RightsApi create(Throwable throwable) {
        return new RightsApi() {
            @Override
            public CommonResult<UserRightResponse> getUserRight(FeignRequest feignRequest) {
                log.error("获取用户权益失败，触发熔断", throwable);
                return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "获取用户权益失败，服务暂时不可用");
            }

            @Override
            public CommonResult<?> expendUserRights(FeignRequest feignRequest) {
                log.error("扣除权益失败，触发熔断", throwable);
                return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "扣除权益失败，服务暂时不可用");
            }

            @Override
            public CommonResult<?> checkUserRights(FeignRequest feignRequest) {
                log.error("校验用户权益失败，触发熔断", throwable);
                return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "校验用户权益失败，服务暂时不可用");
            }

            @Override
            public CommonResult<?> getAllFunctionRights(FeignRequest feignRequest) {
                log.error("获取功能价格列表失败，触发熔断", throwable);
                return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "获取功能价格列表失败，服务暂时不可用");
            }

            @Override
            public CommonResult<UserRightResponse> resetUserRights(FeignRequest feignRequest) {
                log.error("重置用户权益失败，触发熔断", throwable);
                return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "重置用户权益失败，服务暂时不可用");
            }

            @Override
            public CommonResult<?> addUserRights(FeignRequest feignRequest) {
                log.error("新增用户权益失败，触发熔断", throwable);
                return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "新增用户权益失败，服务暂时不可用");
            }

            @Override
            public CommonResult<?> syncUserRights(FeignRequest feignRequest) {
                log.error("用户权益同步和转移失败，触发熔断", throwable);
                return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "用户权益同步和转移失败，服务暂时不可用");
            }

            @Override
            public CommonResult<?> deleteUserRights(FeignRequest feignRequest) {
                log.error("删除用户权益失败，触发熔断", throwable);
                return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "删除用户权益失败，服务暂时不可用");
            }

            @Override
            public CommonResult<?> userRightRecord(FeignRequest feignRequest) {
                log.error("用户权益记录失败，触发熔断", throwable);
                return CommonResult.error(GlobalErrorCodeConstants.INTERNAL_ERROR.getCode(), "用户权益记录失败，服务暂时不可用");
            }
        };
    }
} 