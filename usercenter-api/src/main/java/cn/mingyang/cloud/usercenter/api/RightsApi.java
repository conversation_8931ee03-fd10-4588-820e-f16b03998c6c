package cn.mingyang.cloud.usercenter.api;

import cn.mingyang.cloud.framework.common.pojo.CommonResult;
import cn.mingyang.cloud.usercenter.api.fallback.RightsApiFallback;
import cn.mingyang.cloud.usercenter.api.request.FeignRequest;
import cn.mingyang.cloud.usercenter.api.response.UserRightResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 权益接口API
 */
@FeignClient(name = "usercenter-biz", path = "/biz/inner/rights", fallbackFactory = RightsApiFallback.class)
public interface RightsApi {

    /**
     * 获取用户权益
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10006&*")
    CommonResult<UserRightResponse> getUserRight(@RequestBody @Valid FeignRequest feignRequest);

    /**
     * 扣除权益
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10083&*")
    CommonResult<?> expendUserRights(@RequestBody @Valid FeignRequest feignRequest);

    /**
     * 校验用户权益
     *
     * @param feignRequest 命令请求
     * @return 返回信息
     */
    @PostMapping("/c=10086&*")
    CommonResult<?> checkUserRights(@RequestBody @Valid FeignRequest feignRequest);

    /**
     * 获取功能价格列表
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10087&*")
    CommonResult<?> getAllFunctionRights(@RequestBody @Valid FeignRequest feignRequest);

    /**
     * 重置用户权益
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10088&*")
    CommonResult<UserRightResponse> resetUserRights(@RequestBody @Valid FeignRequest feignRequest);

    /**
     * 新增用户权益
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10082&*")
    CommonResult<?> addUserRights(@RequestBody @Valid FeignRequest feignRequest);


    /**
     * 用户权益同步和转移
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10089&*")
    CommonResult<?> syncUserRights(@RequestBody @Valid FeignRequest feignRequest);


    /**
     * 删除用户权益
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10092&*")
    CommonResult<?> deleteUserRights(@RequestBody @Valid FeignRequest feignRequest);


    /**
     * 用户权益记录
     *
     * @param feignRequest 请求
     * @return 返回信息
     */
    @PostMapping("/c=10093&*")
    CommonResult<?> userRightRecord(@RequestBody @Valid FeignRequest feignRequest);
} 