package cn.mingyang.cloud.usercenter.api.response;


import lombok.Data;

/**
 * 产品返回信息
 */
@Data
public class ProductInfoResponse {
    // 主键
    private Integer id;

    // 商品id
    private Integer productid;

    // 商品名称
    private String productname;

    // 商品图像
    private String productimg;

    // 商品数量
    private Integer quantity;

    // 商品时长（单位：天）
    private Integer duration;

    // 客户端显示标题
    private String title;

    // 客户端显示副标题
    private String subtitle;

    // 商品类型：101=vip，102=svip，103=加油包
    private Integer producttype;

    // 原始价格
    private Float orginalprice;

    // 实付价格
    private Float realprice;

    // 首次订阅价格
    private Float firstpromoteprice;

    // 语言
    private String lg;

    // 排序
    private Integer sort;

    // 卖点数量
    private Float sellquantity;

    // 备注
    private String note;

    // 别名
    private String alias;

    // 老商品id
    private Integer orgProductId;

    // 老商品id
    private Integer oldproductid;

    // 是否是自动订阅： 0：不是 1： 是
    private Integer issubscribe;

    // 客户端是否展示： 0：不展示 1：展示
    private Integer isview;
}
