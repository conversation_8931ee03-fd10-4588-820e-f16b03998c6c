package cn.mingyang.cloud.usercenter.api.request;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * Feign参数
 */
@Data
public class FeignRequest {

    /**
     * cmd 请求
     */
    @NotNull(message = "接口信息不能为空：cmd不能为空")
    @Valid
    private FeignCmdRequest cmd;

    /**
     * com 请求
     */
    @NotNull(message = "请求参数不能为空：com不能为空")
    @Valid
    private FeignComRequest com;
}
