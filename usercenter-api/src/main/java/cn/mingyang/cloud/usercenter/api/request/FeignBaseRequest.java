package cn.mingyang.cloud.usercenter.api.request;


import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;


/**
 * Base请求体
 */
@Data
public class FeignBaseRequest {

    /**
     * 渠道号，用于区分发布软件平台
     */
    private String df;

    /**
     * 用户id，在没有登陆的情况下传0
     */
    @NotBlank(message = "用户id：userid不能为空")
    private String userid;

    /**
     * 认证备用值（32位，雪花算法生成，跟用户绑定，暂时不做。）
     */
    private String sid;

    /**
     * 安卓（and/ios/wx）
     */
    @NotBlank(message = "操作系统ID：osid不能为空")
    @Pattern(regexp = "and|ios|mac|win|web|wx|wxios|dy|dyios|qu|ks|ksios|har|wgzh|wghzios",
            message = "osid必须是and、ios、mac、win、web、wx、wxios、dy、dyios、qu、ks、ksios、har、wgzh、wghzios中的一个")
    private String osid;

    /**
     * 语言，“zh_hans”简体中文，“zh_hant”繁体中文，
     * en 英文
     */
    private String lg;

    /**
     * App软件版本如：1.0.8
     */
    private String version;

    /**
     * 硬件信息 品牌 手机型号，如：Xiaomi|star|M2102K1C|14|1080*2297
     */
    private String model;

    /**
     * 安卓ID/IOS随机值/小程序用户 openId
     */
    private String deviceid;

    /**
     * 手机系统软件版本信息
     */
    //@NotBlank(message = "浏览器名称/版本 | 渲染引擎 | 操作系统")
    private String ua;

    /**
     * 安卓广告ID / 苹果广告回传
     */
    private String oaid;

    /**
     * 微信，抖音，快应用unionid
     */
    private String unionid;

    /**
     * android： 华为智能分包的trackerid或者其他平台的智能分包追踪 ID；
     * ios ： idfa
     */
    private String adtrackerid;
}
