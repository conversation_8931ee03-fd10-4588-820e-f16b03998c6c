<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.mingyang.cloud</groupId>
        <artifactId>cloud-framework</artifactId>
        <version>1.0.3-SNAPSHOT</version>
        <relativePath/>
    </parent>


    <packaging>pom</packaging>
    <modules>
        <module>center-common</module>
        <module>resource-biz</module>
        <module>usercenter-biz</module>
        <module>usercenter-api</module>
    </modules>
    <artifactId>usercenter</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>${project.artifactId}</name>
    <description>
        名阳基础资源接口
    </description>

    <dependencies>
        <dependency>
            <groupId>cn.mingyang.cloud</groupId>
            <artifactId>cloud-framework-common</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>
    </dependencies>



    <repositories>
        <repository>
            <id>my-maven</id>
            <name>mingyang maven repositories</name>
            <url>http://*************:18005/nexus/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>

    <!-- 发布到Maven私有仓库 -->
    <distributionManagement>
        <snapshotRepository>
            <id>my-snapshots</id>
            <name>My snapshots</name>
            <url>http://*************:18005/nexus/repository/maven-snapshots/</url>
        </snapshotRepository>
        <repository>
            <id>my-releases</id>
            <name>My releases</name>
            <url>http://*************:18005/nexus/repository/maven-releases/</url>
        </repository>
    </distributionManagement>
</project>